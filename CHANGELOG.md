# 更新日志

所有重要的项目变更都会记录在此文件中。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
并且本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [未发布]

### 重大变更 💥

- **配置接口重构**：`SDKConfig` 接口已完全重构以与更新的 OpenAPI 规范保持一致
  - `game` → `appid`：属性重命名，更准确地表示应用标识符
  - 移除 `baseUrl`：API 基础 URL 现在硬编码为 `https://open.anyigame.cn`
  - `channel` 保持字符串类型不变
  - 这是一个破坏性变更，需要更新现有代码

### 新增功能 ✨

- **配置验证工具**：更新 `SDKConfigValidator` 类以支持新的配置格式
  - `isValidAppId()`：验证应用标识符格式（原 `isValidGameCode()`）
  - `isValidChannelCode()`：验证渠道代码格式
  - `validateConfig()`：验证完整的 SDK 配置
  - `getConfigWithDefaults()`：获取带默认值的完整配置

- **新增配置选项**：
  - `timeout`：API 请求超时时间（默认 10000ms）
  - `maxRetries`：事件上报重试次数（默认 3 次）
  - `batchSize`：事件上报批次大小（默认 10，最大 10）
  - `reportInterval`：事件上报间隔时间（默认 5000ms）

- **游戏配置获取**：新增 `getGameConfig()` 方法支持获取游戏配置信息

### 改进 🔧

- **API 兼容性**：所有 API 调用现在完全符合更新后的 OpenAPI 规范
- **头部参数修正**：修正 API 请求头部参数名称
  - `X-Channel-ID` → `X-Channel-Code`
- **类型安全**：改进 TypeScript 类型定义，提供更好的类型安全性
- **错误处理**：增强配置验证和错误提示

### 修复 🐛

- **数据类型一致性**：修复游戏代码和渠道代码在 API 调用中的数据类型不一致问题
- **测试兼容性**：修复 Jest 配置以支持 ES 模块
- **构建依赖**：添加缺失的 `tslib` 依赖

### 文档 📚

- **迁移指南**：添加详细的配置迁移指南 (`examples/config-migration.md`)
- **API 文档**：更新所有相关的 JSDoc 注释
- **示例代码**：更新示例代码以使用新的配置格式

### 开发体验 🛠️

- **配置验证**：在 SDK 初始化时自动验证配置参数
- **默认值处理**：自动应用配置默认值
- **调试支持**：改进调试模式下的日志输出

## 迁移指南

如果您正在从旧版本升级，请参考 `examples/config-migration.md` 获取详细的迁移指南。

### 快速迁移

```typescript
// 旧配置
const oldConfig = {
  game: '1001',
  channel: '1',
  baseUrl: 'https://api.example.com'
};

// 新配置
const newConfig = {
  appid: '1001',  // game → appid
  channel: '1'    // baseUrl 已移除
};
```

## 版本历史

### [1.0.1] - 2024-12-19

#### 修复 🐛
- **广告关闭类型比较问题**：修复 `type === AdCloseType.COMPLETED` 不相等的问题
  - 在 `BaseNativeBridgeAdapter.triggerAdCloseCallback` 中添加类型转换和验证
  - 支持原生传入 `number` 或 `string` 类型参数
  - 自动将字符串类型（如 `"1"`, `"2"`）转换为对应的枚举值
  - 添加参数有效性验证，过滤无效的输入值
  - 现在广告完成判断逻辑可以正常工作

#### 改进 🔧
- **示例代码增强**：在示例文件中添加详细的调试信息
  - 显示类型比较的详细过程
  - 帮助开发者理解和调试广告关闭逻辑
- **调试模式优化**：类型验证警告只在 debug 模式下输出
  - 避免在生产环境中输出不必要的警告信息
  - 提升生产环境的性能和用户体验

### [1.0.0] - 2024-XX-XX

- 初始版本发布
- 基础 SDK 功能实现
- 用户管理、广告展示、事件上报功能
