openapi: 3.1.0
info:
  title: Game API
  description: API for retrieving game information and configuration.
  version: 1.0.0

paths:
  /api/user/me:
    get:
      summary: 获取当前登录用户信息
      operationId: getCurrentUser
      responses:
        '200':
          description: 成功获取用户信息
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserResponse'
        '401':
          description: 未授权
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
  /api/game/config:
    get:
      summary: 获取游戏配置
      description: |
        根据游戏代码和渠道代码获取游戏配置信息。此接口不需要用户登录。

        **验证逻辑:**
        - 验证游戏代码和渠道代码为数字格式
        - 检查渠道是否启用
        - 验证游戏是否与指定渠道关联
      operationId: getGameConfig
      parameters:
        - name: code
          in: query
          required: true
          schema:
            type: string
            pattern: '^[0-9]+$'
          description: 游戏代码（数字字符串）
          example: "1001"
        - name: X-Channel-Code
          in: header
          required: true
          schema:
            type: string
            pattern: '^[0-9]+$'
          description: 渠道代码（数字字符串）
          example: "1"
      responses:
        '200':
          description: 成功获取游戏配置
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GameConfigResponse'
              example:
                code: 0
                msg: "Success"
                data:
                  id: 1
                  code: "1001"
                  name: "示例游戏"
                  cover: "/uploads/game_covers/game1.jpg"
                  channel_ids: "1,2,3"
                  status: 1
                  latest_version: "1.0.0"
                  created_at: "2024-01-01 10:00:00"
                  updated_at: "2024-01-15 14:30:00"
        '400':
          description: 请求参数无效
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                code: 400
                msg: "Invalid code or X-Channel-Code"
        '404':
          description: 渠道未启用或游戏未找到
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              examples:
                channel_not_enabled:
                  summary: 渠道未启用
                  value:
                    code: 404
                    msg: "Channel not enabled"
                game_not_found:
                  summary: 游戏未找到或未关联渠道
                  value:
                    code: 404
                    msg: "Game not found or not associated with channel"
  /api/adevent/report:
    post:
      summary: 上报激励视频广告事件
      description: |
        批量上报激励视频广告事件数据。支持部分成功处理，即使部分事件处理失败，成功的事件仍会被处理。

        **请求限制:**
        - 最大请求体大小: 1MB
        - 单次请求最大事件数量: 10个
        - 支持部分成功响应

        **验证逻辑:**
        - 验证X-Channel-Code为数字格式
        - 验证渠道代码有效性
        - 验证事件数据格式和数量
      operationId: reportAdEvent
      parameters:
        - name: X-Channel-Code
          in: header
          required: true
          schema:
            type: string
            pattern: '^[0-9]+$'
          description: 渠道代码（数字字符串）
          example: "1"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/AdEvent'
              maxItems: 10
              description: 广告事件数组，最多包含10个事件
      responses:
        '200':
          description: 事件处理完成（可能包含部分失败）
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AdEventResponse'
        '400':
          description: 请求格式错误
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/InvalidChannelCodeResponse'
                  - $ref: '#/components/schemas/InvalidRequestResponse'
                  - $ref: '#/components/schemas/PayloadTooLargeResponse'
                  - $ref: '#/components/schemas/TooManyEventsResponse'
        '401':
          description: 未授权
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '500':
          description: 服务端内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InternalErrorResponse'
  /api/heartbeat/heartbeat:
    post:
      summary: 发送心跳信号
      description: |
        发送心跳信号以维持会话活跃状态。需要用户登录验证。

        **功能说明:**
        - 系统会使用当前用户的登录会话ID（session()->getId()）来查找对应的心跳会话
        - 不需要在请求中传递session_id，会从当前登录会话中自动获取
        - 支持传递自定义数据用于业务扩展
        - 具有频率限制：10次/分钟

        **会话管理:**
        - 心跳会话在用户登录时自动创建（事件驱动）
        - 会话ID使用用户的登录会话ID，确保唯一性和一致性
        - 系统会自动检测和标记超时会话
        - 所有心跳数据永久保存，不会自动删除
      operationId: sendHeartbeat
      requestBody:
        required: false
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/HeartbeatRequest'
            example:
              custom_data:
                level: 5
                score: 1000
                game_mode: "adventure"
      responses:
        '200':
          description: 心跳处理成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HeartbeatResponse'
              example:
                code: 0
                msg: "ok"
                data:
                  session_id: "unique_session_id_123"
                  heartbeat_count: 10
                  duration_seconds: 300
                  last_heartbeat: 1671234867890
                  status: "活跃"
        '400':
          description: 请求参数无效
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                code: 1
                msg: "Invalid custom_data: must be an array"
        '401':
          description: 用户未登录
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                code: 1
                msg: "User not logged in"
        '404':
          description: 未找到有效会话
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                code: 1
                msg: "No valid session found"
        '429':
          description: 请求频率超限
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                code: 1
                msg: "Rate limit exceeded"
        '500':
          description: 服务端内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                code: 1
                msg: "Internal server error"
  /api/player-data/backup:
    post:
      summary: 备份玩家数据
      description: |
        存储任意JSON格式的玩家数据。支持灵活的数据结构，用于客户端全量数据存储。

        **功能特性:**
        - 支持任意JSON数据结构（最大1MB）
        - 使用backup_key进行数据分类（如inventory、progress、settings等）
        - 自动数据完整性校验（SHA-256）
        - 支持软删除，同一backup_key的新备份会替换旧备份
        - 需要用户登录验证

        **数据限制:**
        - 最大数据大小: 1MB
        - backup_key最大长度: 128字符
        - description最大长度: 255字符
        - 频率限制: 30次/分钟
      operationId: backupPlayerData
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PlayerDataBackupRequest'
            example:
              game_id: 123
              backup_key: "inventory"
              backup_data:
                items:
                  - id: 1
                    name: "sword"
                    count: 1
                  - id: 2
                    name: "potion"
                    count: 5
                gold: 1000
                level: 15
              backup_type: 1
              description: "Player inventory backup"
              device_info:
                platform: "web"
                version: "1.0.0"
      responses:
        '200':
          description: 备份创建成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PlayerDataBackupResponse'
              example:
                code: 0
                msg: "Backup created successfully"
                data:
                  backup_id: 456
                  backup_key: "inventory"
                  data_size: 256
                  backup_type: 1
                  backup_type_description: "手动备份"
                  created_at: "2025-06-23 10:30:00"
                  checksum: "sha256_hash_here"
        '400':
          description: 请求参数无效
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              examples:
                invalid_game_id:
                  summary: 无效的游戏ID
                  value:
                    code: 1
                    msg: "Invalid game_id: must be a positive integer"
                invalid_backup_key:
                  summary: 无效的备份键
                  value:
                    code: 1
                    msg: "Invalid backup_key: must be a non-empty string"
                data_too_large:
                  summary: 数据过大
                  value:
                    code: 1
                    msg: "backup_data too large: maximum 1048576 bytes"
        '401':
          description: 用户未登录
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                code: 1
                msg: "User not logged in"
        '429':
          description: 请求频率超限
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                code: 1
                msg: "Rate limit exceeded"
        '500':
          description: 服务端内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                code: 1
                msg: "Internal server error"
  /api/player-data/retrieve:
    get:
      summary: 检索所有玩家备份数据
      description: |
        获取当前用户在指定游戏中的所有备份数据。

        **功能特性:**
        - 返回用户的所有活跃备份数据
        - 自动验证数据完整性
        - 损坏的备份会被自动标记并排除
        - 按backup_key和创建时间排序
        - 需要用户登录验证
        - 频率限制: 60次/分钟
      operationId: retrieveAllPlayerData
      parameters:
        - name: game_id
          in: query
          required: true
          schema:
            type: integer
            minimum: 1
          description: 游戏ID
          example: 123
      responses:
        '200':
          description: 成功获取所有备份数据
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PlayerDataRetrieveAllResponse'
              example:
                code: 0
                msg: "All backup data retrieved successfully"
                data:
                  backups:
                    - backup_key: "inventory"
                      backup_data:
                        items: [{"id": 1, "count": 5}]
                        gold: 1000
                      data_version: 1
                      backup_type: 1
                      backup_type_description: "手动备份"
                      description: "Player inventory backup"
                      created_at: "2025-06-23 10:30:00"
                      updated_at: "2025-06-23 10:30:00"
                      data_size: 256
                    - backup_key: "progress"
                      backup_data:
                        level: 10
                        score: 5000
                      data_version: 1
                      backup_type: 2
                      backup_type_description: "自动备份"
                      description: null
                      created_at: "2025-06-23 11:00:00"
                      updated_at: "2025-06-23 11:00:00"
                      data_size: 128
                  total_count: 2
        '400':
          description: 请求参数无效
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                code: 1
                msg: "Invalid game_id parameter"
        '401':
          description: 用户未登录
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                code: 1
                msg: "User not logged in"
        '404':
          description: 游戏或渠道不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                code: 1
                msg: "Game is not active or does not exist"
        '500':
          description: 服务端内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                code: 1
                msg: "Internal server error"
  /api/player-data/retrieve/{backup_key}:
    get:
      summary: 检索特定备份数据
      description: |
        根据backup_key获取特定的备份数据。

        **功能特性:**
        - 返回指定backup_key的最新备份数据
        - 自动验证数据完整性
        - 如果数据损坏会返回错误并标记备份状态
        - 需要用户登录验证
        - 频率限制: 60次/分钟
      operationId: retrieveSpecificPlayerData
      parameters:
        - name: backup_key
          in: path
          required: true
          schema:
            type: string
            maxLength: 128
          description: 备份数据键名
          example: "inventory"
        - name: game_id
          in: query
          required: true
          schema:
            type: integer
            minimum: 1
          description: 游戏ID
          example: 123
      responses:
        '200':
          description: 成功获取备份数据
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PlayerDataRetrieveResponse'
              example:
                code: 0
                msg: "Backup data retrieved successfully"
                data:
                  backup_key: "inventory"
                  backup_data:
                    items:
                      - id: 1
                        name: "sword"
                        count: 1
                    gold: 1000
                  data_version: 1
                  backup_type: 1
                  backup_type_description: "手动备份"
                  description: "Player inventory backup"
                  created_at: "2025-06-23 10:30:00"
                  updated_at: "2025-06-23 10:30:00"
                  data_size: 256
        '400':
          description: 请求参数无效
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                code: 1
                msg: "Invalid backup_key parameter"
        '401':
          description: 用户未登录
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                code: 1
                msg: "User not logged in"
        '404':
          description: 备份数据未找到
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                code: 1
                msg: "Backup not found for the specified key"
        '409':
          description: 备份数据损坏
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                code: 1
                msg: "Backup data is corrupted"
        '500':
          description: 服务端内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                code: 1
                msg: "Internal server error"
  /api/player-data/stats:
    get:
      summary: 获取备份统计信息
      description: |
        获取当前用户在指定游戏中的备份统计信息。

        **统计内容:**
        - 总备份数量
        - 总数据大小
        - 按备份类型分组的统计
        - 最新备份时间
        - 需要用户登录验证
        - 频率限制: 30次/分钟
      operationId: getPlayerBackupStats
      parameters:
        - name: game_id
          in: query
          required: true
          schema:
            type: integer
            minimum: 1
          description: 游戏ID
          example: 123
      responses:
        '200':
          description: 成功获取统计信息
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PlayerDataStatsResponse'
              example:
                code: 0
                msg: "Backup statistics retrieved successfully"
                data:
                  total_backups: 5
                  total_data_size: 1024
                  backup_types:
                    "1": 3
                    "2": 2
                  latest_backup: "2025-06-23 10:30:00"
        '400':
          description: 请求参数无效
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                code: 1
                msg: "Invalid game_id parameter"
        '401':
          description: 用户未登录
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                code: 1
                msg: "User not logged in"
        '500':
          description: 服务端内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                code: 1
                msg: "Internal server error"

components:
  schemas:
    User:
      type: object
      properties:
        id:
          type: integer
          description: 用户ID
          example: 1
        username:
          type: string
          description: 用户名
          example: torghay
        nickname:
          type: string
          description: 用户昵称
          example: 艾合
        avatar:
          type: string
          description: 用户头像URL
          example: /app/user/default-avatar.png
        email:
          type: string
          description: 用户邮箱
          example: ""
        mobile:
          type: string
          description: 用户手机号
          example: ""
    UserResponse:
      type: object
      properties:
        code:
          type: integer
          description: 响应代码 (200表示成功)
          example: 200
        msg:
          type: string
          description: 响应消息
          example: success
        data:
          $ref: '#/components/schemas/User'
    GameConfig:
      type: object
      properties:
        id:
          type: integer
          description: 游戏ID（主键）
          example: 1
        code:
          type: string
          description: 游戏编码
          example: "1001"
        name:
          type: string
          description: 游戏名称
          example: "示例游戏"
        cover:
          type: string
          description: 游戏封面图片URL
          example: "/uploads/game_covers/game1.jpg"
        channel_ids:
          type: string
          description: 关联的渠道ID列表（逗号分隔）
          example: "1,2,3"
        status:
          type: integer
          description: 游戏状态（1为启用，0为禁用）
          example: 1
        latest_version:
          type: string
          nullable: true
          description: 游戏最新版本号
          example: "1.0.0"
        created_at:
          type: string
          format: date-time
          description: 创建时间
          example: "2024-01-01 10:00:00"
        updated_at:
          type: string
          format: date-time
          description: 更新时间
          example: "2024-01-15 14:30:00"
    GameConfigResponse:
      type: object
      properties:
        code:
          type: integer
          description: 响应代码 (0表示成功)
          example: 0
        msg:
          type: string
          description: 响应消息
          example: Success
        data:
          $ref: '#/components/schemas/GameConfig'
    AdEvent:
      type: object
      required:
        - event_name
        - event_time
        - user_id
        - game_id
        - client_event_id
      properties:
        event_name:
          type: string
          description: 事件名称
          example: ad_impression
        event_time:
          type: integer
          format: int64
          description: 事件发生的客户端时间戳 (毫秒级 Unix Timestamp)
          example: 1678886400123
        user_id:
          type: integer
          description: 用户唯一标识
          example: 12345
        game_id:
          type: integer
          description: 游戏ID
          example: 1001
        channel_id:
          type: integer
          description: 渠道ID
          example: 1
        client_event_id:
          type: string
          description: 客户端生成的唯一事件ID (UUID 或类似机制)
          example: ceid-xxxx-yyyy-zzzz
        ad_creative_id:
          type: string
          nullable: true
          description: 广告素材ID/创意ID
          example: creative_12345
        error_code:
          type: integer
          nullable: true
          description: 错误码 (仅在失败事件中上报)
          example: 1001
        error_message:
          type: string
          nullable: true
          description: 错误信息 (仅在失败事件中上报)
          example: No fill
        reward_name:
          type: string
          nullable: true
          description: 奖励名称 (仅在ad_reward_grant事件中上报)
          example: 金币
        reward_amount:
          type: integer
          nullable: true
          description: 奖励数量 (仅在ad_reward_grant事件中上报)
          example: 100
        session_id:
          type: string
          nullable: true
          description: 会话ID
          example: sess-xxxx-yyyy
        custom_data:
          type: object
          nullable: true
          description: JSON 对象，用于客户端上报一些临时的、非标准化的、特定于业务场景的补充数据
          example:
            level: 5
            custom_tag: test_A
    AdEventResponse:
      type: object
      properties:
        code:
          type: integer
          description: 响应代码 (0表示成功)
          example: 0
        msg:
          type: string
          description: 响应消息
          example: Events processed
        data:
          type: object
          properties:
            processed:
              type: integer
              description: 成功处理的事件数量
              example: 8
            failed:
              type: integer
              description: 处理失败的事件数量
              example: 2
            total:
              type: integer
              description: 总事件数量
              example: 10
            failures:
              type: array
              description: 失败事件的详细信息
              items:
                type: object
                properties:
                  index:
                    type: integer
                    description: 失败事件在原始数组中的索引
                    example: 3
                  reason:
                    type: string
                    description: 失败原因
                    example: Invalid event data format
            request_id:
              type: string
              description: 请求唯一标识符，用于问题追踪
              example: req_64f1a2b3c4d5e
    InvalidChannelCodeResponse:
      type: object
      properties:
        code:
          type: integer
          description: 响应代码
          example: 40001
        msg:
          type: string
          description: 错误消息
          example: "Invalid X-Channel-Code"
        data:
          nullable: true
          example: null
    InvalidRequestResponse:
      type: object
      properties:
        code:
          type: integer
          description: 响应代码
          example: 40001
        msg:
          type: string
          description: 错误消息
          example: "Invalid request body: events array expected"
        data:
          nullable: true
          example: null
    PayloadTooLargeResponse:
      type: object
      properties:
        code:
          type: integer
          description: 响应代码
          example: 40002
        msg:
          type: string
          description: 错误消息
          example: "Request payload too large"
        data:
          nullable: true
          example: null
    TooManyEventsResponse:
      type: object
      properties:
        code:
          type: integer
          description: 响应代码
          example: 40003
        msg:
          type: string
          description: 错误消息
          example: "Too many events in single request"
        data:
          nullable: true
          example: null
    InternalErrorResponse:
      type: object
      properties:
        code:
          type: integer
          description: 响应代码
          example: 50001
        msg:
          type: string
          description: 错误消息
          example: Internal server error, please try again later
        data:
          nullable: true
          example: null
    ApiResponse:
      type: object
      properties:
        code:
          type: integer
          description: 响应代码 (0表示成功，非0表示失败)
          example: 0
        msg:
          type: string
          description: 响应消息
          example: Success
        data:
          nullable: true
          description: 成功时可能包含一些服务端补充信息，通常为null或空对象
    HeartbeatRequest:
      type: object
      properties:
        custom_data:
          type: object
          nullable: true
          description: 自定义数据，用于业务扩展，可包含游戏相关的任意键值对
          example:
            level: 5
            score: 1000
            game_mode: "adventure"
            items_collected: 15
    HeartbeatData:
      type: object
      properties:
        session_id:
          type: string
          description: 会话唯一标识符
          example: "unique_session_id_123"
        heartbeat_count:
          type: integer
          description: 当前会话的心跳次数
          example: 10
        duration_seconds:
          type: integer
          description: 会话持续时长（秒）
          example: 300
        last_heartbeat:
          type: integer
          format: int64
          description: 最后心跳时间戳（毫秒）
          example: 1671234867890
        status:
          type: string
          description: 会话状态文本描述
          enum: ["活跃", "正常结束", "超时结束"]
          example: "活跃"
    HeartbeatResponse:
      type: object
      properties:
        code:
          type: integer
          description: 响应代码 (0表示成功)
          example: 0
        msg:
          type: string
          description: 响应消息
          example: "ok"
        data:
          $ref: '#/components/schemas/HeartbeatData'
    PlayerDataBackupRequest:
      type: object
      required:
        - game_id
        - backup_key
        - backup_data
      properties:
        game_id:
          type: integer
          minimum: 1
          description: 游戏ID
          example: 123
        backup_key:
          type: string
          maxLength: 128
          description: 备份数据键名，用于区分不同类型的备份数据
          example: "inventory"
        backup_data:
          type: object
          description: 要备份的JSON数据，支持任意结构（最大1MB）
          example:
            items:
              - id: 1
                name: "sword"
                count: 1
              - id: 2
                name: "potion"
                count: 5
            gold: 1000
            level: 15
        backup_type:
          type: integer
          enum: [1, 2, 3]
          default: 1
          description: 备份类型（1=手动备份, 2=自动备份, 3=关键节点备份）
          example: 1
        description:
          type: string
          maxLength: 255
          nullable: true
          description: 备份描述
          example: "Player inventory backup"
        device_info:
          type: object
          nullable: true
          description: 设备信息
          example:
            platform: "web"
            version: "1.0.0"
            user_agent: "Mozilla/5.0..."
    PlayerDataBackupResponse:
      type: object
      properties:
        code:
          type: integer
          description: 响应代码 (0表示成功)
          example: 0
        msg:
          type: string
          description: 响应消息
          example: "Backup created successfully"
        data:
          type: object
          properties:
            backup_id:
              type: integer
              description: 备份记录ID
              example: 456
            backup_key:
              type: string
              description: 备份数据键名
              example: "inventory"
            data_size:
              type: integer
              description: 数据大小（字节）
              example: 256
            backup_type:
              type: integer
              description: 备份类型
              example: 1
            backup_type_description:
              type: string
              description: 备份类型描述
              example: "手动备份"
            created_at:
              type: string
              format: date-time
              description: 创建时间
              example: "2025-06-23 10:30:00"
            checksum:
              type: string
              description: 数据校验和（SHA-256）
              example: "sha256_hash_here"
    PlayerDataRetrieveResponse:
      type: object
      properties:
        code:
          type: integer
          description: 响应代码 (0表示成功)
          example: 0
        msg:
          type: string
          description: 响应消息
          example: "Backup data retrieved successfully"
        data:
          $ref: '#/components/schemas/PlayerBackupData'
    PlayerDataRetrieveAllResponse:
      type: object
      properties:
        code:
          type: integer
          description: 响应代码 (0表示成功)
          example: 0
        msg:
          type: string
          description: 响应消息
          example: "All backup data retrieved successfully"
        data:
          type: object
          properties:
            backups:
              type: array
              items:
                $ref: '#/components/schemas/PlayerBackupData'
              description: 备份数据列表
            total_count:
              type: integer
              description: 总备份数量
              example: 2
    PlayerDataStatsResponse:
      type: object
      properties:
        code:
          type: integer
          description: 响应代码 (0表示成功)
          example: 0
        msg:
          type: string
          description: 响应消息
          example: "Backup statistics retrieved successfully"
        data:
          type: object
          properties:
            total_backups:
              type: integer
              description: 总备份数量
              example: 5
            total_data_size:
              type: integer
              description: 总数据大小（字节）
              example: 1024
            backup_types:
              type: object
              additionalProperties:
                type: integer
              description: 按备份类型分组的统计（键为备份类型，值为数量）
              example:
                "1": 3
                "2": 2
            latest_backup:
              type: string
              format: date-time
              nullable: true
              description: 最新备份时间
              example: "2025-06-23 10:30:00"
    PlayerBackupData:
      type: object
      properties:
        backup_key:
          type: string
          description: 备份数据键名
          example: "inventory"
        backup_data:
          type: object
          description: 备份的JSON数据
          example:
            items:
              - id: 1
                name: "sword"
                count: 1
            gold: 1000
        data_version:
          type: integer
          description: 数据版本号
          example: 1
        backup_type:
          type: integer
          description: 备份类型（1=手动, 2=自动, 3=关键节点）
          example: 1
        backup_type_description:
          type: string
          description: 备份类型描述
          example: "手动备份"
        description:
          type: string
          nullable: true
          description: 备份描述
          example: "Player inventory backup"
        created_at:
          type: string
          format: date-time
          description: 创建时间
          example: "2025-06-23 10:30:00"
        updated_at:
          type: string
          format: date-time
          description: 更新时间
          example: "2025-06-23 10:30:00"
        data_size:
          type: integer
          description: 数据大小（字节）
          example: 256
