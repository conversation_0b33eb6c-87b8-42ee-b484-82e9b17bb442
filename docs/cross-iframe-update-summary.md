# 跨 iframe 玩家数据备份功能更新总结

## 更新概述

成功更新了 `CrossIframeSDK.ts` 文件，为跨 iframe 环境添加了完整的玩家数据备份功能支持。现在运行在 iframe 中的游戏可以通过代理无缝访问父窗口中初始化的 SDK 的所有玩家数据备份功能。

## 主要更新内容

### 1. 类型导入更新

```typescript
// 添加了玩家数据备份相关类型的导入
import type { 
  SDKConfig, 
  AdCloseCallback, 
  PlayerDataBackupOptions, 
  PlayerDataBackupResult, 
  PlayerDataRetrieveResult, 
  PlayerDataStats 
} from '../types';
```

### 2. CrossIframeSDKManager 消息处理扩展

#### 新增消息监听器支持

```typescript
// 在 setupMessageListener 方法中添加了新的消息类型处理
if (type === `${this.MESSAGE_PREFIX}backupPlayerData`) {
  this.handleBackupPlayerDataRequest(event);
} else if (type === `${this.MESSAGE_PREFIX}retrievePlayerData`) {
  this.handleRetrievePlayerDataRequest(event);
} else if (type === `${this.MESSAGE_PREFIX}retrieveAllPlayerData`) {
  this.handleRetrieveAllPlayerDataRequest(event);
} else if (type === `${this.MESSAGE_PREFIX}getPlayerDataStats`) {
  this.handleGetPlayerDataStatsRequest(event);
}
```

#### 新增消息处理方法

1. **`handleBackupPlayerDataRequest()`** - 处理备份玩家数据请求
2. **`handleRetrievePlayerDataRequest()`** - 处理检索特定玩家数据请求
3. **`handleRetrieveAllPlayerDataRequest()`** - 处理检索所有玩家数据请求
4. **`handleGetPlayerDataStatsRequest()`** - 处理获取玩家数据统计请求

每个处理方法都包含：
- 参数验证和提取
- SDK 可用性检查
- 异步 API 调用
- 成功/错误响应处理
- 跨 iframe 消息回传

### 3. CrossIframeSDKProxy 类扩展

#### 新增公共方法

```typescript
/**
 * 备份玩家数据
 */
async backupPlayerData(
  backupKey: string,
  data: Record<string, unknown>,
  options?: PlayerDataBackupOptions
): Promise<PlayerDataBackupResult>

/**
 * 检索特定备份数据
 */
async retrievePlayerData(backupKey: string): Promise<PlayerDataRetrieveResult>

/**
 * 检索所有备份数据
 */
async retrieveAllPlayerData(): Promise<PlayerDataRetrieveResult[]>

/**
 * 获取备份统计信息
 */
async getPlayerDataStats(): Promise<PlayerDataStats>
```

#### 智能路由机制

每个方法都实现了智能路由：

```typescript
if (this.topWindowSDK) {
  // 直接调用顶层窗口的 SDK（性能更好）
  return this.topWindowSDK.backupPlayerData(backupKey, data, options);
} else {
  // 使用消息传递（跨域或复杂 iframe 场景）
  return this.sendPlayerDataMessage('backupPlayerData', { backupKey, data, options });
}
```

#### 新增私有方法

```typescript
/**
 * 发送玩家数据相关消息到顶层窗口
 */
private async sendPlayerDataMessage(method: string, params: any): Promise<any>
```

该方法提供：
- 唯一请求 ID 生成
- 消息监听器设置
- 超时处理（10秒）
- 错误处理和清理

#### 构造函数更新

```typescript
// 添加了新方法的绑定
this.backupPlayerData = this.backupPlayerData.bind(this);
this.retrievePlayerData = this.retrievePlayerData.bind(this);
this.retrieveAllPlayerData = this.retrieveAllPlayerData.bind(this);
this.getPlayerDataStats = this.getPlayerDataStats.bind(this);
```

## 技术特性

### 1. 双重访问模式

- **直接访问**: 当可以直接访问父窗口 SDK 时，使用直接调用（性能最优）
- **消息传递**: 当需要跨域或复杂 iframe 通信时，使用 postMessage API

### 2. 完整的错误处理

- SDK 未初始化检测
- 网络请求超时处理
- 跨 iframe 通信错误处理
- 参数验证和类型检查

### 3. 类型安全

- 完整的 TypeScript 类型支持
- 与主 SDK 完全一致的方法签名
- 编译时类型检查

### 4. 性能优化

- 智能路由减少不必要的消息传递
- 请求 ID 机制防止消息混淆
- 合理的超时设置

## 使用示例

### 父窗口初始化

```typescript
import { initInTopWindow } from '@anyigame/ad-sdk';

await initInTopWindow({
  appid: '1001',
  channel: '1',
  debug: true
});
```

### iframe 中使用

```typescript
import { createIframeSDKProxy, BackupType } from '@anyigame/ad-sdk';

const sdkProxy = createIframeSDKProxy();

if (sdkProxy.isAvailable()) {
  // 备份数据
  const result = await sdkProxy.backupPlayerData('inventory', {
    items: [{ id: 1, name: 'sword', count: 1 }],
    gold: 1000
  }, {
    backupType: BackupType.MANUAL,
    description: '库存备份'
  });

  // 检索数据
  const data = await sdkProxy.retrievePlayerData('inventory');
  
  // 获取统计信息
  const stats = await sdkProxy.getPlayerDataStats();
}
```

## 测试文件

### 1. 跨 iframe 测试页面

- **文件**: `test-cross-iframe-player-data.html`
- **功能**: 完整的跨 iframe 测试环境
- **特性**: 
  - 父窗口 SDK 初始化
  - iframe 代理创建和测试
  - 所有玩家数据备份功能测试
  - 实时日志和状态显示

### 2. 更新的主测试页面

- **文件**: `test-player-data.html`
- **更新**: 添加了跨 iframe 测试的链接
- **功能**: 提供测试选项选择

## 文档更新

### 1. 跨 iframe 功能文档

- **文件**: `docs/cross-iframe-player-data.md`
- **内容**: 
  - 架构说明
  - 详细的 API 参考
  - 使用示例
  - 最佳实践
  - 错误处理指南
  - 调试技巧

### 2. 更新总结文档

- **文件**: `docs/cross-iframe-update-summary.md`
- **内容**: 本文档，详细记录了所有更新内容

## 兼容性

### 浏览器支持

- 支持所有现代浏览器
- 依赖 `postMessage` API（IE8+ 支持）
- 完全兼容 webview 环境

### 框架兼容性

- 与现有 SDK 完全兼容
- 不影响非 iframe 环境的使用
- 支持各种游戏引擎（Cocos、Unity WebGL 等）

## 构建验证

✅ **构建成功**: 所有代码通过 TypeScript 编译
✅ **类型检查**: 所有类型定义正确导出
✅ **API 一致性**: 代理方法与主 SDK 方法签名完全一致
✅ **文档完整**: 提供了完整的使用文档和示例

## 总结

此次更新成功为 SDK 添加了完整的跨 iframe 玩家数据备份功能支持，使得运行在 iframe 中的游戏能够无缝访问父窗口的 SDK 功能。更新遵循了现有的代码模式，保持了 API 的一致性，并提供了完整的错误处理和文档支持。

通过智能路由机制，代理能够在不同场景下选择最优的通信方式，确保了功能的可靠性和性能。完整的测试文件和文档使得开发者能够快速理解和使用这些新功能。
