# H5 与原生（Android/iOS）交互文档

## 1. 需求说明

- H5 页面需要与原生 App 进行双向通信。
- H5 主动调用原生方法（如 `showAd`）。
- 原生主动调用 H5 方法（如 `closeAd(type)`）。

---

## 2. H5 调用原生方法

### 2.1 支持的原生方法

| 方法名   | 说明         | 入参 | 备注         |
|----------|--------------|------|--------------|
| showAd   | 显示广告     | 无   | iOS/Android  |

### 2.2 调用方式

H5 通过以下方式调用原生方法，需兼容 Android、iOS 及自定义壳：

```js
function showAd() {
  // Android
  if (window.DsmJSInterface && window.DsmJSInterface.showAd) {
    window.DsmJSInterface.showAd();
    return;
  }
  // iOS
  if (
    window.webkit &&
    window.webkit.messageHandlers &&
    window.webkit.messageHandlers.showAd
  ) {
    window.webkit.messageHandlers.showAd.postMessage({ body: '' });
    return;
  }
  alert('未检测到原生环境');
}
```

### 2.3 Vue 组件示例

```vue
<template>
  <button @click="showAd">显示广告 (showAd)</button>
</template>

<script>
export default {
  methods: {
    showAd() {
      // ...如上 showAd 方法...
    }
  }
}
</script>
```

---

## 3. 原生调用 H5 方法

### 3.1 支持的 H5 方法

| 方法名   | 说明         | 入参 | 备注         |
|----------|--------------|------|--------------|
| closeAd  | 关闭广告回调 | type (1=观看完成, 2=用户取消) | iOS/Android，支持数字或字符串类型  |

### 3.2 H5 实现方式

H5 需在全局挂载 `closeAd` 方法，供原生调用：

```js
// 推荐在 main.js 或入口文件添加
window.closeAd = function(type) {
  console.log('原生调用了 H5 的 closeAd，type:', type);
  // 也可以用 alert 或其他处理
  // alert('closeAd type: ' + type);
};
```

#### 在 Vue 组件中挂载（可选）

```js
export default {
  mounted() {
    window.closeAd = (type) => {
      console.log('原生调用了 H5 的 closeAd，type:', type);
      // 这里可以做更多处理，比如更新页面状态
    };
  }
};
```

### 3.3 原生调用示例

**支持数字类型：**
- **Android**
  `webView.evaluateJavascript("closeAd(1)", null)`
- **iOS**
  `webView.evaluateJavaScript("closeAd(2)")`

**支持字符串类型：**
- **Android**
  `webView.evaluateJavascript("closeAd('1')", null)`
- **iOS**
  `webView.evaluateJavaScript("closeAd('2')")`

**参数说明：**
- `1` 或 `"1"` = 观看完成
- `2` 或 `"2"` = 用户取消
- SDK 会自动处理数字和字符串类型的转换

---

## 4. 交互流程示意

1. H5 通过 `showAd` 方法请求原生展示广告。
2. 广告展示结束后，原生通过 `closeAd(type)` 回调 H5，type 表示关闭原因。

---

## 5. 注意事项

- H5 必须确保 `window.closeAd` 方法在页面加载时已挂载。
- 建议在开发环境下用 alert 或 console.log 验证参数。
- 若有多页面或 SPA，建议在入口文件统一挂载全局方法。

---

