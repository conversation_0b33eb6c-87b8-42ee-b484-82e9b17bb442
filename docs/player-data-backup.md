# 玩家数据备份功能

## 概述

玩家数据备份模块提供了完整的玩家游戏数据备份和恢复功能，支持任意JSON格式的数据存储，具有以下特性：

- 🔒 **安全可靠**: 自动数据完整性校验（SHA-256）
- 📦 **灵活存储**: 支持任意JSON数据结构（最大1MB）
- 🏷️ **分类管理**: 使用backup_key进行数据分类
- 🔄 **多种备份类型**: 手动备份、自动备份、关键节点备份
- 📊 **统计信息**: 提供详细的备份统计数据
- 🌐 **跨平台**: 支持webview/iframe环境

## 快速开始

### 1. 初始化SDK

```typescript
import { init } from '@anyigame/ad-sdk';

await init({
  appid: '1001',
  channel: '1',
  debug: true
});
```

### 2. 备份数据

```typescript
import { backupPlayerData, BackupType } from '@anyigame/ad-sdk';

// 备份玩家库存数据
const inventoryData = {
  items: [
    { id: 1, name: 'sword', count: 1 },
    { id: 2, name: 'potion', count: 5 }
  ],
  gold: 1000,
  level: 15
};

const result = await backupPlayerData('inventory', inventoryData, {
  backupType: BackupType.MANUAL,
  description: '玩家库存备份'
});

console.log('备份成功:', result);
```

### 3. 检索数据

```typescript
import { retrievePlayerData } from '@anyigame/ad-sdk';

// 检索特定备份数据
const data = await retrievePlayerData('inventory');
console.log('检索到的数据:', data.backupData);

// 检索所有备份数据
const allData = await retrieveAllPlayerData();
allData.forEach(backup => {
  console.log(`${backup.backupKey}: ${backup.dataSize} 字节`);
});
```

## API 参考

### 备份类型

```typescript
enum BackupType {
  MANUAL = 'manual',        // 手动备份
  AUTO = 'auto',           // 自动备份
  CHECKPOINT = 'checkpoint' // 关键节点备份
}
```

### 主要方法

#### `backupPlayerData(backupKey, data, options?)`

备份玩家数据到服务器。

**参数:**
- `backupKey: string` - 备份数据键名，用于区分不同类型的数据
- `data: Record<string, unknown>` - 要备份的JSON数据
- `options?: PlayerDataBackupOptions` - 备份选项

**选项:**
```typescript
interface PlayerDataBackupOptions {
  backupType?: BackupType;           // 备份类型，默认为 MANUAL
  description?: string;              // 备份描述
  deviceInfo?: Record<string, unknown>; // 设备信息
}
```

**返回值:**
```typescript
interface PlayerDataBackupResult {
  backupId: number;           // 备份ID
  backupKey: string;          // 备份键名
  dataSize: number;           // 数据大小（字节）
  backupType: BackupType;     // 备份类型
  backupTypeDescription: string; // 备份类型描述
  createdAt: string;          // 创建时间
  checksum: string;           // 数据校验和
}
```

#### `retrievePlayerData(backupKey)`

检索特定的备份数据。

**参数:**
- `backupKey: string` - 要检索的备份数据键名

**返回值:**
```typescript
interface PlayerDataRetrieveResult {
  backupKey: string;                    // 备份键名
  backupData: Record<string, unknown>;  // 备份数据
  dataVersion: number;                  // 数据版本号
  backupType: BackupType;               // 备份类型
  backupTypeDescription: string;        // 备份类型描述
  description?: string;                 // 备份描述
  createdAt: string;                    // 创建时间
  updatedAt: string;                    // 更新时间
  dataSize: number;                     // 数据大小（字节）
}
```

#### `retrieveAllPlayerData()`

检索当前用户的所有备份数据。

**返回值:** `Promise<PlayerDataRetrieveResult[]>`

#### `getPlayerDataStats()`

获取备份统计信息。

**返回值:**
```typescript
interface PlayerDataStats {
  totalBackups: number;                    // 总备份数量
  totalDataSize: number;                   // 总数据大小（字节）
  backupTypes: Record<BackupType, number>; // 按备份类型分组的统计
  latestBackup?: string;                   // 最新备份时间
}
```

## 使用场景

### 1. 游戏进度备份

```typescript
// 备份游戏进度
const progressData = {
  level: 25,
  experience: 15750,
  currentStage: 'forest_temple',
  completedQuests: ['tutorial', 'first_battle'],
  achievements: ['first_kill', 'level_10']
};

await backupPlayerData('progress', progressData, {
  backupType: BackupType.AUTO,
  description: '自动进度备份'
});
```

### 2. 物品库存备份

```typescript
// 备份玩家库存
const inventoryData = {
  items: [
    { id: 1, name: 'sword', count: 1, rarity: 'epic' },
    { id: 2, name: 'potion', count: 5, rarity: 'common' }
  ],
  gold: 1500,
  gems: 50
};

await backupPlayerData('inventory', inventoryData, {
  backupType: BackupType.MANUAL,
  description: '玩家库存备份'
});
```

### 3. 游戏设置备份

```typescript
// 备份游戏设置
const settingsData = {
  audio: { masterVolume: 0.8, musicVolume: 0.6 },
  graphics: { quality: 'high', resolution: '1920x1080' },
  controls: { mouseSensitivity: 0.5 }
};

await backupPlayerData('settings', settingsData, {
  backupType: BackupType.MANUAL,
  description: '游戏设置备份'
});
```

### 4. 关键节点备份

```typescript
// 在重要游戏节点进行备份
const checkpointData = {
  storyProgress: 'chapter_5_complete',
  playerStats: { health: 100, mana: 80 },
  worldState: { bosses_defeated: ['dragon', 'lich'] }
};

await backupPlayerData('checkpoint_chapter5', checkpointData, {
  backupType: BackupType.CHECKPOINT,
  description: '第五章完成检查点'
});
```

## 错误处理

```typescript
try {
  const result = await backupPlayerData('inventory', inventoryData);
  console.log('备份成功:', result);
} catch (error) {
  if (error.message.includes('用户未登录')) {
    // 处理用户未登录的情况
    console.log('请先登录');
  } else if (error.message.includes('数据过大')) {
    // 处理数据过大的情况
    console.log('数据大小超过限制');
  } else {
    // 处理其他错误
    console.error('备份失败:', error.message);
  }
}
```

## 最佳实践

1. **合理使用备份键名**: 使用有意义的键名，如 `inventory`、`progress`、`settings` 等
2. **控制数据大小**: 单次备份数据不要超过1MB
3. **适当的备份频率**: 避免过于频繁的备份操作
4. **错误处理**: 始终包含适当的错误处理逻辑
5. **数据验证**: 在备份前验证数据的完整性和有效性

## 限制说明

- 最大数据大小: 1MB
- backup_key最大长度: 128字符
- description最大长度: 255字符
- 频率限制: 30次/分钟（备份）、60次/分钟（检索）
- 需要用户登录验证

## 测试

项目包含了完整的测试文件：

- `test-player-data.html` - 浏览器测试页面
- `examples/player-data-backup-example.js` - 使用示例代码

运行测试：

1. 构建项目: `bun run build`
2. 在浏览器中打开 `test-player-data.html`
3. 按照页面提示进行测试
