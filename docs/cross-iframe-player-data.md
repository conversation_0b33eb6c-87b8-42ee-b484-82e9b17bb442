# 跨 iframe 玩家数据备份功能

## 概述

跨 iframe 玩家数据备份功能允许运行在 iframe 中的游戏通过代理访问父窗口中初始化的 SDK，实现完整的玩家数据备份和恢复功能。这对于需要在 iframe 环境中运行的游戏特别重要。

## 架构说明

### 工作原理

1. **父窗口初始化**: SDK 在父窗口（顶层窗口）中初始化
2. **iframe 代理**: iframe 中的游戏通过 `CrossIframeSDKProxy` 代理访问父窗口的 SDK
3. **消息传递**: 使用 `postMessage` API 进行跨 iframe 通信
4. **透明访问**: 对于 iframe 中的代码，API 调用方式与直接使用 SDK 完全相同

### 支持的方法

跨 iframe 代理支持以下玩家数据备份方法：

- `backupPlayerData()` - 备份玩家数据
- `retrievePlayerData()` - 检索特定备份数据
- `retrieveAllPlayerData()` - 检索所有备份数据
- `getPlayerDataStats()` - 获取备份统计信息

## 使用方法

### 1. 父窗口初始化

在父窗口中初始化 SDK：

```typescript
// 在父窗口中
import { initInTopWindow } from '@anyigame/ad-sdk';

await initInTopWindow({
  appid: '1001',
  channel: '1',
  debug: true
});
```

### 2. iframe 中创建代理

在 iframe 中创建 SDK 代理：

```typescript
// 在 iframe 中
import { createIframeSDKProxy } from '@anyigame/ad-sdk';

const sdkProxy = createIframeSDKProxy();

// 检查代理是否可用
if (sdkProxy.isAvailable()) {
  console.log('SDK 代理可用');
} else {
  console.log('SDK 代理不可用，请确保父窗口已初始化 SDK');
}
```

### 3. 使用玩家数据备份功能

在 iframe 中使用玩家数据备份功能的方式与直接使用 SDK 完全相同：

```typescript
// 备份数据
const backupResult = await sdkProxy.backupPlayerData('inventory', {
  items: [{ id: 1, name: 'sword', count: 1 }],
  gold: 1000
}, {
  backupType: 'manual',
  description: '库存备份'
});

// 检索数据
const retrieveResult = await sdkProxy.retrievePlayerData('inventory');

// 检索所有数据
const allData = await sdkProxy.retrieveAllPlayerData();

// 获取统计信息
const stats = await sdkProxy.getPlayerDataStats();
```

## API 参考

### CrossIframeSDKProxy 类

#### 玩家数据备份方法

##### `backupPlayerData(backupKey, data, options?)`

备份玩家数据到服务器。

**参数:**
- `backupKey: string` - 备份数据键名
- `data: Record<string, unknown>` - 要备份的数据
- `options?: PlayerDataBackupOptions` - 备份选项

**返回值:** `Promise<PlayerDataBackupResult>`

**示例:**
```typescript
const result = await sdkProxy.backupPlayerData('progress', {
  level: 25,
  experience: 15750,
  currentStage: 'forest_temple'
}, {
  backupType: 'auto',
  description: '自动进度备份'
});
```

##### `retrievePlayerData(backupKey)`

检索特定的备份数据。

**参数:**
- `backupKey: string` - 要检索的备份数据键名

**返回值:** `Promise<PlayerDataRetrieveResult>`

**示例:**
```typescript
const data = await sdkProxy.retrievePlayerData('progress');
console.log('玩家进度:', data.backupData);
```

##### `retrieveAllPlayerData()`

检索当前用户的所有备份数据。

**返回值:** `Promise<PlayerDataRetrieveResult[]>`

**示例:**
```typescript
const allBackups = await sdkProxy.retrieveAllPlayerData();
allBackups.forEach(backup => {
  console.log(`备份: ${backup.backupKey}, 大小: ${backup.dataSize} 字节`);
});
```

##### `getPlayerDataStats()`

获取备份统计信息。

**返回值:** `Promise<PlayerDataStats>`

**示例:**
```typescript
const stats = await sdkProxy.getPlayerDataStats();
console.log(`总备份数: ${stats.totalBackups}`);
console.log(`总大小: ${stats.totalDataSize} 字节`);
```

## 错误处理

### 常见错误及解决方案

#### 1. SDK 未初始化错误

```typescript
try {
  await sdkProxy.backupPlayerData('test', {});
} catch (error) {
  if (error.message.includes('SDK 未初始化')) {
    console.log('请确保父窗口已正确初始化 SDK');
  }
}
```

#### 2. 跨 iframe 通信超时

```typescript
try {
  await sdkProxy.retrievePlayerData('test');
} catch (error) {
  if (error.message.includes('请求超时')) {
    console.log('网络请求超时，请重试');
  }
}
```

#### 3. 代理不可用

```typescript
const sdkProxy = createIframeSDKProxy();
if (!sdkProxy.isAvailable()) {
  console.error('SDK 代理不可用，可能的原因：');
  console.error('1. 父窗口未初始化 SDK');
  console.error('2. 当前不在 iframe 环境中');
  console.error('3. 跨域限制');
}
```

## 最佳实践

### 1. 检查代理可用性

在使用代理之前，始终检查其可用性：

```typescript
const sdkProxy = createIframeSDKProxy();
if (sdkProxy.isAvailable()) {
  // 安全使用代理
  await sdkProxy.backupPlayerData('test', data);
} else {
  // 处理代理不可用的情况
  console.warn('SDK 代理不可用，跳过数据备份');
}
```

### 2. 错误处理

为所有异步操作添加适当的错误处理：

```typescript
try {
  const result = await sdkProxy.backupPlayerData('inventory', inventoryData);
  console.log('备份成功:', result);
} catch (error) {
  console.error('备份失败:', error.message);
  // 可以选择重试或使用本地存储作为备选方案
}
```

### 3. 性能优化

避免频繁的跨 iframe 调用：

```typescript
// 好的做法：批量操作
const allData = await sdkProxy.retrieveAllPlayerData();
const inventoryData = allData.find(backup => backup.backupKey === 'inventory');
const progressData = allData.find(backup => backup.backupKey === 'progress');

// 避免：多次单独调用
// const inventoryData = await sdkProxy.retrievePlayerData('inventory');
// const progressData = await sdkProxy.retrievePlayerData('progress');
```

## 安全考虑

### 1. 跨域安全

- 代理使用 `postMessage` 进行通信，遵循浏览器的同源策略
- 消息传递使用唯一的请求 ID 防止消息混淆
- 设置合理的超时时间防止无限等待

### 2. 数据验证

在 iframe 中接收数据时，建议进行额外的验证：

```typescript
const data = await sdkProxy.retrievePlayerData('inventory');
if (data && data.backupData && typeof data.backupData === 'object') {
  // 数据有效，可以使用
  processInventoryData(data.backupData);
} else {
  console.warn('接收到无效的备份数据');
}
```

## 调试技巧

### 1. 启用调试模式

在父窗口初始化 SDK 时启用调试模式：

```typescript
await initInTopWindow({
  appid: '1001',
  channel: '1',
  debug: true  // 启用调试模式
});
```

### 2. 监控消息传递

在开发过程中，可以监控跨 iframe 消息：

```typescript
window.addEventListener('message', (event) => {
  if (event.data?.type?.startsWith('AdSDK_CrossIframe_')) {
    console.log('跨 iframe 消息:', event.data);
  }
});
```

### 3. 检查 SDK 状态

定期检查 SDK 状态以确保正常工作：

```typescript
const sdkProxy = createIframeSDKProxy();
console.log('代理可用性:', sdkProxy.isAvailable());
console.log('SDK 状态:', sdkProxy.getState());
console.log('调试信息:', sdkProxy.getDebugInfo());
```

## 测试

项目提供了完整的跨 iframe 测试文件：

- `test-cross-iframe-player-data.html` - 跨 iframe 测试页面

### 运行测试

1. 构建项目: `bun run build`
2. 在浏览器中打开 `test-cross-iframe-player-data.html`
3. 按照页面提示进行测试：
   - 在父窗口初始化 SDK
   - 在 iframe 中测试各项玩家数据备份功能

### 测试场景

测试文件涵盖以下场景：

1. **父窗口 SDK 初始化**
2. **iframe 代理创建和可用性检查**
3. **跨 iframe 数据备份**
4. **跨 iframe 数据检索**
5. **跨 iframe 统计信息获取**
6. **错误处理和超时处理**

通过这些测试，可以验证跨 iframe 玩家数据备份功能在各种场景下的正确性和稳定性。
