/**
 * UUID 生成工具
 */

/**
 * 生成 UUID v4
 * @returns UUID 字符串
 */
export function generateUUID(): string {
  // 使用 crypto.randomUUID() 如果可用
  if (typeof crypto !== 'undefined' && crypto.randomUUID) {
    return crypto.randomUUID();
  }

  // 回退到自定义实现
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0;
    const v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

/**
 * 生成客户端事件 ID
 * @returns 客户端事件 ID
 */
export function generateClientEventId(): string {
  return `ceid-${generateUUID()}`;
}

/**
 * 生成会话 ID
 * @returns 会话 ID
 */
export function generateSessionId(): string {
  return `sess-${generateUUID()}`;
}
