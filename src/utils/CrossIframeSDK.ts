/**
 * 跨 iframe SDK 访问模块
 *
 * 支持在顶层窗口初始化 SDK，然后在 iframe 中访问该 SDK 实例
 * 解决 iframe 隔离导致的 window.closeAd 回调无法访问的问题
 */

// ============================================================================
// 导入依赖
// ============================================================================

import type { SDKConfig, AdCloseCallback, PlayerDataBackupOptions, PlayerDataBackupResult, PlayerDataRetrieveResult, PlayerDataStats } from '../types';
import { AdCloseType } from '../types';
import type { NativeBridgeAdapter } from '../adapters/NativeBridgeAdapter';
import { GameSDK } from '../core/SDK';

// ============================================================================
// 常量定义
// ============================================================================

const GLOBAL_SDK_KEY = '__ANYIGAME_AD_SDK__';
const MESSAGE_PREFIX = 'anyigame-ad-sdk:';

// ============================================================================
// 跨 iframe SDK 管理器
// ============================================================================

/**
 * 跨 iframe SDK 管理器
 * 负责在顶层窗口初始化 SDK，并处理来自 iframe 的请求
 */
export class CrossIframeSDKManager {
  private static readonly GLOBAL_SDK_KEY = GLOBAL_SDK_KEY;
  private static readonly MESSAGE_PREFIX = MESSAGE_PREFIX;

  // --------------------------------------------------------------------------
  // 公共方法
  // --------------------------------------------------------------------------

  /**
   * 在顶层窗口初始化 SDK
   * @param config SDK 配置
   * @param adapter 可选的自定义适配器
   */
  static async initInTopWindow(config: SDKConfig, adapter?: NativeBridgeAdapter): Promise<void> {
    // 确保在顶层窗口执行
    if (window !== window.top) {
      throw new Error('initInTopWindow 必须在顶层窗口中调用');
    }

    // 获取或创建 SDK 实例
    const sdk = GameSDK.getInstance();

    // 初始化 SDK
    await sdk.init(config, adapter);

    // 将 SDK 实例存储到全局对象
    (window as any)[this.GLOBAL_SDK_KEY] = {
      sdk,
      config,
      initialized: true,
      version: '1.0.4'
    };

    // 设置消息监听器，处理来自 iframe 的请求
    this.setupMessageListener();

    if (config.debug) {
      console.log('[CrossIframeSDKManager] SDK 已在顶层窗口初始化完成');
    }
  }

  /**
   * 从 iframe 中获取顶层窗口的 SDK 实例
   */
  static getSDKFromTopWindow(): GameSDK | null {
    try {
      // 尝试访问顶层窗口的 SDK 实例
      const topWindow = window.top;
      if (!topWindow) {
        return null;
      }

      const globalSDK = (topWindow as any)[this.GLOBAL_SDK_KEY];
      if (globalSDK && globalSDK.initialized && globalSDK.sdk) {
        return globalSDK.sdk;
      }

      return null;
    } catch (error) {
      // 跨域访问可能会失败
      console.warn('[CrossIframeSDKManager] 无法访问顶层窗口的 SDK 实例:', error);
      return null;
    }
  }

  /**
   * 检查顶层窗口是否已初始化 SDK
   */
  static isSDKInitializedInTopWindow(): boolean {
    try {
      const topWindow = window.top;
      if (!topWindow) {
        return false;
      }

      const globalSDK = (topWindow as any)[this.GLOBAL_SDK_KEY];
      return !!(globalSDK && globalSDK.initialized);
    } catch (error) {
      return false;
    }
  }

  /**
   * 在 iframe 中显示广告（通过消息传递）
   * @param callback 广告关闭回调
   */
  static async showAdFromIframe(callback?: AdCloseCallback): Promise<void> {
    // 首先尝试直接访问顶层窗口的 SDK
    const sdk = this.getSDKFromTopWindow();
    if (sdk) {
      return sdk.showAd(callback);
    }

    // 如果无法直接访问，使用消息传递
    return new Promise((resolve, reject) => {
      if (!window.top) {
        reject(new Error('无法访问顶层窗口'));
        return;
      }

      // 生成唯一的请求 ID
      const requestId = `showAd_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      // 设置回调处理
      const messageHandler = (event: MessageEvent) => {
        if (event.data?.type === `${this.MESSAGE_PREFIX}showAd-response` &&
            event.data?.requestId === requestId) {
          window.removeEventListener('message', messageHandler);

          if (event.data.success) {
            resolve();
          } else {
            reject(new Error(event.data.error || '显示广告失败'));
          }
        } else if (event.data?.type === `${this.MESSAGE_PREFIX}adClose` &&
                   event.data?.requestId === requestId) {
          // 处理广告关闭回调
          if (callback) {
            callback(event.data.closeType);
          }
        }
      };

      window.addEventListener('message', messageHandler);

      // 向顶层窗口发送显示广告请求
      window.top.postMessage({
        type: `${this.MESSAGE_PREFIX}showAd`,
        requestId,
        hasCallback: !!callback
      }, '*');

      // 设置超时
      setTimeout(() => {
        window.removeEventListener('message', messageHandler);
        reject(new Error('显示广告请求超时'));
      }, 10000);
    });
  }

  /**
   * 清理跨 iframe SDK 资源
   */
  static cleanup(): void {
    if (window === window.top) {
      // 在顶层窗口清理全局 SDK 实例
      const globalSDK = (window as any)[this.GLOBAL_SDK_KEY];
      if (globalSDK && globalSDK.sdk) {
        globalSDK.sdk.destroy();
      }
      delete (window as any)[this.GLOBAL_SDK_KEY];
    }
  }

  // --------------------------------------------------------------------------
  // 私有方法 - 消息处理
  // --------------------------------------------------------------------------

  /**
   * 设置消息监听器（在顶层窗口中）
   */
  private static setupMessageListener(): void {
    window.addEventListener('message', (event) => {
      if (!event.data?.type?.startsWith(this.MESSAGE_PREFIX)) {
        return;
      }

      const { type } = event.data;

      if (type === `${this.MESSAGE_PREFIX}showAd`) {
        this.handleShowAdRequest(event);
      } else if (type === `${this.MESSAGE_PREFIX}backupPlayerData`) {
        this.handleBackupPlayerDataRequest(event);
      } else if (type === `${this.MESSAGE_PREFIX}retrievePlayerData`) {
        this.handleRetrievePlayerDataRequest(event);
      } else if (type === `${this.MESSAGE_PREFIX}retrieveAllPlayerData`) {
        this.handleRetrieveAllPlayerDataRequest(event);
      } else if (type === `${this.MESSAGE_PREFIX}getPlayerDataStats`) {
        this.handleGetPlayerDataStatsRequest(event);
      }
    });
  }

  /**
   * 处理来自 iframe 的显示广告请求
   */
  private static async handleShowAdRequest(event: MessageEvent): Promise<void> {
    const { requestId, hasCallback } = event.data;
    const sourceWindow = event.source as Window;

    try {
      const globalSDK = (window as any)[this.GLOBAL_SDK_KEY];
      if (!globalSDK || !globalSDK.sdk) {
        throw new Error('SDK 未初始化');
      }

      const sdk = globalSDK.sdk as GameSDK;

      // 创建回调函数，将结果发送回 iframe
      const callback = hasCallback ? (closeType: AdCloseType) => {
        sourceWindow.postMessage({
          type: `${this.MESSAGE_PREFIX}adClose`,
          requestId,
          closeType
        }, '*');
      } : undefined;

      // 调用 SDK 显示广告
      await sdk.showAd(callback);

      // 发送成功响应
      sourceWindow.postMessage({
        type: `${this.MESSAGE_PREFIX}showAd-response`,
        requestId,
        success: true
      }, '*');

    } catch (error) {
      // 发送错误响应
      sourceWindow.postMessage({
        type: `${this.MESSAGE_PREFIX}showAd-response`,
        requestId,
        success: false,
        error: error instanceof Error ? error.message : String(error)
      }, '*');
    }
  }

  /**
   * 处理来自 iframe 的备份玩家数据请求
   */
  private static async handleBackupPlayerDataRequest(event: MessageEvent): Promise<void> {
    const { requestId, backupKey, data, options } = event.data;
    const sourceWindow = event.source as Window;

    try {
      const globalSDK = (window as any)[this.GLOBAL_SDK_KEY];
      if (!globalSDK || !globalSDK.sdk) {
        throw new Error('SDK 未初始化');
      }

      const sdk = globalSDK.sdk as GameSDK;
      const result = await sdk.backupPlayerData(backupKey, data, options);

      // 发送成功响应
      sourceWindow.postMessage({
        type: `${this.MESSAGE_PREFIX}backupPlayerData-response`,
        requestId,
        success: true,
        result
      }, '*');

    } catch (error) {
      // 发送错误响应
      sourceWindow.postMessage({
        type: `${this.MESSAGE_PREFIX}backupPlayerData-response`,
        requestId,
        success: false,
        error: error instanceof Error ? error.message : String(error)
      }, '*');
    }
  }

  /**
   * 处理来自 iframe 的检索玩家数据请求
   */
  private static async handleRetrievePlayerDataRequest(event: MessageEvent): Promise<void> {
    const { requestId, backupKey } = event.data;
    const sourceWindow = event.source as Window;

    try {
      const globalSDK = (window as any)[this.GLOBAL_SDK_KEY];
      if (!globalSDK || !globalSDK.sdk) {
        throw new Error('SDK 未初始化');
      }

      const sdk = globalSDK.sdk as GameSDK;
      const result = await sdk.retrievePlayerData(backupKey);

      // 发送成功响应
      sourceWindow.postMessage({
        type: `${this.MESSAGE_PREFIX}retrievePlayerData-response`,
        requestId,
        success: true,
        result
      }, '*');

    } catch (error) {
      // 发送错误响应
      sourceWindow.postMessage({
        type: `${this.MESSAGE_PREFIX}retrievePlayerData-response`,
        requestId,
        success: false,
        error: error instanceof Error ? error.message : String(error)
      }, '*');
    }
  }

  /**
   * 处理来自 iframe 的检索所有玩家数据请求
   */
  private static async handleRetrieveAllPlayerDataRequest(event: MessageEvent): Promise<void> {
    const { requestId } = event.data;
    const sourceWindow = event.source as Window;

    try {
      const globalSDK = (window as any)[this.GLOBAL_SDK_KEY];
      if (!globalSDK || !globalSDK.sdk) {
        throw new Error('SDK 未初始化');
      }

      const sdk = globalSDK.sdk as GameSDK;
      const result = await sdk.retrieveAllPlayerData();

      // 发送成功响应
      sourceWindow.postMessage({
        type: `${this.MESSAGE_PREFIX}retrieveAllPlayerData-response`,
        requestId,
        success: true,
        result
      }, '*');

    } catch (error) {
      // 发送错误响应
      sourceWindow.postMessage({
        type: `${this.MESSAGE_PREFIX}retrieveAllPlayerData-response`,
        requestId,
        success: false,
        error: error instanceof Error ? error.message : String(error)
      }, '*');
    }
  }

  /**
   * 处理来自 iframe 的获取玩家数据统计请求
   */
  private static async handleGetPlayerDataStatsRequest(event: MessageEvent): Promise<void> {
    const { requestId } = event.data;
    const sourceWindow = event.source as Window;

    try {
      const globalSDK = (window as any)[this.GLOBAL_SDK_KEY];
      if (!globalSDK || !globalSDK.sdk) {
        throw new Error('SDK 未初始化');
      }

      const sdk = globalSDK.sdk as GameSDK;
      const result = await sdk.getPlayerDataStats();

      // 发送成功响应
      sourceWindow.postMessage({
        type: `${this.MESSAGE_PREFIX}getPlayerDataStats-response`,
        requestId,
        success: true,
        result
      }, '*');

    } catch (error) {
      // 发送错误响应
      sourceWindow.postMessage({
        type: `${this.MESSAGE_PREFIX}getPlayerDataStats-response`,
        requestId,
        success: false,
        error: error instanceof Error ? error.message : String(error)
      }, '*');
    }
  }


}

// ============================================================================
// 跨 iframe SDK 代理类
// ============================================================================

/**
 * 跨 iframe SDK 代理类
 * 在 iframe 中使用，提供与普通 SDK 相同的接口
 */
export class CrossIframeSDKProxy {
  private topWindowSDK: GameSDK | null = null;

  // --------------------------------------------------------------------------
  // 构造函数
  // --------------------------------------------------------------------------

  constructor() {
    this.topWindowSDK = CrossIframeSDKManager.getSDKFromTopWindow();

    // 强制绑定方法到实例，确保在生产环境中可访问
    this.isAvailable = this.isAvailable.bind(this);
    this.showAd = this.showAd.bind(this);
    this.canShowAd = this.canShowAd.bind(this);
    this.closePage = this.closePage.bind(this);
    this.getUserInfo = this.getUserInfo.bind(this);
    this.refreshUserInfo = this.refreshUserInfo.bind(this);
    this.getGameConfig = this.getGameConfig.bind(this);
    this.getState = this.getState.bind(this);
    this.getVersion = this.getVersion.bind(this);
    this.getDebugInfo = this.getDebugInfo.bind(this);
    this.backupPlayerData = this.backupPlayerData.bind(this);
    this.retrievePlayerData = this.retrievePlayerData.bind(this);
    this.retrieveAllPlayerData = this.retrieveAllPlayerData.bind(this);
    this.getPlayerDataStats = this.getPlayerDataStats.bind(this);
  }

  // --------------------------------------------------------------------------
  // 公共方法 - SDK 状态检查
  // --------------------------------------------------------------------------

  /**
   * 检查 SDK 是否可用
   */
  isAvailable(): boolean {
    return CrossIframeSDKManager.isSDKInitializedInTopWindow();
  }

  // --------------------------------------------------------------------------
  // 公共方法 - 广告相关
  // --------------------------------------------------------------------------

  /**
   * 显示广告
   */
  async showAd(callback?: AdCloseCallback): Promise<void> {
    if (this.topWindowSDK) {
      // 直接调用顶层窗口的 SDK
      return this.topWindowSDK.showAd(callback);
    } else {
      // 使用消息传递
      return CrossIframeSDKManager.showAdFromIframe(callback);
    }
  }

  /**
   * 检查是否可以显示广告
   */
  canShowAd(): boolean {
    if (this.topWindowSDK) {
      return this.topWindowSDK.canShowAd();
    }
    return false;
  }

  /**
   * 关闭页面/返回上一页
   *
   * 注意：此功能不依赖SDK初始化状态，直接调用原生接口
   */
  closePage(): void {
    // 直接使用页面导航模块，不需要检查SDK可用性
    const { PageNavigationModule } = require('../modules/PageNavigationModule');
    const { DsmAdapter } = require('../adapters/DsmAdapter');
    const pageNavigation = new PageNavigationModule(new DsmAdapter());
    pageNavigation.closePage({ debug: false });
    pageNavigation.cleanup();
  }

  // --------------------------------------------------------------------------
  // 公共方法 - 用户信息
  // --------------------------------------------------------------------------

  /**
   * 获取用户信息
   */
  getUserInfo() {
    if (this.topWindowSDK) {
      return this.topWindowSDK.getUserInfo();
    }
    return null;
  }

  /**
   * 刷新用户信息
   */
  async refreshUserInfo() {
    if (this.topWindowSDK) {
      return this.topWindowSDK.refreshUserInfo();
    }
    throw new Error('SDK 不可用');
  }

  // --------------------------------------------------------------------------
  // 公共方法 - 游戏配置
  // --------------------------------------------------------------------------

  /**
   * 获取游戏配置
   */
  async getGameConfig() {
    if (this.topWindowSDK) {
      return this.topWindowSDK.getGameConfig();
    }
    throw new Error('SDK 不可用');
  }

  // --------------------------------------------------------------------------
  // 公共方法 - SDK 信息
  // --------------------------------------------------------------------------

  /**
   * 获取 SDK 状态
   */
  getState(): string {
    if (this.topWindowSDK) {
      return this.topWindowSDK.getState();
    }
    return 'unavailable';
  }

  /**
   * 获取 SDK 版本
   */
  getVersion(): string {
    if (this.topWindowSDK) {
      return this.topWindowSDK.getVersion();
    }
    return '1.0.4';
  }

  /**
   * 获取调试信息
   */
  getDebugInfo(): object {
    if (this.topWindowSDK) {
      return this.topWindowSDK.getDebugInfo();
    }
    return { status: 'SDK not available in iframe' };
  }

  // --------------------------------------------------------------------------
  // 公共方法 - 玩家数据备份
  // --------------------------------------------------------------------------

  /**
   * 备份玩家数据
   * @param backupKey 备份数据键名，用于区分不同类型的数据
   * @param data 要备份的数据
   * @param options 备份选项
   * @returns 备份结果
   */
  async backupPlayerData(
    backupKey: string,
    data: Record<string, unknown>,
    options?: PlayerDataBackupOptions
  ): Promise<PlayerDataBackupResult> {
    if (this.topWindowSDK) {
      // 直接调用顶层窗口的 SDK
      return this.topWindowSDK.backupPlayerData(backupKey, data, options);
    } else {
      // 使用消息传递
      return this.sendPlayerDataMessage('backupPlayerData', { backupKey, data, options });
    }
  }

  /**
   * 检索特定备份数据
   * @param backupKey 备份数据键名
   * @returns 备份数据
   */
  async retrievePlayerData(backupKey: string): Promise<PlayerDataRetrieveResult> {
    if (this.topWindowSDK) {
      // 直接调用顶层窗口的 SDK
      return this.topWindowSDK.retrievePlayerData(backupKey);
    } else {
      // 使用消息传递
      return this.sendPlayerDataMessage('retrievePlayerData', { backupKey });
    }
  }

  /**
   * 检索所有备份数据
   * @returns 所有备份数据列表
   */
  async retrieveAllPlayerData(): Promise<PlayerDataRetrieveResult[]> {
    if (this.topWindowSDK) {
      // 直接调用顶层窗口的 SDK
      return this.topWindowSDK.retrieveAllPlayerData();
    } else {
      // 使用消息传递
      return this.sendPlayerDataMessage('retrieveAllPlayerData', {});
    }
  }

  /**
   * 获取备份统计信息
   * @returns 备份统计信息
   */
  async getPlayerDataStats(): Promise<PlayerDataStats> {
    if (this.topWindowSDK) {
      // 直接调用顶层窗口的 SDK
      return this.topWindowSDK.getPlayerDataStats();
    } else {
      // 使用消息传递
      return this.sendPlayerDataMessage('getPlayerDataStats', {});
    }
  }

  // --------------------------------------------------------------------------
  // 私有方法 - 玩家数据消息传递
  // --------------------------------------------------------------------------

  /**
   * 发送玩家数据相关消息到顶层窗口
   */
  private async sendPlayerDataMessage(method: string, params: any): Promise<any> {
    return new Promise((resolve, reject) => {
      if (!window.top) {
        reject(new Error('无法访问顶层窗口'));
        return;
      }

      // 生成唯一的请求 ID
      const requestId = `${method}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      // 设置回调处理
      const messageHandler = (event: MessageEvent) => {
        if (event.data?.type === `${CrossIframeSDKManager['MESSAGE_PREFIX']}${method}-response` &&
            event.data?.requestId === requestId) {
          window.removeEventListener('message', messageHandler);

          if (event.data.success) {
            resolve(event.data.result);
          } else {
            reject(new Error(event.data.error || `${method} 失败`));
          }
        }
      };

      window.addEventListener('message', messageHandler);

      // 向顶层窗口发送请求
      window.top.postMessage({
        type: `${CrossIframeSDKManager['MESSAGE_PREFIX']}${method}`,
        requestId,
        ...params
      }, '*');

      // 设置超时
      setTimeout(() => {
        window.removeEventListener('message', messageHandler);
        reject(new Error(`${method} 请求超时`));
      }, 10000);
    });
  }
}
