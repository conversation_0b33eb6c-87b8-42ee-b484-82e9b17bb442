/**
 * 本地存储工具类
 */

import type { CachedUserInfo } from '../types';

/**
 * 存储键名常量
 */
export const STORAGE_KEYS = {
  USER_INFO: 'ad_sdk_user_info',
  SESSION_ID: 'ad_sdk_session_id',
} as const;

/**
 * 本地存储工具类
 */
export class StorageUtil {
  /**
   * 保存用户信息到本地存储
   */
  static saveUserInfo(userInfo: CachedUserInfo): void {
    try {
      const data = JSON.stringify(userInfo);
      localStorage.setItem(STORAGE_KEYS.USER_INFO, data);
    } catch (error) {
      console.warn('Failed to save user info to localStorage:', error);
    }
  }

  /**
   * 从本地存储获取用户信息
   */
  static getUserInfo(): CachedUserInfo | null {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.USER_INFO);
      if (!data) {
        return null;
      }

      const userInfo: CachedUserInfo = JSON.parse(data);
      
      // 检查是否过期
      const now = Date.now();
      if (now > userInfo.cachedAt + userInfo.expiresIn) {
        // 已过期，删除缓存
        this.removeUserInfo();
        return null;
      }

      return userInfo;
    } catch (error) {
      console.warn('Failed to get user info from localStorage:', error);
      return null;
    }
  }

  /**
   * 删除用户信息缓存
   */
  static removeUserInfo(): void {
    try {
      localStorage.removeItem(STORAGE_KEYS.USER_INFO);
    } catch (error) {
      console.warn('Failed to remove user info from localStorage:', error);
    }
  }

  /**
   * 保存会话 ID
   */
  static saveSessionId(sessionId: string): void {
    try {
      localStorage.setItem(STORAGE_KEYS.SESSION_ID, sessionId);
    } catch (error) {
      console.warn('Failed to save session ID to localStorage:', error);
    }
  }

  /**
   * 获取会话 ID
   */
  static getSessionId(): string | null {
    try {
      return localStorage.getItem(STORAGE_KEYS.SESSION_ID);
    } catch (error) {
      console.warn('Failed to get session ID from localStorage:', error);
      return null;
    }
  }

  /**
   * 删除会话 ID
   */
  static removeSessionId(): void {
    try {
      localStorage.removeItem(STORAGE_KEYS.SESSION_ID);
    } catch (error) {
      console.warn('Failed to remove session ID from localStorage:', error);
    }
  }

  /**
   * 清除所有 SDK 相关的存储数据
   */
  static clearAll(): void {
    this.removeUserInfo();
    this.removeSessionId();
  }
}
