/**
 * 页面导航工具函数
 * 
 * 提供独立的页面关闭功能，不依赖SDK初始化状态
 */

// ============================================================================
// 类型定义
// ============================================================================

/**
 * 页面关闭选项
 */
export interface PageCloseOptions {
  /** 是否启用调试日志 */
  debug?: boolean;
}

// ============================================================================
// 工具函数
// ============================================================================

/**
 * 关闭页面/返回上一页
 * 
 * 此函数直接调用原生接口，不依赖SDK初始化状态
 * 支持以下原生接口：
 * - iOS: webkit.messageHandlers.returnHome
 * - Android: DsmJSInterface.goPreviewPage
 * 
 * @param options 页面关闭选项
 * @throws {Error} 当未检测到支持的原生环境时抛出错误
 * 
 * @example
 * ```typescript
 * import { closePage } from '@anyigame/ad-sdk';
 * 
 * try {
 *   closePage({ debug: true });
 *   console.log('页面关闭调用成功');
 * } catch (error) {
 *   console.error('关闭页面失败:', error);
 * }
 * ```
 */
export function closePage(options: PageCloseOptions = {}): void {
  const { debug = false } = options;

  if (debug) {
    console.log('[PageNavigation] 开始执行页面关闭操作');
  }

  // 检查原生环境并调用相应接口
  const windowObj = window as any;

  // iOS webkit.messageHandlers.returnHome 优先级更高
  if (windowObj.webkit && 
      windowObj.webkit.messageHandlers && 
      windowObj.webkit.messageHandlers.returnHome) {
    
    if (debug) {
      console.log('[PageNavigation] 使用 webkit.messageHandlers.returnHome 接口关闭');
    }
    
    windowObj.webkit.messageHandlers.returnHome.postMessage({});
    return;
  }

  // Android DsmJSInterface.goPreviewPage 作为备选
  if (windowObj.DsmJSInterface && windowObj.DsmJSInterface.goPreviewPage) {
    if (debug) {
      console.log('[PageNavigation] 使用 DsmJSInterface.goPreviewPage 接口关闭');
    }
    
    windowObj.DsmJSInterface.goPreviewPage();
    return;
  }

  // 如果都不支持，抛出错误
  const errorMessage = '未检测到支持的原生环境，无法关闭页面';
  if (debug) {
    console.error('[PageNavigation]', errorMessage);
    console.log('[PageNavigation] 原生环境检查:', {
      hasWebkit: !!windowObj.webkit,
      hasWebkitMessageHandlers: !!(windowObj.webkit && windowObj.webkit.messageHandlers),
      hasReturnHome: !!(windowObj.webkit && windowObj.webkit.messageHandlers && windowObj.webkit.messageHandlers.returnHome),
      hasDsmJSInterface: !!windowObj.DsmJSInterface,
      hasGoPreviewPage: !!(windowObj.DsmJSInterface && windowObj.DsmJSInterface.goPreviewPage)
    });
  }
  
  throw new Error(errorMessage);
}

/**
 * 检查是否支持页面关闭功能
 * 
 * @returns 如果支持页面关闭功能返回 true，否则返回 false
 * 
 * @example
 * ```typescript
 * import { canClosePage } from '@anyigame/ad-sdk';
 * 
 * if (canClosePage()) {
 *   // 显示关闭按钮
 *   showCloseButton();
 * } else {
 *   // 隐藏关闭按钮或显示其他导航方式
 *   hideCloseButton();
 * }
 * ```
 */
export function canClosePage(): boolean {
  const windowObj = window as any;
  
  // 检查 iOS webkit.messageHandlers.returnHome
  const hasIOSReturnHome = !!(windowObj.webkit && 
                             windowObj.webkit.messageHandlers && 
                             windowObj.webkit.messageHandlers.returnHome);
  
  // 检查 Android DsmJSInterface.goPreviewPage
  const hasAndroidGoPreview = !!(windowObj.DsmJSInterface && 
                                windowObj.DsmJSInterface.goPreviewPage);
  
  return hasIOSReturnHome || hasAndroidGoPreview;
}

/**
 * 获取当前支持的页面关闭接口信息
 * 
 * @returns 页面关闭接口支持信息
 * 
 * @example
 * ```typescript
 * import { getPageCloseSupport } from '@anyigame/ad-sdk';
 * 
 * const support = getPageCloseSupport();
 * console.log('页面关闭支持情况:', support);
 * ```
 */
export function getPageCloseSupport(): {
  supported: boolean;
  ios: boolean;
  android: boolean;
  preferredInterface: 'ios' | 'android' | null;
} {
  const windowObj = window as any;
  
  const ios = !!(windowObj.webkit && 
                windowObj.webkit.messageHandlers && 
                windowObj.webkit.messageHandlers.returnHome);
  
  const android = !!(windowObj.DsmJSInterface && 
                    windowObj.DsmJSInterface.goPreviewPage);
  
  const supported = ios || android;
  
  // iOS 接口优先级更高
  const preferredInterface = ios ? 'ios' : (android ? 'android' : null);
  
  return {
    supported,
    ios,
    android,
    preferredInterface
  };
}
