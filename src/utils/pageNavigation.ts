/**
 * 页面导航工具函数
 *
 * 提供便捷的页面导航功能接口
 */

// ============================================================================
// 导入依赖
// ============================================================================

import {
  PageNavigationModule,
  type PageCloseOptions,
  type PageCloseSupport
} from '../modules/PageNavigationModule';

// ============================================================================
// 默认实例和便捷函数
// ============================================================================

/**
 * 默认页面导航模块实例
 */
const defaultPageNavigation = new PageNavigationModule();

/**
 * 关闭页面/返回上一页 (便捷函数)
 *
 * @param options 页面关闭选项
 * @throws {Error} 当未检测到支持的原生环境时抛出错误
 *
 * @example
 * ```typescript
 * import { closePage } from '@anyigame/ad-sdk';
 *
 * try {
 *   closePage({ debug: true });
 *   console.log('页面关闭调用成功');
 * } catch (error) {
 *   console.error('关闭页面失败:', error);
 * }
 * ```
 */
export function closePage(options: PageCloseOptions = {}): void {
  defaultPageNavigation.closePage(options);
}

/**
 * 检查是否支持页面关闭功能 (便捷函数)
 *
 * @returns 如果支持页面关闭功能返回 true，否则返回 false
 *
 * @example
 * ```typescript
 * import { canClosePage } from '@anyigame/ad-sdk';
 *
 * if (canClosePage()) {
 *   // 显示关闭按钮
 *   showCloseButton();
 * } else {
 *   // 隐藏关闭按钮或显示其他导航方式
 *   hideCloseButton();
 * }
 * ```
 */
export function canClosePage(): boolean {
  return defaultPageNavigation.canClosePage();
}

/**
 * 获取页面关闭支持信息 (便捷函数)
 *
 * @returns 页面关闭接口支持信息
 *
 * @example
 * ```typescript
 * import { getPageCloseSupport } from '@anyigame/ad-sdk';
 *
 * const support = getPageCloseSupport();
 * console.log('页面关闭支持情况:', support);
 * ```
 */
export function getPageCloseSupport(): PageCloseSupport {
  return defaultPageNavigation.getPageCloseSupport();
}

// ============================================================================
// 导出类型和模块
// ============================================================================

export { PageNavigationModule, type PageCloseOptions, type PageCloseSupport };