/**
 * 页面导航工具函数
 *
 * 提供独立的页面关闭功能，不依赖SDK初始化状态
 * 通过 adapter 模式调用原生接口，保持架构一致性
 */

// ============================================================================
// 导入依赖
// ============================================================================

import { DsmAdapter } from '../adapters/DsmAdapter';

// ============================================================================
// 类型定义
// ============================================================================

/**
 * 页面关闭选项
 */
export interface PageCloseOptions {
  /** 是否启用调试日志 */
  debug?: boolean;
}

// ============================================================================
// 工具函数
// ============================================================================

/**
 * 创建临时 adapter 实例
 */
function createTempAdapter(debug: boolean = false): DsmAdapter {
  const adapter = new DsmAdapter();
  adapter.setDebugMode(debug);
  return adapter;
}

/**
 * 关闭页面/返回上一页
 *
 * 这是一个独立的工具函数，不依赖SDK初始化状态。
 * 通过 DsmAdapter 调用原生接口，保持架构一致性。
 * 支持以下原生接口：
 * - iOS: webkit.messageHandlers.returnHome
 * - Android: DsmJSInterface.goPreviewPage
 *
 * @param options 页面关闭选项
 * @throws {Error} 当未检测到支持的原生环境时抛出错误
 *
 * @example
 * ```typescript
 * import { closePage } from '@anyigame/ad-sdk';
 *
 * try {
 *   closePage({ debug: true });
 *   console.log('页面关闭调用成功');
 * } catch (error) {
 *   console.error('关闭页面失败:', error);
 * }
 * ```
 */
export function closePage(options: PageCloseOptions = {}): void {
  const { debug = false } = options;

  // 创建临时 adapter 实例
  const adapter = createTempAdapter(debug);

  try {
    // 通过 adapter 调用页面关闭功能
    adapter.closePage();
  } finally {
    // 清理临时 adapter
    adapter.cleanup();
  }
}

/**
 * 检查是否支持页面关闭功能
 *
 * @returns 如果支持页面关闭功能返回 true，否则返回 false
 *
 * @example
 * ```typescript
 * import { canClosePage } from '@anyigame/ad-sdk';
 *
 * if (canClosePage()) {
 *   // 显示关闭按钮
 *   showCloseButton();
 * } else {
 *   // 隐藏关闭按钮或显示其他导航方式
 *   hideCloseButton();
 * }
 * ```
 */
export function canClosePage(): boolean {
  // 创建临时 adapter 实例来检查环境
  const adapter = createTempAdapter(false);

  try {
    // 检查是否有页面关闭支持
    return !!(window as any).webkit?.messageHandlers?.returnHome ||
           !!(window as any).DsmJSInterface?.goPreviewPage;
  } finally {
    adapter.cleanup();
  }
}

/**
 * 获取当前支持的页面关闭接口信息
 * 
 * @returns 页面关闭接口支持信息
 * 
 * @example
 * ```typescript
 * import { getPageCloseSupport } from '@anyigame/ad-sdk';
 * 
 * const support = getPageCloseSupport();
 * console.log('页面关闭支持情况:', support);
 * ```
 */
export function getPageCloseSupport(): {
  supported: boolean;
  ios: boolean;
  android: boolean;
  preferredInterface: 'ios' | 'android' | null;
} {
  const windowObj = window as any;
  
  const ios = !!(windowObj.webkit && 
                windowObj.webkit.messageHandlers && 
                windowObj.webkit.messageHandlers.returnHome);
  
  const android = !!(windowObj.DsmJSInterface && 
                    windowObj.DsmJSInterface.goPreviewPage);
  
  const supported = ios || android;
  
  // iOS 接口优先级更高
  const preferredInterface = ios ? 'ios' : (android ? 'android' : null);
  
  return {
    supported,
    ios,
    android,
    preferredInterface
  };
}
