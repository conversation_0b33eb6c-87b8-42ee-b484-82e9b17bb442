// This file is auto-generated by @hey-api/openapi-ts

import type { Options as ClientOptions, TDataShape, Client } from '@hey-api/client-fetch';
import type { GetCurrentUserData, GetCurrentUserResponses, GetCurrentUserErrors, GetGameConfigData, GetGameConfigResponses, GetGameConfigErrors, ReportAdEventData, ReportAdEventResponses, ReportAdEventErrors, SendHeartbeatData, SendHeartbeatResponses, SendHeartbeatErrors, BackupPlayerDataData, BackupPlayerDataResponses, BackupPlayerDataErrors, RetrieveAllPlayerDataData, RetrieveAllPlayerDataResponses, RetrieveAllPlayerDataErrors, RetrieveSpecificPlayerDataData, RetrieveSpecificPlayerDataResponses, RetrieveSpecificPlayerDataErrors, GetPlayerBackupStatsData, GetPlayerBackupStatsResponses, GetPlayerBackupStatsErrors } from './types.gen';
import { client as _heyApiClient } from './client.gen';

export type Options<TData extends TDataShape = TDataShape, ThrowOnError extends boolean = boolean> = ClientOptions<TData, ThrowOnError> & {
    /**
     * You can provide a client instance returned by `createClient()` instead of
     * individual options. This might be also useful if you want to implement a
     * custom client.
     */
    client?: Client;
    /**
     * You can pass arbitrary values through the `meta` object. This can be
     * used to access values that aren't defined as part of the SDK function.
     */
    meta?: Record<string, unknown>;
};

/**
 * 获取当前登录用户信息
 */
export const getCurrentUser = <ThrowOnError extends boolean = false>(options?: Options<GetCurrentUserData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetCurrentUserResponses, GetCurrentUserErrors, ThrowOnError>({
        url: '/api/user/me',
        ...options
    });
};

/**
 * 获取游戏配置
 * 根据游戏代码和渠道代码获取游戏配置信息。此接口不需要用户登录。
 *
 * **验证逻辑:**
 * - 验证游戏代码和渠道代码为数字格式
 * - 检查渠道是否启用
 * - 验证游戏是否与指定渠道关联
 *
 */
export const getGameConfig = <ThrowOnError extends boolean = false>(options: Options<GetGameConfigData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetGameConfigResponses, GetGameConfigErrors, ThrowOnError>({
        url: '/api/game/config',
        ...options
    });
};

/**
 * 上报激励视频广告事件
 * 批量上报激励视频广告事件数据。支持部分成功处理，即使部分事件处理失败，成功的事件仍会被处理。
 *
 * **请求限制:**
 * - 最大请求体大小: 1MB
 * - 单次请求最大事件数量: 10个
 * - 支持部分成功响应
 *
 * **验证逻辑:**
 * - 验证X-Channel-Code为数字格式
 * - 验证渠道代码有效性
 * - 验证事件数据格式和数量
 *
 */
export const reportAdEvent = <ThrowOnError extends boolean = false>(options: Options<ReportAdEventData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<ReportAdEventResponses, ReportAdEventErrors, ThrowOnError>({
        url: '/api/adevent/report',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * 发送心跳信号
 * 发送心跳信号以维持会话活跃状态。需要用户登录验证。
 *
 * **功能说明:**
 * - 系统会使用当前用户的登录会话ID（session()->getId()）来查找对应的心跳会话
 * - 不需要在请求中传递session_id，会从当前登录会话中自动获取
 * - 支持传递自定义数据用于业务扩展
 * - 具有频率限制：10次/分钟
 *
 * **会话管理:**
 * - 心跳会话在用户登录时自动创建（事件驱动）
 * - 会话ID使用用户的登录会话ID，确保唯一性和一致性
 * - 系统会自动检测和标记超时会话
 * - 所有心跳数据永久保存，不会自动删除
 *
 */
export const sendHeartbeat = <ThrowOnError extends boolean = false>(options?: Options<SendHeartbeatData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<SendHeartbeatResponses, SendHeartbeatErrors, ThrowOnError>({
        url: '/api/heartbeat/heartbeat',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * 备份玩家数据
 * 存储任意JSON格式的玩家数据。支持灵活的数据结构，用于客户端全量数据存储。
 *
 * **功能特性:**
 * - 支持任意JSON数据结构（最大1MB）
 * - 使用backup_key进行数据分类（如inventory、progress、settings等）
 * - 自动数据完整性校验（SHA-256）
 * - 支持软删除，同一backup_key的新备份会替换旧备份
 * - 需要用户登录验证
 *
 * **数据限制:**
 * - 最大数据大小: 1MB
 * - backup_key最大长度: 128字符
 * - description最大长度: 255字符
 * - 频率限制: 30次/分钟
 *
 */
export const backupPlayerData = <ThrowOnError extends boolean = false>(options: Options<BackupPlayerDataData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<BackupPlayerDataResponses, BackupPlayerDataErrors, ThrowOnError>({
        url: '/api/player-data/backup',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * 检索所有玩家备份数据
 * 获取当前用户在指定游戏中的所有备份数据。
 *
 * **功能特性:**
 * - 返回用户的所有活跃备份数据
 * - 自动验证数据完整性
 * - 损坏的备份会被自动标记并排除
 * - 按backup_key和创建时间排序
 * - 需要用户登录验证
 * - 频率限制: 60次/分钟
 *
 */
export const retrieveAllPlayerData = <ThrowOnError extends boolean = false>(options: Options<RetrieveAllPlayerDataData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<RetrieveAllPlayerDataResponses, RetrieveAllPlayerDataErrors, ThrowOnError>({
        url: '/api/player-data/retrieve',
        ...options
    });
};

/**
 * 检索特定备份数据
 * 根据backup_key获取特定的备份数据。
 *
 * **功能特性:**
 * - 返回指定backup_key的最新备份数据
 * - 自动验证数据完整性
 * - 如果数据损坏会返回错误并标记备份状态
 * - 需要用户登录验证
 * - 频率限制: 60次/分钟
 *
 */
export const retrieveSpecificPlayerData = <ThrowOnError extends boolean = false>(options: Options<RetrieveSpecificPlayerDataData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<RetrieveSpecificPlayerDataResponses, RetrieveSpecificPlayerDataErrors, ThrowOnError>({
        url: '/api/player-data/retrieve/{backup_key}',
        ...options
    });
};

/**
 * 获取备份统计信息
 * 获取当前用户在指定游戏中的备份统计信息。
 *
 * **统计内容:**
 * - 总备份数量
 * - 总数据大小
 * - 按备份类型分组的统计
 * - 最新备份时间
 * - 需要用户登录验证
 * - 频率限制: 30次/分钟
 *
 */
export const getPlayerBackupStats = <ThrowOnError extends boolean = false>(options: Options<GetPlayerBackupStatsData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetPlayerBackupStatsResponses, GetPlayerBackupStatsErrors, ThrowOnError>({
        url: '/api/player-data/stats',
        ...options
    });
};