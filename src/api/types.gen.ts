// This file is auto-generated by @hey-api/openapi-ts

export type User = {
    /**
     * 用户ID
     */
    id?: number;
    /**
     * 用户名
     */
    username?: string;
    /**
     * 用户昵称
     */
    nickname?: string;
    /**
     * 用户头像URL
     */
    avatar?: string;
    /**
     * 用户邮箱
     */
    email?: string;
    /**
     * 用户手机号
     */
    mobile?: string;
};

export type UserResponse = {
    /**
     * 响应代码 (200表示成功)
     */
    code?: number;
    /**
     * 响应消息
     */
    msg?: string;
    data?: User;
};

export type GameConfig = {
    /**
     * 游戏ID（主键）
     */
    id?: number;
    /**
     * 游戏编码
     */
    code?: string;
    /**
     * 游戏名称
     */
    name?: string;
    /**
     * 游戏封面图片URL
     */
    cover?: string;
    /**
     * 关联的渠道ID列表（逗号分隔）
     */
    channel_ids?: string;
    /**
     * 游戏状态（1为启用，0为禁用）
     */
    status?: number;
    /**
     * 游戏最新版本号
     */
    latest_version?: string;
    /**
     * 创建时间
     */
    created_at?: string;
    /**
     * 更新时间
     */
    updated_at?: string;
};

export type GameConfigResponse = {
    /**
     * 响应代码 (0表示成功)
     */
    code?: number;
    /**
     * 响应消息
     */
    msg?: string;
    data?: GameConfig;
};

export type AdEvent = {
    /**
     * 事件名称
     */
    event_name: string;
    /**
     * 事件发生的客户端时间戳 (毫秒级 Unix Timestamp)
     */
    event_time: number;
    /**
     * 用户唯一标识
     */
    user_id: number;
    /**
     * 游戏ID
     */
    game_id: number;
    /**
     * 渠道ID
     */
    channel_id?: number;
    /**
     * 客户端生成的唯一事件ID (UUID 或类似机制)
     */
    client_event_id: string;
    /**
     * 广告素材ID/创意ID
     */
    ad_creative_id?: string;
    /**
     * 错误码 (仅在失败事件中上报)
     */
    error_code?: number;
    /**
     * 错误信息 (仅在失败事件中上报)
     */
    error_message?: string;
    /**
     * 奖励名称 (仅在ad_reward_grant事件中上报)
     */
    reward_name?: string;
    /**
     * 奖励数量 (仅在ad_reward_grant事件中上报)
     */
    reward_amount?: number;
    /**
     * 会话ID
     */
    session_id?: string;
    /**
     * JSON 对象，用于客户端上报一些临时的、非标准化的、特定于业务场景的补充数据
     */
    custom_data?: {
        [key: string]: unknown;
    };
};

export type AdEventResponse = {
    /**
     * 响应代码 (0表示成功)
     */
    code?: number;
    /**
     * 响应消息
     */
    msg?: string;
    data?: {
        /**
         * 成功处理的事件数量
         */
        processed?: number;
        /**
         * 处理失败的事件数量
         */
        failed?: number;
        /**
         * 总事件数量
         */
        total?: number;
        /**
         * 失败事件的详细信息
         */
        failures?: Array<{
            /**
             * 失败事件在原始数组中的索引
             */
            index?: number;
            /**
             * 失败原因
             */
            reason?: string;
        }>;
        /**
         * 请求唯一标识符，用于问题追踪
         */
        request_id?: string;
    };
};

export type InvalidChannelCodeResponse = {
    /**
     * 响应代码
     */
    code?: number;
    /**
     * 错误消息
     */
    msg?: string;
    data?: unknown;
};

export type InvalidRequestResponse = {
    /**
     * 响应代码
     */
    code?: number;
    /**
     * 错误消息
     */
    msg?: string;
    data?: unknown;
};

export type PayloadTooLargeResponse = {
    /**
     * 响应代码
     */
    code?: number;
    /**
     * 错误消息
     */
    msg?: string;
    data?: unknown;
};

export type TooManyEventsResponse = {
    /**
     * 响应代码
     */
    code?: number;
    /**
     * 错误消息
     */
    msg?: string;
    data?: unknown;
};

export type InternalErrorResponse = {
    /**
     * 响应代码
     */
    code?: number;
    /**
     * 错误消息
     */
    msg?: string;
    data?: unknown;
};

export type ApiResponse = {
    /**
     * 响应代码 (0表示成功，非0表示失败)
     */
    code?: number;
    /**
     * 响应消息
     */
    msg?: string;
    /**
     * 成功时可能包含一些服务端补充信息，通常为null或空对象
     */
    data?: unknown;
};

export type HeartbeatRequest = {
    /**
     * 自定义数据，用于业务扩展，可包含游戏相关的任意键值对
     */
    custom_data?: {
        [key: string]: unknown;
    };
};

export type HeartbeatData = {
    /**
     * 会话唯一标识符
     */
    session_id?: string;
    /**
     * 当前会话的心跳次数
     */
    heartbeat_count?: number;
    /**
     * 会话持续时长（秒）
     */
    duration_seconds?: number;
    /**
     * 最后心跳时间戳（毫秒）
     */
    last_heartbeat?: number;
    /**
     * 会话状态文本描述
     */
    status?: '活跃' | '正常结束' | '超时结束';
};

export type HeartbeatResponse = {
    /**
     * 响应代码 (0表示成功)
     */
    code?: number;
    /**
     * 响应消息
     */
    msg?: string;
    data?: HeartbeatData;
};

export type PlayerDataBackupRequest = {
    /**
     * 游戏ID
     */
    game_id: number;
    /**
     * 备份数据键名，用于区分不同类型的备份数据
     */
    backup_key: string;
    /**
     * 要备份的JSON数据，支持任意结构（最大1MB）
     */
    backup_data: {
        [key: string]: unknown;
    };
    /**
     * 备份类型（1=手动备份, 2=自动备份, 3=关键节点备份）
     */
    backup_type?: 1 | 2 | 3;
    /**
     * 备份描述
     */
    description?: string;
    /**
     * 设备信息
     */
    device_info?: {
        [key: string]: unknown;
    };
};

export type PlayerDataBackupResponse = {
    /**
     * 响应代码 (0表示成功)
     */
    code?: number;
    /**
     * 响应消息
     */
    msg?: string;
    data?: {
        /**
         * 备份记录ID
         */
        backup_id?: number;
        /**
         * 备份数据键名
         */
        backup_key?: string;
        /**
         * 数据大小（字节）
         */
        data_size?: number;
        /**
         * 备份类型
         */
        backup_type?: number;
        /**
         * 备份类型描述
         */
        backup_type_description?: string;
        /**
         * 创建时间
         */
        created_at?: string;
        /**
         * 数据校验和（SHA-256）
         */
        checksum?: string;
    };
};

export type PlayerDataRetrieveResponse = {
    /**
     * 响应代码 (0表示成功)
     */
    code?: number;
    /**
     * 响应消息
     */
    msg?: string;
    data?: PlayerBackupData;
};

export type PlayerDataRetrieveAllResponse = {
    /**
     * 响应代码 (0表示成功)
     */
    code?: number;
    /**
     * 响应消息
     */
    msg?: string;
    data?: {
        /**
         * 备份数据列表
         */
        backups?: Array<PlayerBackupData>;
        /**
         * 总备份数量
         */
        total_count?: number;
    };
};

export type PlayerDataStatsResponse = {
    /**
     * 响应代码 (0表示成功)
     */
    code?: number;
    /**
     * 响应消息
     */
    msg?: string;
    data?: {
        /**
         * 总备份数量
         */
        total_backups?: number;
        /**
         * 总数据大小（字节）
         */
        total_data_size?: number;
        /**
         * 按备份类型分组的统计（键为备份类型，值为数量）
         */
        backup_types?: {
            [key: string]: number;
        };
        /**
         * 最新备份时间
         */
        latest_backup?: string;
    };
};

export type PlayerBackupData = {
    /**
     * 备份数据键名
     */
    backup_key?: string;
    /**
     * 备份的JSON数据
     */
    backup_data?: {
        [key: string]: unknown;
    };
    /**
     * 数据版本号
     */
    data_version?: number;
    /**
     * 备份类型（1=手动, 2=自动, 3=关键节点）
     */
    backup_type?: number;
    /**
     * 备份类型描述
     */
    backup_type_description?: string;
    /**
     * 备份描述
     */
    description?: string;
    /**
     * 创建时间
     */
    created_at?: string;
    /**
     * 更新时间
     */
    updated_at?: string;
    /**
     * 数据大小（字节）
     */
    data_size?: number;
};

export type GetCurrentUserData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/user/me';
};

export type GetCurrentUserErrors = {
    /**
     * 未授权
     */
    401: ApiResponse;
};

export type GetCurrentUserError = GetCurrentUserErrors[keyof GetCurrentUserErrors];

export type GetCurrentUserResponses = {
    /**
     * 成功获取用户信息
     */
    200: UserResponse;
};

export type GetCurrentUserResponse = GetCurrentUserResponses[keyof GetCurrentUserResponses];

export type GetGameConfigData = {
    body?: never;
    headers: {
        /**
         * 渠道代码（数字字符串）
         */
        'X-Channel-Code': string;
    };
    path?: never;
    query: {
        /**
         * 游戏代码（数字字符串）
         */
        code: string;
    };
    url: '/api/game/config';
};

export type GetGameConfigErrors = {
    /**
     * 请求参数无效
     */
    400: ApiResponse;
    /**
     * 渠道未启用或游戏未找到
     */
    404: ApiResponse;
};

export type GetGameConfigError = GetGameConfigErrors[keyof GetGameConfigErrors];

export type GetGameConfigResponses = {
    /**
     * 成功获取游戏配置
     */
    200: GameConfigResponse;
};

export type GetGameConfigResponse = GetGameConfigResponses[keyof GetGameConfigResponses];

export type ReportAdEventData = {
    /**
     * 广告事件数组，最多包含10个事件
     */
    body: Array<AdEvent>;
    headers: {
        /**
         * 渠道代码（数字字符串）
         */
        'X-Channel-Code': string;
    };
    path?: never;
    query?: never;
    url: '/api/adevent/report';
};

export type ReportAdEventErrors = {
    /**
     * 请求格式错误
     */
    400: InvalidChannelCodeResponse | InvalidRequestResponse | PayloadTooLargeResponse | TooManyEventsResponse;
    /**
     * 未授权
     */
    401: ApiResponse;
    /**
     * 服务端内部错误
     */
    500: InternalErrorResponse;
};

export type ReportAdEventError = ReportAdEventErrors[keyof ReportAdEventErrors];

export type ReportAdEventResponses = {
    /**
     * 事件处理完成（可能包含部分失败）
     */
    200: AdEventResponse;
};

export type ReportAdEventResponse = ReportAdEventResponses[keyof ReportAdEventResponses];

export type SendHeartbeatData = {
    body?: HeartbeatRequest;
    path?: never;
    query?: never;
    url: '/api/heartbeat/heartbeat';
};

export type SendHeartbeatErrors = {
    /**
     * 请求参数无效
     */
    400: ApiResponse;
    /**
     * 用户未登录
     */
    401: ApiResponse;
    /**
     * 未找到有效会话
     */
    404: ApiResponse;
    /**
     * 请求频率超限
     */
    429: ApiResponse;
    /**
     * 服务端内部错误
     */
    500: ApiResponse;
};

export type SendHeartbeatError = SendHeartbeatErrors[keyof SendHeartbeatErrors];

export type SendHeartbeatResponses = {
    /**
     * 心跳处理成功
     */
    200: HeartbeatResponse;
};

export type SendHeartbeatResponse = SendHeartbeatResponses[keyof SendHeartbeatResponses];

export type BackupPlayerDataData = {
    body: PlayerDataBackupRequest;
    path?: never;
    query?: never;
    url: '/api/player-data/backup';
};

export type BackupPlayerDataErrors = {
    /**
     * 请求参数无效
     */
    400: ApiResponse;
    /**
     * 用户未登录
     */
    401: ApiResponse;
    /**
     * 请求频率超限
     */
    429: ApiResponse;
    /**
     * 服务端内部错误
     */
    500: ApiResponse;
};

export type BackupPlayerDataError = BackupPlayerDataErrors[keyof BackupPlayerDataErrors];

export type BackupPlayerDataResponses = {
    /**
     * 备份创建成功
     */
    200: PlayerDataBackupResponse;
};

export type BackupPlayerDataResponse = BackupPlayerDataResponses[keyof BackupPlayerDataResponses];

export type RetrieveAllPlayerDataData = {
    body?: never;
    path?: never;
    query: {
        /**
         * 游戏ID
         */
        game_id: number;
    };
    url: '/api/player-data/retrieve';
};

export type RetrieveAllPlayerDataErrors = {
    /**
     * 请求参数无效
     */
    400: ApiResponse;
    /**
     * 用户未登录
     */
    401: ApiResponse;
    /**
     * 游戏或渠道不存在
     */
    404: ApiResponse;
    /**
     * 服务端内部错误
     */
    500: ApiResponse;
};

export type RetrieveAllPlayerDataError = RetrieveAllPlayerDataErrors[keyof RetrieveAllPlayerDataErrors];

export type RetrieveAllPlayerDataResponses = {
    /**
     * 成功获取所有备份数据
     */
    200: PlayerDataRetrieveAllResponse;
};

export type RetrieveAllPlayerDataResponse = RetrieveAllPlayerDataResponses[keyof RetrieveAllPlayerDataResponses];

export type RetrieveSpecificPlayerDataData = {
    body?: never;
    path: {
        /**
         * 备份数据键名
         */
        backup_key: string;
    };
    query: {
        /**
         * 游戏ID
         */
        game_id: number;
    };
    url: '/api/player-data/retrieve/{backup_key}';
};

export type RetrieveSpecificPlayerDataErrors = {
    /**
     * 请求参数无效
     */
    400: ApiResponse;
    /**
     * 用户未登录
     */
    401: ApiResponse;
    /**
     * 备份数据未找到
     */
    404: ApiResponse;
    /**
     * 备份数据损坏
     */
    409: ApiResponse;
    /**
     * 服务端内部错误
     */
    500: ApiResponse;
};

export type RetrieveSpecificPlayerDataError = RetrieveSpecificPlayerDataErrors[keyof RetrieveSpecificPlayerDataErrors];

export type RetrieveSpecificPlayerDataResponses = {
    /**
     * 成功获取备份数据
     */
    200: PlayerDataRetrieveResponse;
};

export type RetrieveSpecificPlayerDataResponse = RetrieveSpecificPlayerDataResponses[keyof RetrieveSpecificPlayerDataResponses];

export type GetPlayerBackupStatsData = {
    body?: never;
    path?: never;
    query: {
        /**
         * 游戏ID
         */
        game_id: number;
    };
    url: '/api/player-data/stats';
};

export type GetPlayerBackupStatsErrors = {
    /**
     * 请求参数无效
     */
    400: ApiResponse;
    /**
     * 用户未登录
     */
    401: ApiResponse;
    /**
     * 服务端内部错误
     */
    500: ApiResponse;
};

export type GetPlayerBackupStatsError = GetPlayerBackupStatsErrors[keyof GetPlayerBackupStatsErrors];

export type GetPlayerBackupStatsResponses = {
    /**
     * 成功获取统计信息
     */
    200: PlayerDataStatsResponse;
};

export type GetPlayerBackupStatsResponse = GetPlayerBackupStatsResponses[keyof GetPlayerBackupStatsResponses];

export type ClientOptions = {
    baseUrl: `${string}://docs` | (string & {});
};