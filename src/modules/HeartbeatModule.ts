/**
 * 心跳上报模块
 *
 * 负责定期发送心跳信号以维持会话活跃状态
 */

// ============================================================================
// 导入依赖
// ============================================================================

import type { SDKConfig } from '../types';
import { sendHeartbeat } from '../api';

// ============================================================================
// 类型定义
// ============================================================================

/**
 * 心跳模块状态
 */
enum HeartbeatState {
  STOPPED = 'stopped',
  RUNNING = 'running',
  ERROR = 'error',
}

// ============================================================================
// 心跳模块实现
// ============================================================================

/**
 * 心跳模块类
 */
export class HeartbeatModule {
  // ------------------------------------------------------------------------
  // 属性定义
  // ------------------------------------------------------------------------

  private config: SDKConfig;
  private state: HeartbeatState = HeartbeatState.STOPPED;
  private heartbeatTimer?: number | undefined;
  private heartbeatCount = 0;
  private startTime?: number | undefined;

  // 配置常量
  private readonly HEARTBEAT_INTERVAL = 30000; // 30 seconds

  // ------------------------------------------------------------------------
  // 构造函数
  // ------------------------------------------------------------------------

  constructor(config: SDKConfig) {
    this.config = config;
  }

  // ------------------------------------------------------------------------
  // 公共方法
  // ------------------------------------------------------------------------

  /**
   * 启动心跳上报
   */
  start(): void {
    if (this.state === HeartbeatState.RUNNING) {
      if (this.config.debug) {
        console.warn('[HeartbeatModule] 心跳已在运行中，跳过重复启动');
      }
      return;
    }

    this.state = HeartbeatState.RUNNING;
    this.startTime = Date.now();
    this.heartbeatCount = 0;

    if (this.config.debug) {
      console.log('[HeartbeatModule] 启动心跳上报，间隔:', this.HEARTBEAT_INTERVAL, 'ms');
    }

    // 立即发送第一次心跳
    this.sendHeartbeatSafely();

    // 设置定时器
    this.heartbeatTimer = window.setInterval(() => {
      this.sendHeartbeatSafely();
    }, this.HEARTBEAT_INTERVAL);
  }

  /**
   * 停止心跳上报
   */
  stop(): void {
    if (this.state === HeartbeatState.STOPPED) {
      return;
    }

    if (this.config.debug) {
      console.log('[HeartbeatModule] 停止心跳上报');
    }

    this.state = HeartbeatState.STOPPED;

    if (this.heartbeatTimer) {
      window.clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = undefined;
    }
  }

  /**
   * 获取心跳状态
   */
  getStatus(): {
    state: string;
    heartbeatCount: number;
    uptime: number;
  } {
    const uptime = this.startTime ? Date.now() - this.startTime : 0;
    return {
      state: this.state,
      heartbeatCount: this.heartbeatCount,
      uptime,
    };
  }

  /**
   * 清理模块资源
   */
  cleanup(): void {
    this.stop();
    this.heartbeatCount = 0;
    this.startTime = undefined;

    if (this.config.debug) {
      console.log('[HeartbeatModule] 心跳模块已清理');
    }
  }

  // ------------------------------------------------------------------------
  // 私有方法
  // ------------------------------------------------------------------------

  /**
   * 安全地发送心跳（带错误处理）
   */
  private async sendHeartbeatSafely(): Promise<void> {
    try {
      await this.sendHeartbeat();
    } catch (error) {
      // 静默处理错误，不影响其他模块
      if (this.config.debug) {
        console.error('[HeartbeatModule] 心跳发送失败:', error);
      }
      
      // 如果连续失败，可以考虑设置错误状态
      this.state = HeartbeatState.ERROR;
      
      // 但不停止心跳，继续尝试
      setTimeout(() => {
        if (this.state === HeartbeatState.ERROR) {
          this.state = HeartbeatState.RUNNING;
        }
      }, this.HEARTBEAT_INTERVAL);
    }
  }

  /**
   * 发送心跳请求
   */
  private async sendHeartbeat(): Promise<void> {
    this.heartbeatCount++;
    
    const customData = {
      heartbeat_count: this.heartbeatCount,
      uptime_seconds: this.startTime ? Math.floor((Date.now() - this.startTime) / 1000) : 0,
      sdk_version: '1.0.4',
      timestamp: Date.now(),
    };

    if (this.config.debug) {
      console.log('[HeartbeatModule] 发送心跳:', customData);
    }

    const response = await sendHeartbeat({
      body: {
        custom_data: customData,
      },
    });

    if (this.config.debug) {
      console.log('[HeartbeatModule] 心跳响应:', response);
    }
  }
}
