/**
 * 事件上报模块
 *
 * 负责收集和上报各种 SDK 事件
 */

// ============================================================================
// 导入依赖
// ============================================================================

import type { AdEvent } from '../api';
import type { SDKConfig, EventData, SDKEventName } from '../types';
import { reportAdEvent } from '../api';
import { generateClientEventId, generateSessionId } from '../utils/uuid';
import { StorageUtil } from '../utils/storage';

// ============================================================================
// 类型定义
// ============================================================================

/**
 * 事件队列项
 */
interface EventQueueItem {
  event: AdEvent;
  retryCount: number;
}

// ============================================================================
// 事件模块实现
// ============================================================================

/**
 * 事件模块类
 */
export class EventModule {
  // ------------------------------------------------------------------------
  // 属性定义
  // ------------------------------------------------------------------------

  private config: SDKConfig;
  private sessionId: string;
  private eventQueue: EventQueueItem[] = [];
  private isReporting = false;
  private getUserId: () => number | null;

  // 配置常量
  private readonly MAX_QUEUE_SIZE = 50;
  private readonly MAX_RETRY_COUNT: number;
  private readonly BATCH_SIZE: number;
  private readonly REPORT_INTERVAL: number;
  private reportTimer?: number | undefined;

  // ------------------------------------------------------------------------
  // 构造函数
  // ------------------------------------------------------------------------

  constructor(config: SDKConfig, getUserId: () => number | null) {
    this.config = config;
    this.getUserId = getUserId;
    this.sessionId = this.initializeSessionId();

    // 从配置中获取参数，如果配置中没有则使用默认值
    this.MAX_RETRY_COUNT = config.maxRetries ?? 3;
    this.BATCH_SIZE = Math.min(config.batchSize ?? 10, 10); // 确保不超过 API 限制
    this.REPORT_INTERVAL = config.reportInterval ?? 5000;

    this.startPeriodicReporting();
  }

  // ------------------------------------------------------------------------
  // 公共方法
  // ------------------------------------------------------------------------

  /**
   * 上报事件
   */
  reportEvent(eventName: SDKEventName, eventData: Partial<EventData> = {}): void {
    const userId = this.getUserId();
    if (!userId) {
      if (this.config.debug) {
        console.warn('[EventModule] 用户 ID 不存在，跳过事件上报:', eventName);
      }
      return;
    }

    const event: AdEvent = {
      event_name: eventName,
      event_time: Date.now(),
      user_id: userId,
      game_id: parseInt(this.config.appid, 10),
      channel_id: parseInt(this.config.channel, 10),
      client_event_id: generateClientEventId(),
      session_id: this.sessionId,
      ...eventData,
    };

    this.addToQueue(event);

    if (this.config.debug) {
      console.log('[EventModule] 事件已加入队列:', event);
    }
  }

  /**
   * 立即上报所有队列中的事件
   */
  async flushEvents(): Promise<void> {
    if (this.isReporting || this.eventQueue.length === 0) {
      return;
    }

    this.isReporting = true;

    try {
      // 取出要上报的事件
      const eventsToReport = this.eventQueue.splice(0, this.BATCH_SIZE);
      const events = eventsToReport.map(item => item.event);

      if (this.config.debug) {
        console.log('[EventModule] 开始上报事件:', events);
      }

      // 调用 API 上报事件
      const response = await reportAdEvent({
        body: events,
        headers: {
          'X-Channel-Code': this.config.channel,
        },
      });

      if (this.config.debug) {
        console.log('[EventModule] 事件上报成功:', response);
      }

      // 检查是否有失败的事件需要重试
      if (response.data?.data?.failures && response.data.data.failures.length > 0) {
        this.handleFailedEvents(eventsToReport, response.data.data.failures);
      }
    } catch (error) {
      if (this.config.debug) {
        console.error('[EventModule] 事件上报失败:', error);
      }

      // 将失败的事件重新加入队列进行重试
      const failedEvents = this.eventQueue.splice(0, this.BATCH_SIZE);
      this.handleRetryEvents(failedEvents);
    } finally {
      this.isReporting = false;
    }
  }

  /**
   * 获取队列状态
   */
  getQueueStatus(): { queueSize: number; isReporting: boolean } {
    return {
      queueSize: this.eventQueue.length,
      isReporting: this.isReporting,
    };
  }

  /**
   * 清理模块资源
   */
  cleanup(): void {
    this.stopPeriodicReporting();

    // 尝试上报剩余事件
    if (this.eventQueue.length > 0) {
      this.flushEvents().catch(error => {
        if (this.config.debug) {
          console.error('[EventModule] 清理时上报事件失败:', error);
        }
      });
    }

    this.eventQueue = [];
  }

  // ------------------------------------------------------------------------
  // 私有方法
  // ------------------------------------------------------------------------

  /**
   * 初始化会话 ID
   */
  private initializeSessionId(): string {
    let sessionId = StorageUtil.getSessionId();
    if (!sessionId) {
      sessionId = generateSessionId();
      StorageUtil.saveSessionId(sessionId);
    }
    return sessionId;
  }

  /**
   * 添加事件到队列
   */
  private addToQueue(event: AdEvent): void {
    // 检查队列大小
    if (this.eventQueue.length >= this.MAX_QUEUE_SIZE) {
      // 移除最旧的事件
      this.eventQueue.shift();
      if (this.config.debug) {
        console.warn('[EventModule] 事件队列已满，移除最旧的事件');
      }
    }

    this.eventQueue.push({
      event,
      retryCount: 0,
    });

    // 如果队列达到批次大小，立即上报
    if (this.eventQueue.length >= this.BATCH_SIZE) {
      this.flushEvents();
    }
  }

  /**
   * 处理失败的事件
   */
  private handleFailedEvents(
    originalEvents: EventQueueItem[],
    failures: Array<{ index?: number; reason?: string }>
  ): void {
    failures.forEach(failure => {
      if (failure.index !== undefined && failure.index < originalEvents.length) {
        const failedEvent = originalEvents[failure.index];
        if (failedEvent && failedEvent.retryCount < this.MAX_RETRY_COUNT) {
          failedEvent.retryCount++;
          this.eventQueue.unshift(failedEvent); // 重新加入队列头部

          if (this.config.debug) {
            console.warn('[EventModule] 事件上报失败，加入重试队列:', {
              event: failedEvent.event.event_name,
              reason: failure.reason,
              retryCount: failedEvent.retryCount,
            });
          }
        } else if (failedEvent) {
          if (this.config.debug) {
            console.error('[EventModule] 事件重试次数已达上限，丢弃事件:', {
              event: failedEvent.event.event_name,
              reason: failure.reason,
            });
          }
        }
      }
    });
  }

  /**
   * 处理需要重试的事件
   */
  private handleRetryEvents(events: EventQueueItem[]): void {
    events.forEach(eventItem => {
      if (eventItem.retryCount < this.MAX_RETRY_COUNT) {
        eventItem.retryCount++;
        this.eventQueue.unshift(eventItem); // 重新加入队列头部
      } else {
        if (this.config.debug) {
          console.error('[EventModule] 事件重试次数已达上限，丢弃事件:', eventItem.event.event_name);
        }
      }
    });
  }

  /**
   * 开始定期上报
   */
  private startPeriodicReporting(): void {
    this.reportTimer = window.setInterval(() => {
      if (this.eventQueue.length > 0) {
        this.flushEvents();
      }
    }, this.REPORT_INTERVAL);
  }

  /**
   * 停止定期上报
   */
  private stopPeriodicReporting(): void {
    if (this.reportTimer) {
      window.clearInterval(this.reportTimer);
      this.reportTimer = undefined;
    }
  }
}
