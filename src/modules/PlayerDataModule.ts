/**
 * 玩家数据备份模块
 *
 * 负责玩家游戏数据的备份和恢复功能
 */

// ============================================================================
// 导入依赖
// ============================================================================

import type { SDKConfig, PlayerDataBackupOptions, PlayerDataBackupResult, PlayerDataRetrieveResult, PlayerDataStats } from '../types';
import { BackupType, BackupTypeToNumber, NumberToBackupType } from '../types';
import type { PlayerBackupData } from '../api';
import { backupPlayerData, retrieveAllPlayerData, retrieveSpecificPlayerData, getPlayerBackupStats } from '../api';

// ============================================================================
// 类型定义
// ============================================================================

/**
 * 玩家数据模块状态
 */
enum PlayerDataModuleState {
  IDLE = 'idle',
  BACKING_UP = 'backing_up',
  RETRIEVING = 'retrieving',
  ERROR = 'error',
}

// ============================================================================
// 玩家数据模块实现
// ============================================================================

/**
 * 玩家数据备份模块类
 */
export class PlayerDataModule {
  // ------------------------------------------------------------------------
  // 属性定义
  // ------------------------------------------------------------------------

  private config: SDKConfig;
  private getUserId: () => number | null;
  private state: PlayerDataModuleState = PlayerDataModuleState.IDLE;

  // ------------------------------------------------------------------------
  // 构造函数
  // ------------------------------------------------------------------------

  constructor(config: SDKConfig, getUserId: () => number | null) {
    this.config = config;
    this.getUserId = getUserId;
  }

  // ------------------------------------------------------------------------
  // 公共方法
  // ------------------------------------------------------------------------

  /**
   * 备份玩家数据
   * @param backupKey 备份数据键名，用于区分不同类型的数据
   * @param data 要备份的数据
   * @param options 备份选项
   * @returns 备份结果
   */
  async backupData(
    backupKey: string,
    data: Record<string, unknown>,
    options: PlayerDataBackupOptions = {}
  ): Promise<PlayerDataBackupResult> {
    if (this.state !== PlayerDataModuleState.IDLE) {
      throw new Error(`玩家数据模块正在${this.state === PlayerDataModuleState.BACKING_UP ? '备份' : '检索'}中，请稍后再试`);
    }

    const userId = this.getUserId();
    if (!userId) {
      throw new Error('用户未登录，无法备份数据');
    }

    try {
      this.state = PlayerDataModuleState.BACKING_UP;

      if (this.config.debug) {
        console.log('[PlayerDataModule] 开始备份数据:', { backupKey, dataSize: JSON.stringify(data).length });
      }

      // 准备备份请求数据
      const backupType = options.backupType || BackupType.MANUAL;
      const requestData: any = {
        game_id: parseInt(this.config.appid, 10),
        backup_key: backupKey,
        backup_data: data,
        backup_type: BackupTypeToNumber[backupType] as 1 | 2 | 3,
        device_info: options.deviceInfo || {
          platform: 'web',
          user_agent: navigator.userAgent,
          timestamp: Date.now(),
        },
      };

      // 只有在有描述时才添加描述字段
      if (options.description) {
        requestData.description = options.description;
      }

      // 调用 API 备份数据
      const response = await backupPlayerData({
        body: requestData,
      });

      if (response.data?.code !== 0) {
        throw new Error(response.data?.msg || '备份数据失败');
      }

      const apiData = response.data.data;
      if (!apiData) {
        throw new Error('备份响应数据格式错误');
      }

      // 转换响应数据为标准格式
      const result: PlayerDataBackupResult = {
        backupId: apiData.backup_id || 0,
        backupKey: apiData.backup_key || backupKey,
        dataSize: apiData.data_size || 0,
        backupType: NumberToBackupType[apiData.backup_type || 1] || BackupType.MANUAL,
        backupTypeDescription: apiData.backup_type_description || '',
        createdAt: apiData.created_at || '',
        checksum: apiData.checksum || '',
      };

      if (this.config.debug) {
        console.log('[PlayerDataModule] 数据备份成功:', result);
      }

      return result;
    } catch (error) {
      this.state = PlayerDataModuleState.ERROR;
      const errorMessage = `备份数据失败: ${error instanceof Error ? error.message : String(error)}`;
      
      if (this.config.debug) {
        console.error('[PlayerDataModule]', errorMessage, error);
      }
      
      throw new Error(errorMessage);
    } finally {
      if (this.state !== PlayerDataModuleState.ERROR) {
        this.state = PlayerDataModuleState.IDLE;
      }
    }
  }

  /**
   * 检索特定备份数据
   * @param backupKey 备份数据键名
   * @returns 备份数据
   */
  async retrieveData(backupKey: string): Promise<PlayerDataRetrieveResult> {
    if (this.state !== PlayerDataModuleState.IDLE) {
      throw new Error(`玩家数据模块正在${this.state === PlayerDataModuleState.BACKING_UP ? '备份' : '检索'}中，请稍后再试`);
    }

    const userId = this.getUserId();
    if (!userId) {
      throw new Error('用户未登录，无法检索数据');
    }

    try {
      this.state = PlayerDataModuleState.RETRIEVING;

      if (this.config.debug) {
        console.log('[PlayerDataModule] 开始检索数据:', backupKey);
      }

      // 调用 API 检索数据
      const response = await retrieveSpecificPlayerData({
        path: { backup_key: backupKey },
        query: { game_id: parseInt(this.config.appid, 10) },
      });

      if (response.data?.code !== 0) {
        throw new Error(response.data?.msg || '检索数据失败');
      }

      const apiData = response.data.data;
      if (!apiData) {
        throw new Error('检索响应数据格式错误');
      }

      // 转换响应数据为标准格式
      const result = this.convertPlayerBackupDataToResult(apiData);

      if (this.config.debug) {
        console.log('[PlayerDataModule] 数据检索成功:', { backupKey, dataSize: result.dataSize });
      }

      return result;
    } catch (error) {
      this.state = PlayerDataModuleState.ERROR;
      const errorMessage = `检索数据失败: ${error instanceof Error ? error.message : String(error)}`;
      
      if (this.config.debug) {
        console.error('[PlayerDataModule]', errorMessage, error);
      }
      
      throw new Error(errorMessage);
    } finally {
      if (this.state !== PlayerDataModuleState.ERROR) {
        this.state = PlayerDataModuleState.IDLE;
      }
    }
  }

  /**
   * 检索所有备份数据
   * @returns 所有备份数据列表
   */
  async retrieveAllData(): Promise<PlayerDataRetrieveResult[]> {
    if (this.state !== PlayerDataModuleState.IDLE) {
      throw new Error(`玩家数据模块正在${this.state === PlayerDataModuleState.BACKING_UP ? '备份' : '检索'}中，请稍后再试`);
    }

    const userId = this.getUserId();
    if (!userId) {
      throw new Error('用户未登录，无法检索数据');
    }

    try {
      this.state = PlayerDataModuleState.RETRIEVING;

      if (this.config.debug) {
        console.log('[PlayerDataModule] 开始检索所有备份数据');
      }

      // 调用 API 检索所有数据
      const response = await retrieveAllPlayerData({
        query: { game_id: parseInt(this.config.appid, 10) },
      });

      if (response.data?.code !== 0) {
        throw new Error(response.data?.msg || '检索所有数据失败');
      }

      const apiData = response.data.data;
      if (!apiData || !apiData.backups) {
        return [];
      }

      // 转换响应数据为标准格式
      const results = apiData.backups.map(backup => this.convertPlayerBackupDataToResult(backup));

      if (this.config.debug) {
        console.log('[PlayerDataModule] 所有数据检索成功:', { count: results.length });
      }

      return results;
    } catch (error) {
      this.state = PlayerDataModuleState.ERROR;
      const errorMessage = `检索所有数据失败: ${error instanceof Error ? error.message : String(error)}`;
      
      if (this.config.debug) {
        console.error('[PlayerDataModule]', errorMessage, error);
      }
      
      throw new Error(errorMessage);
    } finally {
      if (this.state !== PlayerDataModuleState.ERROR) {
        this.state = PlayerDataModuleState.IDLE;
      }
    }
  }

  /**
   * 获取备份统计信息
   * @returns 备份统计信息
   */
  async getStats(): Promise<PlayerDataStats> {
    const userId = this.getUserId();
    if (!userId) {
      throw new Error('用户未登录，无法获取统计信息');
    }

    try {
      if (this.config.debug) {
        console.log('[PlayerDataModule] 开始获取备份统计信息');
      }

      // 调用 API 获取统计信息
      const response = await getPlayerBackupStats({
        query: { game_id: parseInt(this.config.appid, 10) },
      });

      if (response.data?.code !== 0) {
        throw new Error(response.data?.msg || '获取统计信息失败');
      }

      const apiData = response.data.data;
      if (!apiData) {
        throw new Error('统计信息响应数据格式错误');
      }

      // 转换备份类型统计
      const backupTypes: Record<BackupType, number> = {
        [BackupType.MANUAL]: 0,
        [BackupType.AUTO]: 0,
        [BackupType.CHECKPOINT]: 0,
      };

      if (apiData.backup_types) {
        Object.entries(apiData.backup_types).forEach(([key, value]) => {
          const numKey = parseInt(key, 10);
          const backupType = NumberToBackupType[numKey];
          if (backupType) {
            backupTypes[backupType] = value;
          }
        });
      }

      const result: PlayerDataStats = {
        totalBackups: apiData.total_backups || 0,
        totalDataSize: apiData.total_data_size || 0,
        backupTypes,
        latestBackup: apiData.latest_backup || undefined,
      };

      if (this.config.debug) {
        console.log('[PlayerDataModule] 统计信息获取成功:', result);
      }

      return result;
    } catch (error) {
      const errorMessage = `获取统计信息失败: ${error instanceof Error ? error.message : String(error)}`;
      
      if (this.config.debug) {
        console.error('[PlayerDataModule]', errorMessage, error);
      }
      
      throw new Error(errorMessage);
    }
  }

  /**
   * 获取模块状态
   */
  getModuleStatus(): { state: string } {
    return { state: this.state };
  }

  /**
   * 清理模块资源
   */
  cleanup(): void {
    this.state = PlayerDataModuleState.IDLE;

    if (this.config.debug) {
      console.log('[PlayerDataModule] 玩家数据模块已清理');
    }
  }

  // ------------------------------------------------------------------------
  // 私有方法
  // ------------------------------------------------------------------------

  /**
   * 转换 API 响应数据为标准格式
   */
  private convertPlayerBackupDataToResult(apiData: PlayerBackupData): PlayerDataRetrieveResult {
    return {
      backupKey: apiData.backup_key || '',
      backupData: apiData.backup_data || {},
      dataVersion: apiData.data_version || 1,
      backupType: NumberToBackupType[apiData.backup_type || 1] || BackupType.MANUAL,
      backupTypeDescription: apiData.backup_type_description || '',
      description: apiData.description ? apiData.description : undefined,
      createdAt: apiData.created_at || '',
      updatedAt: apiData.updated_at || '',
      dataSize: apiData.data_size || 0,
    };
  }
}
