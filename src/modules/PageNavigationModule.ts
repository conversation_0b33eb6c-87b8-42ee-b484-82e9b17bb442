/**
 * 页面导航模块
 * 
 * 提供独立的页面导航功能，不依赖SDK初始化状态
 * 通过 adapter 模式调用原生接口，保持架构一致性
 */

// ============================================================================
// 导入依赖
// ============================================================================

import type { NativeBridgeAdapter } from '../adapters/NativeBridgeAdapter';
import { DsmAdapter } from '../adapters/DsmAdapter';

// ============================================================================
// 类型定义
// ============================================================================

/**
 * 页面导航配置
 */
export interface PageNavigationConfig {
  /** 是否启用调试模式 */
  debug?: boolean;
  /** 自定义适配器实例 */
  adapter?: NativeBridgeAdapter;
}

/**
 * 页面关闭选项
 */
export interface PageCloseOptions {
  /** 是否启用调试日志 */
  debug?: boolean;
}

/**
 * 页面关闭支持信息
 */
export interface PageCloseSupport {
  /** 是否支持页面关闭 */
  supported: boolean;
  /** 是否支持 iOS 接口 */
  ios: boolean;
  /** 是否支持 Android 接口 */
  android: boolean;
  /** 优先使用的接口类型 */
  preferredInterface: 'ios' | 'android' | null;
}

// ============================================================================
// 页面导航模块类
// ============================================================================

/**
 * 页面导航模块
 * 
 * 独立的页面导航功能模块，不依赖SDK状态
 */
export class PageNavigationModule {
  // ------------------------------------------------------------------------
  // 属性定义
  // ------------------------------------------------------------------------

  private adapter: NativeBridgeAdapter;
  private config: PageNavigationConfig;

  // ------------------------------------------------------------------------
  // 构造函数
  // ------------------------------------------------------------------------

  constructor(config: PageNavigationConfig = {}) {
    this.config = { debug: false, ...config };
    
    // 使用自定义适配器或创建默认适配器
    this.adapter = config.adapter || new DsmAdapter();
    
    // 设置调试模式（如果适配器支持）
    if ('setDebugMode' in this.adapter && typeof this.adapter.setDebugMode === 'function') {
      (this.adapter as any).setDebugMode(this.config.debug || false);
    }
  }

  // ------------------------------------------------------------------------
  // 公共方法
  // ------------------------------------------------------------------------

  /**
   * 关闭页面/返回上一页
   * 
   * @param options 页面关闭选项
   * @throws {Error} 当未检测到支持的原生环境时抛出错误
   */
  closePage(options: PageCloseOptions = {}): void {
    const debug = options.debug ?? this.config.debug ?? false;
    
    if (debug) {
      console.log('[PageNavigationModule] 开始执行页面关闭操作');
    }

    try {
      this.adapter.closePage();
      
      if (debug) {
        console.log('[PageNavigationModule] 页面关闭操作完成');
      }
    } catch (error) {
      if (debug) {
        console.error('[PageNavigationModule] 页面关闭操作失败:', error);
      }
      throw error;
    }
  }

  /**
   * 检查是否支持页面关闭功能
   */
  canClosePage(): boolean {
    try {
      const windowObj = window as any;
      return !!(windowObj.webkit?.messageHandlers?.returnHome) || 
             !!(windowObj.DsmJSInterface?.goPreviewPage);
    } catch (error) {
      if (this.config.debug) {
        console.error('[PageNavigationModule] 检查页面关闭支持时出错:', error);
      }
      return false;
    }
  }

  /**
   * 获取页面关闭支持信息
   */
  getPageCloseSupport(): PageCloseSupport {
    try {
      const windowObj = window as any;
      
      const ios = !!(windowObj.webkit?.messageHandlers?.returnHome);
      const android = !!(windowObj.DsmJSInterface?.goPreviewPage);
      const supported = ios || android;
      const preferredInterface = ios ? 'ios' : (android ? 'android' : null);

      return {
        supported,
        ios,
        android,
        preferredInterface
      };
    } catch (error) {
      if (this.config.debug) {
        console.error('[PageNavigationModule] 获取页面关闭支持信息时出错:', error);
      }
      return {
        supported: false,
        ios: false,
        android: false,
        preferredInterface: null
      };
    }
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    if (this.config.debug) {
      console.log('[PageNavigationModule] 清理页面导航模块');
    }
    
    this.adapter.cleanup();
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<PageNavigationConfig>): void {
    this.config = { ...this.config, ...config };
    
    // 设置调试模式（如果适配器支持）
    if ('setDebugMode' in this.adapter && typeof this.adapter.setDebugMode === 'function') {
      (this.adapter as any).setDebugMode(this.config.debug || false);
    }
  }
}
