/**
 * 广告模块
 *
 * 负责广告的展示和相关事件的处理
 */

// ============================================================================
// 导入依赖
// ============================================================================

import type { AdCloseCallback, SDKConfig } from '../types';
import { AdCloseType, SDKEventName } from '../types';
import type { NativeBridgeAdapter } from '../adapters/NativeBridgeAdapter';
import type { EventModule } from './EventModule';

// ============================================================================
// 类型定义
// ============================================================================

/**
 * 广告展示状态
 */
enum AdState {
  IDLE = 'idle',
  REQUESTING = 'requesting',
  SHOWING = 'showing',
}

// ============================================================================
// 广告模块实现
// ============================================================================

/**
 * 广告模块类
 */
export class AdModule {
  // ------------------------------------------------------------------------
  // 属性定义
  // ------------------------------------------------------------------------

  private adapter: NativeBridgeAdapter;
  private config: SDKConfig;
  private eventModule: EventModule;
  private currentState: AdState = AdState.IDLE;
  private currentCallback?: AdCloseCallback | undefined;

  // ------------------------------------------------------------------------
  // 构造函数
  // ------------------------------------------------------------------------

  constructor(adapter: NativeBridgeAdapter, config: SDKConfig, eventModule: EventModule) {
    this.adapter = adapter;
    this.config = config;
    this.eventModule = eventModule;
  }

  // ------------------------------------------------------------------------
  // 公共方法
  // ------------------------------------------------------------------------

  /**
   * 显示广告
   * @param callback 广告关闭时的回调函数
   * @throws {Error} 当显示广告失败时抛出错误
   */
  async showAd(callback?: AdCloseCallback): Promise<void> {
    if (this.currentState !== AdState.IDLE) {
      const error = new Error(`广告正在${this.currentState === AdState.REQUESTING ? '请求' : '展示'}中，请稍后再试`);
      if (this.config.debug) {
        console.error('[AdModule]', error.message);
      }
      throw error;
    }

    try {
      this.currentState = AdState.REQUESTING;
      this.currentCallback = callback;

      // 上报广告请求事件
      this.eventModule.reportEvent(SDKEventName.AD_REQUEST, {
        custom_data: {
          request_time: Date.now(),
        },
      });

      if (this.config.debug) {
        console.log('[AdModule] 开始请求广告');
      }

      // 调用适配器显示广告
      this.adapter.showAd(this.handleAdClose.bind(this));

      this.currentState = AdState.SHOWING;

      // 上报广告展示成功事件
      this.eventModule.reportEvent(SDKEventName.AD_IMPRESSION, {
        custom_data: {
          impression_time: Date.now(),
        },
      });

      if (this.config.debug) {
        console.log('[AdModule] 广告展示成功');
      }
    } catch (error) {
      this.currentState = AdState.IDLE;
      this.currentCallback = undefined;

      // 上报广告展示失败事件
      this.eventModule.reportEvent(SDKEventName.AD_IMPRESSION_FAILED, {
        error_message: error instanceof Error ? error.message : String(error),
        custom_data: {
          error_time: Date.now(),
        },
      });

      const errorMessage = `显示广告失败: ${error instanceof Error ? error.message : String(error)}`;
      if (this.config.debug) {
        console.error('[AdModule]', errorMessage, error);
      }
      throw new Error(errorMessage);
    }
  }

  /**
   * 获取当前广告状态
   */
  getAdState(): string {
    return this.currentState;
  }

  /**
   * 检查是否可以显示广告
   */
  canShowAd(): boolean {
    return this.currentState === AdState.IDLE;
  }

  /**
   * 强制重置广告状态
   * 用于异常情况下的状态恢复
   */
  resetState(): void {
    if (this.config.debug) {
      console.log('[AdModule] 强制重置广告状态');
    }

    this.currentState = AdState.IDLE;
    this.currentCallback = undefined;
  }

  /**
   * 清理模块资源
   */
  cleanup(): void {
    this.resetState();
  }

  // ------------------------------------------------------------------------
  // 私有方法
  // ------------------------------------------------------------------------

  /**
   * 处理广告关闭
   */
  private handleAdClose(type: AdCloseType): void {
    if (this.config.debug) {
      console.log(`[AdModule] 收到广告关闭回调: ${type}, 当前状态: ${this.currentState}`);
    }

    // 更宽松的状态检查 - 允许在 REQUESTING 或 SHOWING 状态下处理关闭
    if (this.currentState !== AdState.SHOWING && this.currentState !== AdState.REQUESTING) {
      if (this.config.debug) {
        console.warn('[AdModule] 收到广告关闭回调，但当前状态不是展示中:', this.currentState);
        console.warn('[AdModule] 强制重置状态以恢复功能');
      }
      // 强制重置状态而不是直接返回
      this.resetState();
      return;
    }

    try {
      if (this.config.debug) {
        console.log('[AdModule] 开始处理广告关闭逻辑');
      }

      // 上报广告关闭事件
      this.eventModule.reportEvent(SDKEventName.AD_CLOSE, {
        custom_data: {
          close_type: AdCloseType[type],
          close_time: Date.now(),
          is_completed: type === AdCloseType.COMPLETED,
        },
      });

      // 如果是观看完成，上报奖励发放事件
      if (type === AdCloseType.COMPLETED) {
        this.eventModule.reportEvent(SDKEventName.AD_REWARD_GRANT, {
          reward_name: '广告奖励',
          reward_amount: 1,
          custom_data: {
            reward_time: Date.now(),
          },
        });
      }

      if (this.config.debug) {
        console.log('[AdModule] 广告关闭:', {
          type,
          isCompleted: type === AdCloseType.COMPLETED,
        });
      }

      // 调用用户回调
      if (this.currentCallback) {
        try {
          if (this.config.debug) {
            console.log('[AdModule] 调用用户回调函数');
          }
          this.currentCallback(type);
          if (this.config.debug) {
            console.log('[AdModule] 用户回调函数执行完成');
          }
        } catch (error) {
          if (this.config.debug) {
            console.error('[AdModule] 用户回调执行失败:', error);
          }
        }
      } else {
        if (this.config.debug) {
          console.warn('[AdModule] 用户回调函数不存在');
        }
      }
    } catch (error) {
      if (this.config.debug) {
        console.error('[AdModule] 处理广告关闭失败:', error);
      }
    } finally {
      // 重置状态 - 这是关键步骤，确保状态被正确重置
      if (this.config.debug) {
        console.log('[AdModule] 重置广告状态到 IDLE');
      }
      this.resetState();
      if (this.config.debug) {
        console.log(`[AdModule] 状态重置完成，当前状态: ${this.currentState}, 可以显示广告: ${this.canShowAd()}`);
      }
    }
  }
}
