/**
 * 用户模块
 *
 * 负责用户信息的获取、缓存和管理
 */

// ============================================================================
// 导入依赖
// ============================================================================

import type { User } from '../api';
import type { CachedUserInfo, SDKConfig } from '../types';
import { StorageUtil } from '../utils/storage';
import { getCurrentUser } from '../api';

// ============================================================================
// 用户模块实现
// ============================================================================

/**
 * 用户模块类
 */
export class UserModule {
  // ------------------------------------------------------------------------
  // 属性定义
  // ------------------------------------------------------------------------

  private config: SDKConfig;
  private cachedUserInfo: CachedUserInfo | null = null;

  // ------------------------------------------------------------------------
  // 构造函数
  // ------------------------------------------------------------------------

  constructor(config: SDKConfig) {
    this.config = config;
  }

  // ------------------------------------------------------------------------
  // 公共方法
  // ------------------------------------------------------------------------

  /**
   * 初始化用户模块
   * 异步获取用户信息并缓存
   */
  async initialize(): Promise<void> {
    try {
      // 先尝试从缓存获取
      const cached = StorageUtil.getUserInfo();
      if (cached) {
        this.cachedUserInfo = cached;
        if (this.config.debug) {
          console.log('[UserModule] 从缓存加载用户信息:', cached);
        }
        return;
      }

      // 缓存不存在或已过期，从 API 获取
      const userInfo = await this.fetchUserInfoFromAPI();

      // 创建缓存对象
      const cachedUserInfo: CachedUserInfo = {
        ...userInfo,
        cachedAt: Date.now(),
        expiresIn: 24 * 60 * 60 * 1000, // 24小时过期
      };

      // 保存到内存和本地存储
      this.cachedUserInfo = cachedUserInfo;
      StorageUtil.saveUserInfo(cachedUserInfo);

      if (this.config.debug) {
        console.log('[UserModule] 从 API 获取并缓存用户信息:', userInfo);
      }
    } catch (error) {
      const errorMessage = `用户模块初始化失败: ${error instanceof Error ? error.message : String(error)}`;
      if (this.config.debug) {
        console.error('[UserModule]', errorMessage, error);
      }
      throw new Error(errorMessage);
    }
  }

  /**
   * 同步获取用户信息
   * 从缓存中返回用户信息，如果缓存不存在则返回 null
   */
  getUserInfo(): User | null {
    if (!this.cachedUserInfo) {
      return null;
    }

    // 检查缓存是否过期
    const now = Date.now();
    if (now > this.cachedUserInfo.cachedAt + this.cachedUserInfo.expiresIn) {
      // 缓存已过期
      this.clearCache();
      return null;
    }

    // 返回用户信息（不包含缓存相关字段）
    const { cachedAt, expiresIn, ...userInfo } = this.cachedUserInfo;
    return userInfo;
  }

  /**
   * 强制刷新用户信息
   * 清除缓存并重新从原生获取
   */
  async refreshUserInfo(): Promise<User> {
    try {
      this.clearCache();

      const userInfo = await this.fetchUserInfoFromAPI();

      // 创建新的缓存对象
      const cachedUserInfo: CachedUserInfo = {
        ...userInfo,
        cachedAt: Date.now(),
        expiresIn: 24 * 60 * 60 * 1000, // 24小时过期
      };

      // 保存到内存和本地存储
      this.cachedUserInfo = cachedUserInfo;
      StorageUtil.saveUserInfo(cachedUserInfo);

      if (this.config.debug) {
        console.log('[UserModule] 刷新用户信息成功:', userInfo);
      }

      return userInfo;
    } catch (error) {
      const errorMessage = `刷新用户信息失败: ${error instanceof Error ? error.message : String(error)}`;
      if (this.config.debug) {
        console.error('[UserModule]', errorMessage, error);
      }
      throw new Error(errorMessage);
    }
  }

  /**
   * 清除用户信息缓存
   */
  clearCache(): void {
    this.cachedUserInfo = null;
    StorageUtil.removeUserInfo();

    if (this.config.debug) {
      console.log('[UserModule] 清除用户信息缓存');
    }
  }

  /**
   * 检查用户信息是否已缓存且有效
   */
  isUserInfoCached(): boolean {
    if (!this.cachedUserInfo) {
      return false;
    }

    const now = Date.now();
    return now <= this.cachedUserInfo.cachedAt + this.cachedUserInfo.expiresIn;
  }

  /**
   * 获取用户 ID
   */
  getUserId(): number | null {
    const userInfo = this.getUserInfo();
    return userInfo?.id ?? null;
  }

  /**
   * 清理模块资源
   */
  cleanup(): void {
    this.cachedUserInfo = null;
  }

  // ------------------------------------------------------------------------
  // 私有方法
  // ------------------------------------------------------------------------

  /**
   * 从 API 获取用户信息
   */
  private async fetchUserInfoFromAPI(): Promise<User> {
    try {
      if (this.config.debug) {
        console.log('[UserModule] 开始获取用户信息...');
      }

      const response = await getCurrentUser();

      if (this.config.debug) {
        console.log('[UserModule] API 响应:', response);
        console.log('[UserModule] response.data:', response.data);
        console.log('[UserModule] response.data?.data:', response.data?.data);
      }

      // 根据 API 规范，响应格式应该是 { data: { code, msg, data: User } }
      if (response.data?.data) {
        return response.data.data;
      } else {
        throw new Error(`API 返回的用户信息格式不正确: ${JSON.stringify(response)}`);
      }
    } catch (error) {
      if (this.config.debug) {
        console.error('[UserModule] API 调用失败:', error);
      }

      const errorMessage = `获取用户信息失败: ${error instanceof Error ? error.message : String(error)}`;
      throw new Error(errorMessage);
    }
  }
}
