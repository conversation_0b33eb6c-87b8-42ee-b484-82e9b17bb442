/**
 * SDK 基础功能测试
 */

import { GameSDK, AdCloseType, SDKEventName } from '../index';

// Mock API 模块
jest.mock('../api', () => ({
  getCurrentUser: jest.fn(),
  reportAdEvent: jest.fn(),
  sendHeartbeat: jest.fn(),
  client: {
    setConfig: jest.fn(),
  },
}));

// Mock window 对象
const mockWindow = global.window as any;

describe('GameSDK', () => {
  let sdk: GameSDK;

  beforeEach(() => {
    // 重置 SDK 实例
    sdk = GameSDK.getInstance();
    
    // 清理 window 对象
    delete mockWindow.DsmJSInterface;
    delete mockWindow.webkit;
    delete mockWindow.closeAd;
    
    // Mock localStorage
    const localStorageMock = {
      getItem: jest.fn(),
      setItem: jest.fn(),
      removeItem: jest.fn(),
      clear: jest.fn(),
    };
    Object.defineProperty(window, 'localStorage', {
      value: localStorageMock,
      writable: true,
    });
  });

  afterEach(() => {
    // 清理 SDK
    sdk.destroy();
  });

  describe('初始化', () => {
    it('应该能够成功初始化 SDK', async () => {
      // Mock 原生环境
      mockWindow.DsmJSInterface = {
        showAd: jest.fn(),
      };

      // Mock API 响应
      const { getCurrentUser } = require('../api');
      getCurrentUser.mockResolvedValue({
        data: {
          data: {
            id: 1,
            username: 'testuser',
            nickname: '测试用户',
            avatar: '/avatar.png',
          },
        },
      });

      const config = {
        appid: '1001',
        channel: '1',
        debug: true,
      };

      await expect(sdk.init(config)).resolves.not.toThrow();
      expect(sdk.getState()).toBe('initialized');
    });

    it('应该在没有原生环境时抛出错误', async () => {
      const config = {
        appid: '1001',
        channel: '1',
        debug: true,
      };

      await expect(sdk.init(config)).rejects.toThrow('未检测到支持的原生环境');
    });
  });

  describe('用户信息', () => {
    beforeEach(async () => {
      // 设置原生环境
      mockWindow.DsmJSInterface = {
        showAd: jest.fn(),
      };

      // Mock API 响应
      const { getCurrentUser } = require('../api');
      getCurrentUser.mockResolvedValue({
        data: {
          data: {
            id: 1,
            username: 'testuser',
            nickname: '测试用户',
            avatar: '/avatar.png',
          },
        },
      });

      await sdk.init({
        appid: '1001',
        channel: '1',
        debug: true,
      });
    });

    it('应该能够获取用户信息', () => {
      const userInfo = sdk.getUserInfo();
      expect(userInfo).toEqual({
        id: 1,
        username: 'testuser',
        nickname: '测试用户',
        avatar: '/avatar.png',
      });
    });

    it('应该能够刷新用户信息', async () => {
      const { getCurrentUser } = require('../api');
      getCurrentUser.mockResolvedValue({
        data: {
          data: {
            id: 1,
            username: 'testuser',
            nickname: '更新的用户',
            avatar: '/new-avatar.png',
          },
        },
      });

      const userInfo = await sdk.refreshUserInfo();
      expect(userInfo.nickname).toBe('更新的用户');
    });
  });

  describe('广告功能', () => {
    beforeEach(async () => {
      // 设置原生环境
      mockWindow.DsmJSInterface = {
        showAd: jest.fn(),
      };

      // Mock API 响应
      const { getCurrentUser } = require('../api');
      getCurrentUser.mockResolvedValue({
        data: {
          data: {
            id: 1,
            username: 'testuser',
            nickname: '测试用户',
            avatar: '/avatar.png',
          },
        },
      });

      await sdk.init({
        appid: '1001',
        channel: '1',
        debug: true,
      });
    });

    it('应该能够显示广告', async () => {
      const callback = jest.fn();
      
      await expect(sdk.showAd(callback)).resolves.not.toThrow();
      expect(mockWindow.DsmJSInterface.showAd).toHaveBeenCalled();
    });

    it('应该能够处理广告关闭回调', async () => {
      const callback = jest.fn();
      
      await sdk.showAd(callback);
      
      // 模拟原生调用广告关闭回调
      if (mockWindow.closeAd) {
        mockWindow.closeAd(AdCloseType.COMPLETED);
      }
      
      expect(callback).toHaveBeenCalledWith(AdCloseType.COMPLETED);
    });

    it('应该能够检查是否可以显示广告', () => {
      expect(sdk.canShowAd()).toBe(true);
    });
  });

  describe('心跳功能', () => {
    beforeEach(async () => {
      // 设置原生环境
      mockWindow.DsmJSInterface = {
        showAd: jest.fn(),
      };

      // Mock API 响应
      const { getCurrentUser, sendHeartbeat } = require('../api');
      getCurrentUser.mockResolvedValue({
        data: {
          data: {
            id: 1,
            username: 'testuser',
            nickname: '测试用户',
            avatar: '/avatar.png',
          },
        },
      });
      sendHeartbeat.mockResolvedValue({ data: { code: 0 } });

      await sdk.init({
        appid: '1001',
        channel: '1',
        debug: true,
      });
    });

    it('应该在 SDK 初始化后自动启动心跳', async () => {
      const { sendHeartbeat } = require('../api');

      // 等待心跳模块启动
      await Promise.resolve();

      expect(sendHeartbeat).toHaveBeenCalled();
    });

    it('应该在调试信息中包含心跳状态', () => {
      const debugInfo = sdk.getDebugInfo();
      expect(debugInfo).toHaveProperty('heartbeatStatus');
      expect(debugInfo.heartbeatStatus).toHaveProperty('state');
      expect(debugInfo.heartbeatStatus).toHaveProperty('heartbeatCount');
      expect(debugInfo.heartbeatStatus).toHaveProperty('uptime');
    });

    it('应该在 SDK 销毁时停止心跳', () => {
      const debugInfoBefore = sdk.getDebugInfo();
      expect(debugInfoBefore.heartbeatStatus.state).toBe('running');

      sdk.destroy();

      // 创建新实例来验证心跳已停止
      const newSdk = GameSDK.getInstance();
      expect(newSdk.getState()).toBe('uninitialized');
    });
  });

  describe('工具方法', () => {
    it('应该返回正确的版本号', () => {
      expect(sdk.getVersion()).toBe('1.0.2');
    });

    it('应该返回调试信息', async () => {
      // Mock 原生环境
      mockWindow.DsmJSInterface = {
        showAd: jest.fn(),
      };

      // Mock API 响应
      const { getCurrentUser, sendHeartbeat } = require('../api');
      getCurrentUser.mockResolvedValue({
        data: {
          data: {
            id: 1,
            username: 'testuser',
            nickname: '测试用户',
            avatar: '/avatar.png',
          },
        },
      });
      sendHeartbeat.mockResolvedValue({ data: { code: 0 } });

      await sdk.init({
        appid: '1001',
        channel: '1',
        debug: true,
      });

      const debugInfo = sdk.getDebugInfo();
      expect(debugInfo).toHaveProperty('state');
      expect(debugInfo).toHaveProperty('version');
      expect(debugInfo).toHaveProperty('heartbeatStatus');
    });
  });
});
