/**
 * 页面导航模块测试
 */

import {
  PageNavigationModule,
  type PageCloseOptions,
  type PageCloseSupport
} from '../modules/PageNavigationModule';

import {
  closePage,
  canClosePage,
  getPageCloseSupport
} from '../index';

import { DsmAdapter } from '../adapters/DsmAdapter';

describe('页面导航模块', () => {
  let mockWindow: any;

  beforeEach(() => {
    // 重置 window 对象
    mockWindow = {};
    Object.defineProperty(global, 'window', {
      value: mockWindow,
      writable: true,
      configurable: true
    });
  });

  afterEach(() => {
    // 清理
    delete (global as any).window;
  });

  describe('closePage', () => {
    it('应该优先使用 iOS webkit.messageHandlers.returnHome 接口', () => {
      // 设置 iOS 和 Android 接口都存在
      mockWindow.webkit = {
        messageHandlers: {
          returnHome: { postMessage: jest.fn() }
        }
      };
      mockWindow.DsmJSInterface = {
        goPreviewPage: jest.fn()
      };

      closePage({ debug: true });

      // 验证 iOS 接口被调用，Android 接口未被调用
      expect(mockWindow.webkit.messageHandlers.returnHome.postMessage).toHaveBeenCalledWith({});
      expect(mockWindow.DsmJSInterface.goPreviewPage).not.toHaveBeenCalled();
    });

    it('应该在没有 iOS 接口时使用 Android DsmJSInterface.goPreviewPage 接口', () => {
      // 只设置 Android 接口
      mockWindow.DsmJSInterface = {
        goPreviewPage: jest.fn()
      };

      closePage({ debug: true });

      // 验证 Android 接口被调用
      expect(mockWindow.DsmJSInterface.goPreviewPage).toHaveBeenCalled();
    });

    it('应该在没有任何原生接口时抛出错误', () => {
      // 不设置任何原生接口
      mockWindow = {};
      Object.defineProperty(global, 'window', {
        value: mockWindow,
        writable: true,
        configurable: true
      });

      expect(() => closePage({ debug: true })).toThrow('未检测到原生环境，无法关闭页面');
    });

    it('应该在调试模式下输出日志', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      
      mockWindow.webkit = {
        messageHandlers: {
          returnHome: { postMessage: jest.fn() }
        }
      };

      closePage({ debug: true });

      expect(consoleSpy).toHaveBeenCalledWith('[PageNavigationModule] 开始执行页面关闭操作');
      expect(consoleSpy).toHaveBeenCalledWith('[PageNavigationModule] 页面关闭操作完成');

      consoleSpy.mockRestore();
    });

    it('应该在非调试模式下不输出日志', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      
      mockWindow.webkit = {
        messageHandlers: {
          returnHome: { postMessage: jest.fn() }
        }
      };

      closePage({ debug: false });

      expect(consoleSpy).not.toHaveBeenCalled();

      consoleSpy.mockRestore();
    });

    it('应该在默认情况下不输出日志', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      
      mockWindow.webkit = {
        messageHandlers: {
          returnHome: { postMessage: jest.fn() }
        }
      };

      closePage(); // 不传递参数

      expect(consoleSpy).not.toHaveBeenCalled();

      consoleSpy.mockRestore();
    });
  });

  describe('canClosePage', () => {
    it('应该在有 iOS 接口时返回 true', () => {
      mockWindow.webkit = {
        messageHandlers: {
          returnHome: { postMessage: jest.fn() }
        }
      };

      expect(canClosePage()).toBe(true);
    });

    it('应该在有 Android 接口时返回 true', () => {
      mockWindow.DsmJSInterface = {
        goPreviewPage: jest.fn()
      };

      expect(canClosePage()).toBe(true);
    });

    it('应该在有任一接口时返回 true', () => {
      mockWindow.webkit = {
        messageHandlers: {
          returnHome: { postMessage: jest.fn() }
        }
      };
      mockWindow.DsmJSInterface = {
        goPreviewPage: jest.fn()
      };

      expect(canClosePage()).toBe(true);
    });

    it('应该在没有任何接口时返回 false', () => {
      expect(canClosePage()).toBe(false);
    });
  });

  describe('getPageCloseSupport', () => {
    it('应该正确检测 iOS 支持', () => {
      mockWindow.webkit = {
        messageHandlers: {
          returnHome: { postMessage: jest.fn() }
        }
      };

      const support = getPageCloseSupport();
      expect(support).toEqual({
        supported: true,
        ios: true,
        android: false,
        preferredInterface: 'ios'
      });
    });

    it('应该正确检测 Android 支持', () => {
      mockWindow.DsmJSInterface = {
        goPreviewPage: jest.fn()
      };

      const support = getPageCloseSupport();
      expect(support).toEqual({
        supported: true,
        ios: false,
        android: true,
        preferredInterface: 'android'
      });
    });

    it('应该正确检测双平台支持并优先选择 iOS', () => {
      mockWindow.webkit = {
        messageHandlers: {
          returnHome: { postMessage: jest.fn() }
        }
      };
      mockWindow.DsmJSInterface = {
        goPreviewPage: jest.fn()
      };

      const support = getPageCloseSupport();
      expect(support).toEqual({
        supported: true,
        ios: true,
        android: true,
        preferredInterface: 'ios'
      });
    });

    it('应该正确检测无支持情况', () => {
      const support = getPageCloseSupport();
      expect(support).toEqual({
        supported: false,
        ios: false,
        android: false,
        preferredInterface: null
      });
    });
  });

  describe('PageNavigationModule 类', () => {
    it('应该能够创建实例', () => {
      const adapter = new DsmAdapter();
      const module = new PageNavigationModule(adapter);
      expect(module).toBeInstanceOf(PageNavigationModule);
    });

    it('应该能够配置调试模式', () => {
      const adapter = new DsmAdapter();
      const module = new PageNavigationModule(adapter, { debug: true });
      expect(module).toBeInstanceOf(PageNavigationModule);
    });

    it('应该能够通过模块实例关闭页面', () => {
      mockWindow.webkit = {
        messageHandlers: {
          returnHome: { postMessage: jest.fn() }
        }
      };

      const adapter = new DsmAdapter();
      const module = new PageNavigationModule(adapter, { debug: true });
      module.closePage();

      expect(mockWindow.webkit.messageHandlers.returnHome.postMessage).toHaveBeenCalledWith({});
    });

    it('应该能够检查页面关闭支持', () => {
      mockWindow.webkit = {
        messageHandlers: {
          returnHome: { postMessage: jest.fn() }
        }
      };

      const adapter = new DsmAdapter();
      const module = new PageNavigationModule(adapter);
      expect(module.canClosePage()).toBe(true);
    });

    it('应该能够获取页面关闭支持信息', () => {
      mockWindow.webkit = {
        messageHandlers: {
          returnHome: { postMessage: jest.fn() }
        }
      };

      const adapter = new DsmAdapter();
      const module = new PageNavigationModule(adapter);
      const support = module.getPageCloseSupport();
      expect(support.supported).toBe(true);
      expect(support.ios).toBe(true);
      expect(support.preferredInterface).toBe('ios');
    });

    it('应该能够更新配置', () => {
      const adapter = new DsmAdapter();
      const module = new PageNavigationModule(adapter, { debug: false });
      module.updateConfig({ debug: true });
      // 配置更新应该成功，不抛出错误
      expect(module).toBeInstanceOf(PageNavigationModule);
    });

    it('应该能够清理资源', () => {
      const adapter = new DsmAdapter();
      const module = new PageNavigationModule(adapter);
      // 清理应该成功，不抛出错误
      expect(() => module.cleanup()).not.toThrow();
    });
  });
});
