/**
 * 心跳模块测试
 */

import { HeartbeatModule } from '../modules/HeartbeatModule';
import type { SDKConfig } from '../types';

// Mock API 模块
jest.mock('../api', () => ({
  sendHeartbeat: jest.fn(),
}));

// Mock timers
jest.useFakeTimers();

describe('HeartbeatModule', () => {
  let heartbeatModule: HeartbeatModule;
  let mockConfig: SDKConfig;

  beforeEach(() => {
    mockConfig = {
      appid: '1001',
      channel: '1',
      debug: true,
    };

    heartbeatModule = new HeartbeatModule(mockConfig);

    // 清除所有 mock
    jest.clearAllMocks();
  });

  afterEach(() => {
    heartbeatModule.cleanup();
    jest.clearAllTimers();
  });

  describe('初始化和启动', () => {
    it('应该能够创建心跳模块实例', () => {
      expect(heartbeatModule).toBeInstanceOf(HeartbeatModule);
    });

    it('应该能够启动心跳', () => {
      const { sendHeartbeat } = require('../api');
      sendHeartbeat.mockResolvedValue({ data: { code: 0 } });

      heartbeatModule.start();

      const status = heartbeatModule.getStatus();
      expect(status.state).toBe('running');
      expect(status.heartbeatCount).toBe(1); // 第一次心跳立即发送
    });

    it('应该立即发送第一次心跳', async () => {
      const { sendHeartbeat } = require('../api');
      sendHeartbeat.mockResolvedValue({ data: { code: 0 } });

      heartbeatModule.start();

      // 等待第一次心跳发送
      await Promise.resolve();

      expect(sendHeartbeat).toHaveBeenCalledTimes(1);
      expect(sendHeartbeat).toHaveBeenCalledWith({
        body: {
          custom_data: expect.objectContaining({
            heartbeat_count: 1,
            uptime_seconds: expect.any(Number),
            sdk_version: '1.0.4',
            timestamp: expect.any(Number),
          }),
        },
      });
    });

    it('应该防止重复启动', () => {
      const { sendHeartbeat } = require('../api');
      sendHeartbeat.mockResolvedValue({ data: { code: 0 } });

      heartbeatModule.start();
      heartbeatModule.start(); // 重复启动

      const status = heartbeatModule.getStatus();
      expect(status.state).toBe('running');
    });
  });

  describe('定时心跳', () => {
    it('应该每30秒发送一次心跳', async () => {
      const { sendHeartbeat } = require('../api');
      sendHeartbeat.mockResolvedValue({ data: { code: 0 } });

      heartbeatModule.start();

      // 等待第一次心跳
      await Promise.resolve();
      expect(sendHeartbeat).toHaveBeenCalledTimes(1);

      // 快进30秒
      jest.advanceTimersByTime(30000);
      await Promise.resolve();
      expect(sendHeartbeat).toHaveBeenCalledTimes(2);

      // 再快进30秒
      jest.advanceTimersByTime(30000);
      await Promise.resolve();
      expect(sendHeartbeat).toHaveBeenCalledTimes(3);
    });

    it('应该正确递增心跳计数', async () => {
      const { sendHeartbeat } = require('../api');
      sendHeartbeat.mockResolvedValue({ data: { code: 0 } });

      heartbeatModule.start();

      // 等待第一次心跳
      await Promise.resolve();

      // 快进并等待第二次心跳
      jest.advanceTimersByTime(30000);
      await Promise.resolve();

      // 检查第二次调用的参数
      expect(sendHeartbeat).toHaveBeenNthCalledWith(2, {
        body: {
          custom_data: expect.objectContaining({
            heartbeat_count: 2,
          }),
        },
      });
    });
  });

  describe('错误处理', () => {
    it('应该静默处理心跳发送失败', async () => {
      const { sendHeartbeat } = require('../api');
      sendHeartbeat.mockRejectedValue(new Error('网络错误'));

      // 不应该抛出错误
      expect(() => heartbeatModule.start()).not.toThrow();

      // 等待错误处理
      await Promise.resolve();
      await Promise.resolve(); // 额外等待确保异步错误处理完成

      const status = heartbeatModule.getStatus();
      expect(status.state).toBe('error');
    });

    it('应该在错误后继续尝试发送心跳', async () => {
      const { sendHeartbeat } = require('../api');
      sendHeartbeat.mockRejectedValueOnce(new Error('网络错误'))
                  .mockResolvedValue({ data: { code: 0 } });

      heartbeatModule.start();

      // 等待第一次失败的心跳
      await Promise.resolve();
      await Promise.resolve(); // 额外等待确保异步错误处理完成
      expect(sendHeartbeat).toHaveBeenCalledTimes(1);

      // 快进30秒，应该恢复正常状态并继续发送
      jest.advanceTimersByTime(30000);
      await Promise.resolve();

      const status = heartbeatModule.getStatus();
      expect(status.state).toBe('running');
      expect(sendHeartbeat).toHaveBeenCalledTimes(2);
    });
  });

  describe('停止和清理', () => {
    it('应该能够停止心跳', () => {
      const { sendHeartbeat } = require('../api');
      sendHeartbeat.mockResolvedValue({ data: { code: 0 } });

      heartbeatModule.start();
      heartbeatModule.stop();

      const status = heartbeatModule.getStatus();
      expect(status.state).toBe('stopped');
    });

    it('应该在停止后不再发送心跳', async () => {
      const { sendHeartbeat } = require('../api');
      sendHeartbeat.mockResolvedValue({ data: { code: 0 } });

      heartbeatModule.start();
      await Promise.resolve(); // 等待第一次心跳

      heartbeatModule.stop();

      // 快进30秒，不应该再发送心跳
      jest.advanceTimersByTime(30000);
      await Promise.resolve();

      expect(sendHeartbeat).toHaveBeenCalledTimes(1); // 只有第一次
    });

    it('应该能够清理资源', () => {
      const { sendHeartbeat } = require('../api');
      sendHeartbeat.mockResolvedValue({ data: { code: 0 } });

      heartbeatModule.start();
      heartbeatModule.cleanup();

      const status = heartbeatModule.getStatus();
      expect(status.state).toBe('stopped');
      expect(status.heartbeatCount).toBe(0);
      expect(status.uptime).toBe(0);
    });
  });

  describe('状态查询', () => {
    it('应该返回正确的状态信息', async () => {
      const { sendHeartbeat } = require('../api');
      sendHeartbeat.mockResolvedValue({ data: { code: 0 } });

      const initialStatus = heartbeatModule.getStatus();
      expect(initialStatus.state).toBe('stopped');
      expect(initialStatus.heartbeatCount).toBe(0);
      expect(initialStatus.uptime).toBe(0);

      heartbeatModule.start();
      await Promise.resolve();

      // 快进一些时间以确保有uptime
      jest.advanceTimersByTime(1000);

      const runningStatus = heartbeatModule.getStatus();
      expect(runningStatus.state).toBe('running');
      expect(runningStatus.heartbeatCount).toBe(1);
      expect(runningStatus.uptime).toBeGreaterThan(0);
    });
  });
});
