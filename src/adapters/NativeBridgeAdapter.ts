/**
 * 原生桥接适配器接口
 *
 * 这个接口定义了 SDK 与原生应用交互的标准方法。
 * 不同的客户可以实现这个接口来适配他们的原生应用。
 */

// ============================================================================
// 导入依赖
// ============================================================================

import type { AdCloseCallback, NativeEnvironment } from '../types';
import { AdCloseType } from '../types';

// ============================================================================
// 接口定义
// ============================================================================

/**
 * 原生桥接适配器接口
 */
export interface NativeBridgeAdapter {
  /**
   * 检测原生环境
   * @returns 原生环境信息
   */
  detectNativeEnvironment(): NativeEnvironment;

  /**
   * 显示广告
   * @param callback 广告关闭时的回调函数
   * @throws {Error} 当显示广告失败时抛出错误
   */
  showAd(callback: AdCloseCallback): void;

  /**
   * 关闭页面/返回上一页
   * @throws {Error} 当关闭页面失败时抛出错误
   */
  closePage(): void;

  /**
   * 设置广告关闭回调
   * @param callback 广告关闭回调函数
   */
  setAdCloseCallback(callback: AdCloseCallback): void;

  /**
   * 清理资源
   * 在 SDK 销毁时调用，用于清理事件监听器等资源
   */
  cleanup(): void;
}

// ============================================================================
// 抽象基类
// ============================================================================

/**
 * 抽象适配器基类
 * 提供一些通用的实现和工具方法
 */
export abstract class BaseNativeBridgeAdapter implements NativeBridgeAdapter {
  // ------------------------------------------------------------------------
  // 属性定义
  // ------------------------------------------------------------------------

  protected adCloseCallback?: AdCloseCallback | undefined;
  protected debug: boolean = false;

  // ------------------------------------------------------------------------
  // 抽象方法 (子类必须实现)
  // ------------------------------------------------------------------------

  abstract detectNativeEnvironment(): NativeEnvironment;
  abstract showAd(callback: AdCloseCallback): void;
  abstract closePage(): void;

  // ------------------------------------------------------------------------
  // 公共方法
  // ------------------------------------------------------------------------

  /**
   * 设置调试模式
   */
  setDebugMode(debug: boolean): void {
    this.debug = debug;
  }

  /**
   * 设置广告关闭回调
   */
  setAdCloseCallback(callback: AdCloseCallback): void {
    this.adCloseCallback = callback;
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.adCloseCallback = undefined;
  }

  // ------------------------------------------------------------------------
  // 受保护的工具方法
  // ------------------------------------------------------------------------

  /**
   * 触发广告关闭回调
   */
  protected triggerAdCloseCallback(type: number | string): void {
    if (this.debug) {
      console.log(`[NativeBridgeAdapter] 收到广告关闭回调: ${type} (类型: ${typeof type})`);
      console.log(`[NativeBridgeAdapter] 当前回调函数存在: ${!!this.adCloseCallback}`);
    }

    if (!this.adCloseCallback) {
      if (this.debug) {
        console.warn(`[NativeBridgeAdapter] 广告关闭回调函数不存在，忽略回调`);
      }
      return;
    }

    // 将原生传入的数字或字符串类型转换为 AdCloseType 枚举类型
    let adCloseType: AdCloseType;

    if (typeof type === 'string') {
      // 如果是字符串，转换为数字
      const numericType = parseInt(type, 10);
      if (isNaN(numericType)) {
        if (this.debug) {
          console.warn(`[NativeBridgeAdapter] 无效的广告关闭类型: ${type}`);
        }
        return;
      }
      adCloseType = numericType as AdCloseType;
    } else {
      // 如果是数字，直接转换
      adCloseType = type as AdCloseType;
    }

    // 验证类型是否有效
    if (adCloseType !== AdCloseType.COMPLETED && adCloseType !== AdCloseType.CANCELLED) {
      if (this.debug) {
        console.warn(`[NativeBridgeAdapter] 未知的广告关闭类型: ${type} (转换后: ${adCloseType})`);
      }
      // 对于未知类型，默认当作取消处理
      adCloseType = AdCloseType.CANCELLED;
    }

    try {
      this.adCloseCallback(adCloseType);
      if (this.debug) {
        console.log(`[NativeBridgeAdapter] 广告关闭回调执行成功: ${adCloseType}`);
      }
    } catch (error) {
      if (this.debug) {
        console.error(`[NativeBridgeAdapter] 广告关闭回调执行失败:`, error);
      }
    }
  }

  /**
   * 检查是否为 Android 环境
   */
  protected isAndroidEnvironment(): boolean {
    return !!(window as any).DsmJSInterface?.showAd;
  }

  /**
   * 检查是否为 iOS 环境
   */
  protected isIOSEnvironment(): boolean {
    return !!(window as any).webkit?.messageHandlers?.showAd;
  }

  /**
   * 检查是否支持页面关闭功能
   */
  protected hasPageCloseSupport(): boolean {
    const hasIOSReturnHome = !!(window as any).webkit?.messageHandlers?.returnHome;
    const hasAndroidGoPreview = !!(window as any).DsmJSInterface?.goPreviewPage;
    return hasIOSReturnHome || hasAndroidGoPreview;
  }

  /**
   * 检查是否有原生支持
   */
  protected hasNativeSupport(): boolean {
    return this.isAndroidEnvironment() || this.isIOSEnvironment();
  }
}
