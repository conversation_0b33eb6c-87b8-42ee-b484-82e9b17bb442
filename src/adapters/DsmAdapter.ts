/**
 * DSM 客户端适配器实现
 *
 * 基于 H5交互文档.md 实现的具体适配器，用于与当前客户的原生应用交互。
 * 支持跨 iframe 的回调处理。
 */

// ============================================================================
// 导入依赖
// ============================================================================

import type { AdCloseCallback, NativeEnvironment } from '../types';
import { BaseNativeBridgeAdapter } from './NativeBridgeAdapter';

// ============================================================================
// 类型声明
// ============================================================================

/**
 * DSM 客户端的原生接口声明
 */
declare global {
  interface Window {
    DsmJSInterface?: {
      showAd(): void;
      goPreviewPage(): void;
    };
    webkit?: {
      messageHandlers?: {
        showAd?: {
          postMessage(data: { body: string }): void;
        };
        returnHome?: {
          postMessage(data: Record<string, unknown>): void;
        };
      };
    };
    closeAd?: (type: number | string) => void;
  }
}

// ============================================================================
// DSM 适配器实现
// ============================================================================

/**
 * DSM 适配器实现
 */
export class DsmAdapter extends BaseNativeBridgeAdapter {
  // ------------------------------------------------------------------------
  // 静态常量
  // ------------------------------------------------------------------------

  private static readonly CROSS_IFRAME_KEY = '__ANYIGAME_AD_SDK_CALLBACK__';

  // ------------------------------------------------------------------------
  // 构造函数
  // ------------------------------------------------------------------------

  constructor() {
    super();
    this.setupGlobalCallbacks();
  }

  // ------------------------------------------------------------------------
  // 公共方法 (实现接口)
  // ------------------------------------------------------------------------

  /**
   * 检测原生环境
   */
  detectNativeEnvironment(): NativeEnvironment {
    const isAndroid = this.isAndroidEnvironment();
    const isIOS = this.isIOSEnvironment();
    const hasNativeSupport = this.hasNativeSupport();

    return {
      isAndroid,
      isIOS,
      hasNativeSupport,
    };
  }

  /**
   * 显示广告
   */
  showAd(callback: AdCloseCallback): void {
    const env = this.detectNativeEnvironment();

    if (!env.hasNativeSupport) {
      throw new Error('未检测到原生环境，无法显示广告');
    }

    // 设置回调
    this.setAdCloseCallback(callback);

    if (this.debug) {
      console.log('[DsmAdapter] 开始调用原生广告显示方法');
      console.log('[DsmAdapter] 环境信息:', env);
    }

    try {
      if (env.isAndroid) {
        // Android 环境
        if (this.debug) {
          console.log('[DsmAdapter] 调用 Android 原生方法: window.DsmJSInterface.showAd()');
        }
        window.DsmJSInterface?.showAd();
      } else if (env.isIOS) {
        // iOS 环境
        if (this.debug) {
          console.log('[DsmAdapter] 调用 iOS 原生方法: webkit.messageHandlers.showAd.postMessage()');
        }
        window.webkit?.messageHandlers?.showAd?.postMessage({ body: '' });
      }

      if (this.debug) {
        console.log('[DsmAdapter] 原生广告显示方法调用完成');
      }
    } catch (error) {
      if (this.debug) {
        console.error('[DsmAdapter] 调用原生广告显示方法失败:', error);
      }
      throw new Error(`显示广告失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 关闭页面/返回上一页
   */
  closePage(): void {
    if (this.debug) {
      console.log('[DsmAdapter] 开始执行页面关闭操作');
    }

    // 优先使用 iOS webkit.messageHandlers.returnHome 接口
    if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.returnHome) {
      if (this.debug) {
        console.log('[DsmAdapter] 使用 webkit.messageHandlers.returnHome 接口关闭');
      }
      try {
        window.webkit.messageHandlers.returnHome.postMessage({});
        return;
      } catch (error) {
        if (this.debug) {
          console.error('[DsmAdapter] webkit.messageHandlers.returnHome 调用失败:', error);
        }
      }
    }

    if (this.debug) {
      console.log('[DsmAdapter] DsmJSInterface 接口检查:', {
        hasDsmJSInterface: !!window.DsmJSInterface,
        hasGoPreviewPage: !!(window.DsmJSInterface && window.DsmJSInterface.goPreviewPage)
      });
    }

    // 使用 Android DsmJSInterface.goPreviewPage 接口
    if (window.DsmJSInterface && window.DsmJSInterface.goPreviewPage) {
      if (this.debug) {
        console.log('[DsmAdapter] 使用 DsmJSInterface.goPreviewPage 接口关闭');
      }
      try {
        window.DsmJSInterface.goPreviewPage();
        return;
      } catch (error) {
        if (this.debug) {
          console.error('[DsmAdapter] DsmJSInterface.goPreviewPage 调用失败:', error);
        }
      }
    }

    // 如果没有可用的原生接口
    if (this.debug) {
      console.warn('[DsmAdapter] 未找到可用的页面关闭接口');
    }
    throw new Error('未检测到原生环境，无法关闭页面');
  }

  /**
   * 清理资源
   */
  override cleanup(): void {
    super.cleanup();

    // 清理全局回调
    const targetWindow = this.getTargetWindow();
    if (targetWindow.closeAd) {
      delete targetWindow.closeAd;
    }

    // 清理跨 iframe 回调引用
    try {
      if (window.top && window !== window.top) {
        delete (window.top as any)[DsmAdapter.CROSS_IFRAME_KEY];
      }
      if ((window as any)[DsmAdapter.CROSS_IFRAME_KEY]) {
        delete (window as any)[DsmAdapter.CROSS_IFRAME_KEY];
      }
    } catch (error) {
      // 跨域访问可能失败，忽略错误
      if (this.debug) {
        console.warn('[DsmAdapter] 清理跨 iframe 回调引用失败:', error);
      }
    }
  }

  // ------------------------------------------------------------------------
  // 私有方法 (内部实现)
  // ------------------------------------------------------------------------

  /**
   * 设置全局回调函数
   * 支持跨 iframe 的回调处理
   */
  private setupGlobalCallbacks(): void {
    // 获取目标窗口（优先使用顶层窗口）
    const targetWindow = this.getTargetWindow();

    // 设置广告关闭回调
    targetWindow.closeAd = (type: number | string) => {
      if (this.debug) {
        console.log(`[DsmAdapter] window.closeAd 被调用: ${type}`);
        console.log(`[DsmAdapter] 当前窗口是顶层窗口: ${window === window.top}`);
        console.log(`[DsmAdapter] 当前 adCloseCallback 存在: ${!!this.adCloseCallback}`);
      }

      // 如果在顶层窗口但回调不存在，尝试查找跨 iframe 回调
      if (!this.adCloseCallback && window === window.top) {
        this.handleCrossIframeCallback(type);
        return;
      }

      // 确保回调函数存在
      if (!this.adCloseCallback) {
        console.warn('[DsmAdapter] 广告关闭回调函数不存在，可能是状态管理问题');
        return;
      }

      this.triggerAdCloseCallback(type);
    };

    // 如果当前不是顶层窗口，在顶层窗口也设置回调引用
    if (window !== window.top && window.top) {
      this.setupCrossIframeCallback();
    }
  }

  /**
   * 获取目标窗口（用于设置回调）
   */
  private getTargetWindow(): Window {
    // 优先在顶层窗口设置回调，这样原生调用更容易访问
    return window.top || window;
  }

  /**
   * 设置跨 iframe 回调引用
   */
  private setupCrossIframeCallback(): void {
    try {
      if (window.top) {
        // 在顶层窗口存储当前 iframe 的回调引用
        (window.top as any)[DsmAdapter.CROSS_IFRAME_KEY] = {
          callback: (type: number | string) => {
            if (this.adCloseCallback) {
              this.triggerAdCloseCallback(type);
            }
          },
          debug: this.debug
        };
      }
    } catch (error) {
      // 跨域访问可能失败，忽略错误
      if (this.debug) {
        console.warn('[DsmAdapter] 无法设置跨 iframe 回调引用:', error);
      }
    }
  }

  /**
   * 处理跨 iframe 回调
   */
  private handleCrossIframeCallback(type: number | string): void {
    try {
      const crossIframeCallback = (window as any)[DsmAdapter.CROSS_IFRAME_KEY];
      if (crossIframeCallback && crossIframeCallback.callback) {
        if (this.debug) {
          console.log('[DsmAdapter] 使用跨 iframe 回调处理广告关闭');
        }
        crossIframeCallback.callback(type);
      } else {
        if (this.debug) {
          console.warn('[DsmAdapter] 未找到跨 iframe 回调');
        }
      }
    } catch (error) {
      if (this.debug) {
        console.error('[DsmAdapter] 跨 iframe 回调处理失败:', error);
      }
    }
  }
}
