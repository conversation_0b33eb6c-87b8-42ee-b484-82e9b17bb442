/**
 * SDK 核心类型定义
 */

import type { User, AdEvent } from '../api';

/**
 * SDK 初始化配置
 */
export interface SDKConfig {
  /**
   * 应用/游戏标识符（数字字符串）
   * 必须为纯数字格式，如 "1001"
   * 对应 API 中的 code 参数
   */
  appid: string;
  /**
   * 渠道代码（数字字符串）
   * 必须为纯数字格式，如 "1"
   * 对应 API 中的 X-Channel-Code 头部参数
   */
  channel: string;
  /**
   * 是否启用调试模式
   * 启用后将输出详细的日志信息
   * @default false
   */
  debug?: boolean;
  /**
   * API 请求超时时间（毫秒）
   * @default 10000
   */
  timeout?: number;
  /**
   * 事件上报重试次数
   * @default 3
   */
  maxRetries?: number;
  /**
   * 事件上报批次大小
   * 单次请求最大事件数量，不能超过 10
   * @default 10
   */
  batchSize?: number;
  /**
   * 事件上报间隔时间（毫秒）
   * @default 5000
   */
  reportInterval?: number;
}

/**
 * 用户信息缓存结构
 */
export interface CachedUserInfo extends User {
  /** 缓存时间戳 */
  cachedAt: number;
  /** 缓存有效期（毫秒） */
  expiresIn: number;
}

/**
 * 广告关闭类型
 */
export enum AdCloseType {
  /** 观看完成 */
  COMPLETED = 1,
  /** 用户取消 */
  CANCELLED = 2,
}

/**
 * 广告关闭回调函数类型
 */
export type AdCloseCallback = (type: AdCloseType) => void;

/**
 * SDK 事件名称枚举
 */
export enum SDKEventName {
  /** 开始初始化 SDK */
  SDK_INIT_START = 'sdk_init_start',
  /** SDK 初始化成功 */
  SDK_INIT_SUCCESS = 'sdk_init_success',
  /** SDK 初始化失败 */
  SDK_INIT_FAILED = 'sdk_init_failed',
  /** 广告请求 */
  AD_REQUEST = 'ad_request',
  /** 广告展示成功 */
  AD_IMPRESSION = 'ad_impression',
  /** 广告展示失败 */
  AD_IMPRESSION_FAILED = 'ad_impression_failed',
  /** 广告关闭 */
  AD_CLOSE = 'ad_close',
  /** 广告奖励发放 */
  AD_REWARD_GRANT = 'ad_reward_grant',
}

/**
 * 事件上报数据
 */
export interface EventData extends Omit<AdEvent, 'event_time' | 'client_event_id'> {
  /** 自定义数据 */
  custom_data?: Record<string, unknown>;
}

/**
 * SDK 错误类型
 */
export class SDKError extends Error {
  constructor(
    message: string,
    public code?: number,
    public originalError?: Error
  ) {
    super(message);
    this.name = 'SDKError';
  }
}

/**
 * 原生环境检测结果
 */
export interface NativeEnvironment {
  /** 是否为 Android 环境 */
  isAndroid: boolean;
  /** 是否为 iOS 环境 */
  isIOS: boolean;
  /** 是否检测到原生环境 */
  hasNativeSupport: boolean;
}

/**
 * SDK 配置验证工具
 */
export class SDKConfigValidator {
  /**
   * 验证应用标识符格式
   * @param appid 应用标识符
   * @returns 是否有效
   */
  static isValidAppId(appid: string): boolean {
    return /^[0-9]+$/.test(appid);
  }

  /**
   * 验证渠道代码格式
   * @param channelCode 渠道代码
   * @returns 是否有效
   */
  static isValidChannelCode(channelCode: string): boolean {
    return /^[0-9]+$/.test(channelCode);
  }

  /**
   * 验证 SDK 配置
   * @param config SDK 配置
   * @throws {SDKError} 当配置无效时抛出错误
   */
  static validateConfig(config: SDKConfig): void {
    if (!config.appid || !this.isValidAppId(config.appid)) {
      throw new SDKError('应用标识符必须为数字字符串格式，如 "1001"');
    }

    if (!config.channel || !this.isValidChannelCode(config.channel)) {
      throw new SDKError('渠道代码必须为数字字符串格式，如 "1"');
    }

    if (config.timeout !== undefined && (config.timeout <= 0 || config.timeout > 60000)) {
      throw new SDKError('超时时间必须在 1-60000 毫秒之间');
    }

    if (config.maxRetries !== undefined && (config.maxRetries < 0 || config.maxRetries > 10)) {
      throw new SDKError('重试次数必须在 0-10 之间');
    }

    if (config.batchSize !== undefined && (config.batchSize < 1 || config.batchSize > 10)) {
      throw new SDKError('批次大小必须在 1-10 之间');
    }

    if (config.reportInterval !== undefined && (config.reportInterval < 1000 || config.reportInterval > 60000)) {
      throw new SDKError('上报间隔必须在 1000-60000 毫秒之间');
    }
  }

  /**
   * 获取配置的默认值
   * @param config 用户配置
   * @returns 合并默认值后的完整配置
   */
  static getConfigWithDefaults(config: SDKConfig): Required<SDKConfig> {
    return {
      appid: config.appid,
      channel: config.channel,
      debug: config.debug ?? false,
      timeout: config.timeout ?? 10000,
      maxRetries: config.maxRetries ?? 3,
      batchSize: config.batchSize ?? 10,
      reportInterval: config.reportInterval ?? 5000,
    };
  }
}

// ============================================================================
// 玩家数据备份相关类型
// ============================================================================

/**
 * 备份类型枚举
 * 使用字符串枚举以符合用户偏好
 */
export enum BackupType {
  /** 手动备份 */
  MANUAL = 'manual',
  /** 自动备份 */
  AUTO = 'auto',
  /** 关键节点备份 */
  CHECKPOINT = 'checkpoint',
}

/**
 * 备份类型到数字的映射（用于API调用）
 */
export const BackupTypeToNumber: Record<BackupType, number> = {
  [BackupType.MANUAL]: 1,
  [BackupType.AUTO]: 2,
  [BackupType.CHECKPOINT]: 3,
};

/**
 * 数字到备份类型的映射（用于API响应解析）
 */
export const NumberToBackupType: Record<number, BackupType> = {
  1: BackupType.MANUAL,
  2: BackupType.AUTO,
  3: BackupType.CHECKPOINT,
};

/**
 * 玩家数据备份选项
 */
export interface PlayerDataBackupOptions {
  /** 备份类型 */
  backupType?: BackupType;
  /** 备份描述 */
  description?: string;
  /** 设备信息 */
  deviceInfo?: Record<string, unknown>;
}

/**
 * 玩家数据备份结果
 */
export interface PlayerDataBackupResult {
  /** 备份ID */
  backupId: number;
  /** 备份键名 */
  backupKey: string;
  /** 数据大小（字节） */
  dataSize: number;
  /** 备份类型 */
  backupType: BackupType;
  /** 备份类型描述 */
  backupTypeDescription: string;
  /** 创建时间 */
  createdAt: string;
  /** 数据校验和 */
  checksum: string;
}

/**
 * 玩家数据检索结果
 */
export interface PlayerDataRetrieveResult {
  /** 备份键名 */
  backupKey: string;
  /** 备份数据 */
  backupData: Record<string, unknown>;
  /** 数据版本号 */
  dataVersion: number;
  /** 备份类型 */
  backupType: BackupType;
  /** 备份类型描述 */
  backupTypeDescription: string;
  /** 备份描述 */
  description?: string | undefined;
  /** 创建时间 */
  createdAt: string;
  /** 更新时间 */
  updatedAt: string;
  /** 数据大小（字节） */
  dataSize: number;
}

/**
 * 玩家数据统计信息
 */
export interface PlayerDataStats {
  /** 总备份数量 */
  totalBackups: number;
  /** 总数据大小（字节） */
  totalDataSize: number;
  /** 按备份类型分组的统计 */
  backupTypes: Record<BackupType, number>;
  /** 最新备份时间 */
  latestBackup?: string | undefined;
}
