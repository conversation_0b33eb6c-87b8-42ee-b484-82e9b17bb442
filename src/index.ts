/**
 * @anyigame/ad-sdk
 *
 * H5 游戏 SDK，用于桥接 H5 与原生应用环境
 * 核心功能包括广告展示、用户系统
 *
 * <AUTHOR>
 * @version 1.0.4
 */

// ============================================================================
// 类型和枚举导出
// ============================================================================

// 基础类型定义
export type {
  SDKConfig,
  AdCloseCallback,
  CachedUserInfo,
  NativeEnvironment,
  PlayerDataBackupOptions,
  PlayerDataBackupResult,
  PlayerDataRetrieveResult,
  PlayerDataStats,
} from './types';

// 枚举和常量
export {
  AdCloseType,
  BackupType,
  SDKError,
  SDKConfigValidator,
} from './types';

// API 类型（从生成的文件中重新导出）
export type {
  User,
  UserResponse,
  GameConfig,
  GameConfigResponse,
  PlayerBackupData,
  PlayerDataBackupRequest,
  PlayerDataBackupResponse,
  PlayerDataRetrieveResponse,
  PlayerDataRetrieveAllResponse,
  PlayerDataStatsResponse,
} from './api';

// ============================================================================
// 核心 SDK 类、适配器和跨 iframe 管理器导出
// ============================================================================

// 核心 SDK 类
export { GameSDK } from './core/SDK';
import { GameSDK } from './core/SDK';

// 适配器接口和基类
export type { NativeBridgeAdapter } from './adapters/NativeBridgeAdapter';
export { BaseNativeBridgeAdapter } from './adapters/NativeBridgeAdapter';

// 具体适配器实现
export { DsmAdapter } from './adapters/DsmAdapter';

// 跨 iframe 管理器和代理类
export { CrossIframeSDKManager, CrossIframeSDKProxy } from './utils/CrossIframeSDK';

// ============================================================================
// 工具函数导出
// ============================================================================

// 页面导航工具函数
export {
  canClosePage,
  getPageCloseSupport
} from './utils/pageNavigation';

// 导入页面关闭工具函数用于重新导出
import { closePage as closePageUtil } from './utils/pageNavigation';

// ============================================================================
// SDK 实例和核心功能
// ============================================================================

// 创建默认 SDK 实例
const sdk = GameSDK.getInstance();

/**
 * 初始化 SDK
 *
 * @param config SDK 配置
 * @example
 * ```typescript
 * import { init, AdCloseType } from '@anyigame/ad-sdk';
 *
 * // 初始化 SDK
 * await init({
 *   appid: '1001',
 *   channel: '1',
 *   debug: true
 * });
 *
 * // 显示广告
 * await showAd((type) => {
 *   if (type === AdCloseType.COMPLETED) {
 *     console.log('用户观看完成，发放奖励');
 *   } else {
 *     console.log('用户取消观看');
 *   }
 * });
 * ```
 */
export const init = sdk.init.bind(sdk);

// ============================================================================
// 广告相关功能
// ============================================================================

/**
 * 显示广告
 *
 * @param callback 广告关闭时的回调函数
 * @example
 * ```typescript
 * await showAd((type) => {
 *   if (type === AdCloseType.COMPLETED) {
 *     // 用户观看完成，发放奖励
 *     grantReward();
 *   } else {
 *     // 用户取消观看
 *     console.log('用户取消观看广告');
 *   }
 * });
 * ```
 */
export const showAd = sdk.showAd.bind(sdk);

/**
 * 检查是否可以显示广告
 *
 * @returns boolean 是否可以显示广告
 * @example
 * ```typescript
 * if (canShowAd()) {
 *   await showAd();
 * } else {
 *   console.log('当前无法显示广告');
 * }
 * ```
 */
export const canShowAd = sdk.canShowAd.bind(sdk);

/**
 * 关闭页面/返回上一页
 * 调用原生接口关闭当前页面或返回上一页
 *
 * 注意：此功能不依赖SDK初始化状态，可以在任何时候调用
 *
 * @param options 页面关闭选项
 * @throws {Error} 当关闭页面失败时抛出错误
 *
 * @example
 * ```typescript
 * import { closePage } from '@anyigame/ad-sdk';
 *
 * try {
 *   closePage({ debug: true });
 *   console.log('页面关闭调用成功');
 * } catch (error) {
 *   console.error('关闭页面失败:', error);
 * }
 * ```
 */
export const closePage = closePageUtil;

// ============================================================================
// 用户信息相关功能
// ============================================================================

/**
 * 获取用户信息（同步）
 *
 * @returns 用户信息，如果未初始化或用户信息不存在则返回 null
 * @example
 * ```typescript
 * const userInfo = getUserInfo();
 * if (userInfo) {
 *   console.log('用户昵称:', userInfo.nickname);
 * }
 * ```
 */
export const getUserInfo = sdk.getUserInfo.bind(sdk);

/**
 * 刷新用户信息
 *
 * @returns Promise<User> 最新的用户信息
 * @example
 * ```typescript
 * try {
 *   const userInfo = await refreshUserInfo();
 *   console.log('刷新后的用户信息:', userInfo);
 * } catch (error) {
 *   console.error('刷新用户信息失败:', error);
 * }
 * ```
 */
export const refreshUserInfo = sdk.refreshUserInfo.bind(sdk);

// ============================================================================
// 游戏配置相关功能
// ============================================================================

/**
 * 获取游戏配置
 *
 * @returns Promise<GameConfig> 游戏配置信息
 * @example
 * ```typescript
 * try {
 *   const gameConfig = await getGameConfig();
 *   console.log('游戏配置:', gameConfig);
 * } catch (error) {
 *   console.error('获取游戏配置失败:', error);
 * }
 * ```
 */
export const getGameConfig = sdk.getGameConfig.bind(sdk);

// ============================================================================
// 玩家数据备份相关功能
// ============================================================================

/**
 * 备份玩家数据
 *
 * @param backupKey 备份数据键名，用于区分不同类型的数据
 * @param data 要备份的数据
 * @param options 备份选项
 * @returns Promise<PlayerDataBackupResult> 备份结果
 * @example
 * ```typescript
 * import { backupPlayerData, BackupType } from '@anyigame/ad-sdk';
 *
 * try {
 *   const result = await backupPlayerData('inventory', {
 *     items: [{ id: 1, name: 'sword', count: 1 }],
 *     gold: 1000
 *   }, {
 *     backupType: BackupType.MANUAL,
 *     description: 'Player inventory backup'
 *   });
 *   console.log('备份成功:', result);
 * } catch (error) {
 *   console.error('备份失败:', error);
 * }
 * ```
 */
export const backupPlayerData = sdk.backupPlayerData.bind(sdk);

/**
 * 检索特定备份数据
 *
 * @param backupKey 备份数据键名
 * @returns Promise<PlayerDataRetrieveResult> 备份数据
 * @example
 * ```typescript
 * import { retrievePlayerData } from '@anyigame/ad-sdk';
 *
 * try {
 *   const data = await retrievePlayerData('inventory');
 *   console.log('检索到的数据:', data.backupData);
 * } catch (error) {
 *   console.error('检索失败:', error);
 * }
 * ```
 */
export const retrievePlayerData = sdk.retrievePlayerData.bind(sdk);

/**
 * 检索所有备份数据
 *
 * @returns Promise<PlayerDataRetrieveResult[]> 所有备份数据列表
 * @example
 * ```typescript
 * import { retrieveAllPlayerData } from '@anyigame/ad-sdk';
 *
 * try {
 *   const allData = await retrieveAllPlayerData();
 *   allData.forEach(backup => {
 *     console.log(`备份键: ${backup.backupKey}, 数据:`, backup.backupData);
 *   });
 * } catch (error) {
 *   console.error('检索失败:', error);
 * }
 * ```
 */
export const retrieveAllPlayerData = sdk.retrieveAllPlayerData.bind(sdk);

/**
 * 获取备份统计信息
 *
 * @returns Promise<PlayerDataStats> 备份统计信息
 * @example
 * ```typescript
 * import { getPlayerDataStats } from '@anyigame/ad-sdk';
 *
 * try {
 *   const stats = await getPlayerDataStats();
 *   console.log(`总备份数: ${stats.totalBackups}, 总大小: ${stats.totalDataSize} 字节`);
 * } catch (error) {
 *   console.error('获取统计信息失败:', error);
 * }
 * ```
 */
export const getPlayerDataStats = sdk.getPlayerDataStats.bind(sdk);

// ============================================================================
// SDK 状态和工具功能
// ============================================================================

/**
 * 获取 SDK 状态
 *
 * @returns string SDK 当前状态
 */
export const getState = sdk.getState.bind(sdk);

/**
 * 获取 SDK 版本信息
 *
 * @returns string SDK 版本号
 */
export const getVersion = sdk.getVersion.bind(sdk);

/**
 * 获取调试信息
 *
 * @returns object 包含 SDK 各种状态信息的对象
 */
export const getDebugInfo = sdk.getDebugInfo.bind(sdk);

/**
 * 强制重置广告状态
 * 用于异常情况下的状态恢复
 *
 * @example
 * ```typescript
 * // 当广告状态异常时，强制重置
 * if (!canShowAd()) {
 *   resetState();
 *   console.log('广告状态已重置');
 * }
 * ```
 */
export const resetState = sdk.resetState.bind(sdk);

/**
 * 销毁 SDK
 * 清理所有资源和事件监听器
 *
 * @example
 * ```typescript
 * // 在页面卸载时销毁 SDK
 * window.addEventListener('beforeunload', () => {
 *   destroy();
 * });
 * ```
 */
export const destroy = sdk.destroy.bind(sdk);

// ============================================================================
// 跨 iframe 功能
// ============================================================================

// 导入跨 iframe 相关模块
import { CrossIframeSDKManager, CrossIframeSDKProxy } from './utils/CrossIframeSDK';

/**
 * 在顶层窗口初始化 SDK（跨 iframe 模式）
 *
 * @param config SDK 配置
 * @param adapter 可选的自定义适配器
 * @example
 * ```typescript
 * // 在顶层窗口（父页面）中初始化 SDK
 * import { initInTopWindow } from '@anyigame/ad-sdk';
 *
 * await initInTopWindow({
 *   appid: '1001',
 *   channel: '1',
 *   debug: true
 * });
 * ```
 */
export const initInTopWindow = CrossIframeSDKManager.initInTopWindow.bind(CrossIframeSDKManager);

/**
 * 检查顶层窗口是否已初始化 SDK
 *
 * @returns boolean 是否已初始化
 * @example
 * ```typescript
 * import { isSDKInitializedInTopWindow } from '@anyigame/ad-sdk';
 *
 * if (isSDKInitializedInTopWindow()) {
 *   console.log('SDK 已在顶层窗口初始化');
 * }
 * ```
 */
export const isSDKInitializedInTopWindow = CrossIframeSDKManager.isSDKInitializedInTopWindow.bind(CrossIframeSDKManager);

/**
 * 清理跨 iframe SDK 资源
 *
 * @example
 * ```typescript
 * import { cleanupCrossIframeSDK } from '@anyigame/ad-sdk';
 *
 * // 在页面卸载时清理
 * window.addEventListener('beforeunload', () => {
 *   cleanupCrossIframeSDK();
 * });
 * ```
 */
export const cleanupCrossIframeSDK = CrossIframeSDKManager.cleanup.bind(CrossIframeSDKManager);

/**
 * 创建 iframe SDK 代理实例
 *
 * @returns CrossIframeSDKProxy 代理实例
 * @example
 * ```typescript
 * import { createIframeSDKProxy, AdCloseType } from '@anyigame/ad-sdk';
 *
 * // 在 iframe 中创建代理
 * const sdkProxy = createIframeSDKProxy();
 *
 * if (sdkProxy.isAvailable()) {
 *   await sdkProxy.showAd((type) => {
 *     if (type === AdCloseType.COMPLETED) {
 *       console.log('用户观看完成');
 *     }
 *   });
 * }
 * ```
 */
export function createIframeSDKProxy(): CrossIframeSDKProxy {
  return new CrossIframeSDKProxy();
}

// ============================================================================
// 默认导出
// ============================================================================

/**
 * 默认导出 SDK 实例
 *
 * @example
 * ```typescript
 * import AdSDK from '@anyigame/ad-sdk';
 *
 * // 使用默认实例
 * await AdSDK.init({ appid: '1001', channel: '1' });
 * await AdSDK.showAd();
 * ```
 */
export default sdk;