/**
 * H5 游戏 SDK 核心类
 *
 * 提供统一的 SDK 接口，管理各个模块的生命周期
 */

// ============================================================================
// 导入依赖
// ============================================================================

// API 相关类型和函数
import type { User, GameConfig } from '../api';
import { client } from '../api/client.gen';
import { getGameConfig } from '../api';

// SDK 核心类型和工具
import type { SDKConfig, AdCloseCallback } from '../types';
import { SDKError, SDKEventName, SDKConfigValidator } from '../types';

// 适配器
import type { NativeBridgeAdapter } from '../adapters/NativeBridgeAdapter';
import { DsmAdapter } from '../adapters/DsmAdapter';

// 模块
import { UserModule } from '../modules/UserModule';
import { AdModule } from '../modules/AdModule';
import { EventModule } from '../modules/EventModule';
import { HeartbeatModule } from '../modules/HeartbeatModule';
import { PlayerDataModule } from '../modules/PlayerDataModule';

// ============================================================================
// 类型定义
// ============================================================================

/**
 * SDK 初始化状态枚举
 */
enum SDKState {
  UNINITIALIZED = 'uninitialized',
  INITIALIZING = 'initializing',
  INITIALIZED = 'initialized',
  ERROR = 'error',
}

// ============================================================================
// 主类定义
// ============================================================================

/**
 * H5 游戏 SDK 主类
 *
 * 采用单例模式，提供统一的 SDK 接口
 */
export class GameSDK {
  // ========================================================================
  // 静态属性和方法（单例模式）
  // ========================================================================

  private static instance: GameSDK | null = null;

  /**
   * 获取 SDK 实例（单例模式）
   */
  static getInstance(): GameSDK {
    if (!GameSDK.instance) {
      GameSDK.instance = new GameSDK();
    }
    return GameSDK.instance;
  }

  // ========================================================================
  // 实例属性
  // ========================================================================

  private config?: SDKConfig | undefined;
  private adapter?: NativeBridgeAdapter | undefined;
  private userModule?: UserModule | undefined;
  private adModule?: AdModule | undefined;
  private eventModule?: EventModule | undefined;
  private heartbeatModule?: HeartbeatModule | undefined;
  private playerDataModule?: PlayerDataModule | undefined;
  private state: SDKState = SDKState.UNINITIALIZED;
  private initError?: Error | undefined;

  // ========================================================================
  // 构造函数
  // ========================================================================

  /**
   * 私有构造函数，确保单例模式
   */
  private constructor() {}

  // ========================================================================
  // 初始化相关方法
  // ========================================================================

  /**
   * 初始化 SDK
   * @param config SDK 配置
   * @param adapter 可选的自定义适配器，如果不提供则使用默认的 DsmAdapter
   */
  async init(config: SDKConfig, adapter?: NativeBridgeAdapter): Promise<void> {
    if (this.state === SDKState.INITIALIZING) {
      throw new SDKError('SDK 正在初始化中，请勿重复调用');
    }

    if (this.state === SDKState.INITIALIZED) {
      if (this.config?.debug) {
        console.warn('[GameSDK] SDK 已经初始化，跳过重复初始化');
      }
      return;
    }

    this.state = SDKState.INITIALIZING;

    try {
      // 验证配置
      SDKConfigValidator.validateConfig(config);

      // 获取带默认值的完整配置
      const fullConfig = SDKConfigValidator.getConfigWithDefaults(config);
      this.config = fullConfig;

      // 配置 API 客户端
      try {
        client.setConfig({
          baseUrl: 'https://open.anyigame.cn',
          headers: {
            'X-Channel-Code': this.config.channel,
            'Content-Type': 'application/json',
          },
        });

        if (this.config.debug) {
          console.log('[GameSDK] API 客户端配置成功:', {
            baseUrl: 'https://open.anyigame.cn',
            headers: {
              'X-Channel-Code': this.config.channel,
              'Content-Type': 'application/json',
            }
          });
        }
      } catch (error) {
        if (this.config.debug) {
          console.error('[GameSDK] API 客户端配置失败:', error);
        }
        throw new SDKError(`API 客户端配置失败: ${error instanceof Error ? error.message : String(error)}`);
      }

      // 初始化适配器
      this.adapter = adapter || new DsmAdapter();

      // 设置适配器的调试模式
      if ('setDebugMode' in this.adapter && typeof this.adapter.setDebugMode === 'function') {
        this.adapter.setDebugMode(fullConfig.debug);
      }

      // 检查原生环境
      const nativeEnv = this.adapter.detectNativeEnvironment();
      if (!nativeEnv.hasNativeSupport) {
        throw new SDKError('未检测到支持的原生环境');
      }

      if (config.debug) {
        console.log('[GameSDK] 检测到原生环境:', nativeEnv);
      }

      // 初始化用户模块
      this.userModule = new UserModule(config);
      await this.userModule.initialize();

      // 初始化事件模块
      this.eventModule = new EventModule(config, () => this.userModule?.getUserId() ?? null);

      // 上报 SDK 初始化开始事件
      this.eventModule.reportEvent(SDKEventName.SDK_INIT_START, {
        custom_data: {
          config: {
            appid: this.config.appid,
            channel: this.config.channel,
            debug: this.config.debug,
          },
          nativeEnv
        },
      });

      // 初始化广告模块
      this.adModule = new AdModule(this.adapter, config, this.eventModule);

      // 初始化玩家数据模块
      this.playerDataModule = new PlayerDataModule(config, () => this.userModule?.getUserId() || null);

      this.state = SDKState.INITIALIZED;

      // 初始化心跳模块并启动
      this.heartbeatModule = new HeartbeatModule(config);
      this.heartbeatModule.start();

      // 上报 SDK 初始化成功事件
      this.eventModule.reportEvent(SDKEventName.SDK_INIT_SUCCESS, {
        custom_data: {
          init_duration: Date.now(),
          user_id: this.userModule.getUserId(),
        },
      });

      if (config.debug) {
        console.log('[GameSDK] SDK 初始化成功');
      }
    } catch (error) {
      this.state = SDKState.ERROR;
      this.initError = error instanceof Error ? error : new Error(String(error));

      // 上报 SDK 初始化失败事件
      if (this.eventModule) {
        this.eventModule.reportEvent(SDKEventName.SDK_INIT_FAILED, {
          error_message: this.initError.message,
          custom_data: {
            error_time: Date.now(),
          },
        });
      }

      const errorMessage = `SDK 初始化失败: ${this.initError.message}`;
      if (config.debug) {
        console.error('[GameSDK]', errorMessage, error);
      }
      throw new SDKError(errorMessage, undefined, this.initError);
    }
  }

  // ========================================================================
  // 用户相关方法
  // ========================================================================

  /**
   * 获取用户信息（同步）
   * @returns 用户信息，如果未初始化或用户信息不存在则返回 null
   */
  getUserInfo(): User | null {
    this.checkInitialized();
    return this.userModule?.getUserInfo() ?? null;
  }

  /**
   * 刷新用户信息
   * @returns Promise<User> 最新的用户信息
   */
  async refreshUserInfo(): Promise<User> {
    this.checkInitialized();
    if (!this.userModule) {
      throw new SDKError('用户模块未初始化');
    }
    return await this.userModule.refreshUserInfo();
  }

  // ========================================================================
  // 游戏配置相关方法
  // ========================================================================

  /**
   * 获取游戏配置
   * @returns Promise<GameConfig> 游戏配置信息
   */
  async getGameConfig(): Promise<GameConfig> {
    this.checkInitialized();
    if (!this.config) {
      throw new SDKError('SDK 配置未初始化');
    }

    try {
      const response = await getGameConfig({
        client: client,
        query: {
          code: this.config.appid,
        },
        headers: {
          'X-Channel-Code': this.config.channel,
        },
      });

      if (response.data?.data) {
        return response.data.data;
      } else {
        throw new SDKError('API 返回的游戏配置格式不正确');
      }
    } catch (error) {
      const errorMessage = `获取游戏配置失败: ${error instanceof Error ? error.message : String(error)}`;
      if (this.config.debug) {
        console.error('[GameSDK]', errorMessage, error);
      }
      throw new SDKError(errorMessage, undefined, error instanceof Error ? error : undefined);
    }
  }

  // ========================================================================
  // 广告相关方法
  // ========================================================================

  /**
   * 显示广告
   * @param callback 广告关闭时的回调函数
   */
  async showAd(callback?: AdCloseCallback): Promise<void> {
    this.checkInitialized();
    if (!this.adModule) {
      throw new SDKError('广告模块未初始化');
    }
    await this.adModule.showAd(callback);
  }

  /**
   * 检查是否可以显示广告
   */
  canShowAd(): boolean {
    if (this.state !== SDKState.INITIALIZED || !this.adModule) {
      return false;
    }
    return this.adModule.canShowAd();
  }

  /**
   * 关闭页面/返回上一页
   * 调用原生接口关闭当前页面或返回上一页
   *
   * 注意：此功能不依赖SDK初始化状态，可以在任何时候调用
   */
  closePage(): void {
    // 通过适配器调用页面关闭功能
    if (!this.adapter) {
      throw new Error('SDK adapter not initialized');
    }
    this.adapter.closePage();
  }

  // ========================================================================
  // 玩家数据备份相关方法
  // ========================================================================

  /**
   * 备份玩家数据
   * @param backupKey 备份数据键名，用于区分不同类型的数据
   * @param data 要备份的数据
   * @param options 备份选项
   * @returns 备份结果
   */
  async backupPlayerData(
    backupKey: string,
    data: Record<string, unknown>,
    options?: import('../types').PlayerDataBackupOptions
  ): Promise<import('../types').PlayerDataBackupResult> {
    this.checkInitialized();
    if (!this.playerDataModule) {
      throw new SDKError('玩家数据模块未初始化');
    }
    return await this.playerDataModule.backupData(backupKey, data, options);
  }

  /**
   * 检索特定备份数据
   * @param backupKey 备份数据键名
   * @returns 备份数据
   */
  async retrievePlayerData(backupKey: string): Promise<import('../types').PlayerDataRetrieveResult> {
    this.checkInitialized();
    if (!this.playerDataModule) {
      throw new SDKError('玩家数据模块未初始化');
    }
    return await this.playerDataModule.retrieveData(backupKey);
  }

  /**
   * 检索所有备份数据
   * @returns 所有备份数据列表
   */
  async retrieveAllPlayerData(): Promise<import('../types').PlayerDataRetrieveResult[]> {
    this.checkInitialized();
    if (!this.playerDataModule) {
      throw new SDKError('玩家数据模块未初始化');
    }
    return await this.playerDataModule.retrieveAllData();
  }

  /**
   * 获取备份统计信息
   * @returns 备份统计信息
   */
  async getPlayerDataStats(): Promise<import('../types').PlayerDataStats> {
    this.checkInitialized();
    if (!this.playerDataModule) {
      throw new SDKError('玩家数据模块未初始化');
    }
    return await this.playerDataModule.getStats();
  }

  /**
   * 强制重置广告状态
   * 用于异常情况下的状态恢复
   */
  resetState(): void {
    if (this.config?.debug) {
      console.log('[GameSDK] 强制重置广告状态');
    }

    if (this.adModule) {
      this.adModule.resetState();
    } else {
      if (this.config?.debug) {
        console.warn('[GameSDK] 广告模块未初始化，无法重置状态');
      }
    }
  }

  // ========================================================================
  // 状态和调试相关方法
  // ========================================================================

  /**
   * 获取 SDK 状态
   */
  getState(): string {
    return this.state;
  }

  /**
   * 获取 SDK 版本信息
   */
  getVersion(): string {
    return '1.0.4';
  }

  /**
   * 获取调试信息
   */
  getDebugInfo(): object {
    return {
      state: this.state,
      version: this.getVersion(),
      config: this.config,
      userInfo: this.getUserInfo(),
      canShowAd: this.canShowAd(),
      adState: this.adModule?.getAdState(),
      eventQueueStatus: this.eventModule?.getQueueStatus(),
      heartbeatStatus: this.heartbeatModule?.getStatus(),
      playerDataStatus: this.playerDataModule?.getModuleStatus(),
      initError: this.initError?.message,
    };
  }



  // ========================================================================
  // 生命周期管理方法
  // ========================================================================

  /**
   * 销毁 SDK
   * 清理所有资源和事件监听器
   */
  destroy(): void {
    const debug = this.config?.debug;

    if (debug) {
      console.log('[GameSDK] 开始销毁 SDK');
    }

    // 清理各个模块
    this.heartbeatModule?.cleanup();
    this.playerDataModule?.cleanup();
    this.eventModule?.cleanup();
    this.adModule?.cleanup();
    this.userModule?.cleanup();
    this.adapter?.cleanup();

    // 重置状态
    this.state = SDKState.UNINITIALIZED;
    this.config = undefined;
    this.adapter = undefined;
    this.userModule = undefined;
    this.adModule = undefined;
    this.eventModule = undefined;
    this.heartbeatModule = undefined;
    this.playerDataModule = undefined;
    this.initError = undefined;

    // 清除单例实例
    GameSDK.instance = null;

    if (debug) {
      console.log('[GameSDK] SDK 销毁完成');
    }
  }

  // ========================================================================
  // 私有辅助方法
  // ========================================================================

  /**
   * 检查 SDK 是否已初始化
   */
  private checkInitialized(): void {
    if (this.state === SDKState.UNINITIALIZED) {
      throw new SDKError('SDK 未初始化，请先调用 init() 方法');
    }
    if (this.state === SDKState.INITIALIZING) {
      throw new SDKError('SDK 正在初始化中，请等待初始化完成');
    }
    if (this.state === SDKState.ERROR) {
      throw new SDKError(`SDK 初始化失败: ${this.initError?.message ?? '未知错误'}`);
    }
  }
}
