# @anyigame/ad-sdk 示例文档

本文档提供了 @anyigame/ad-sdk 的详细使用示例和 API 参考。

## 📖 目录

- [基础功能](#基础功能)
- [用户管理](#用户管理)
- [广告功能](#广告功能)
- [玩家数据备份](#玩家数据备份)
- [跨 iframe 功能](#跨-iframe-功能)
- [工具方法](#工具方法)
- [错误处理](#错误处理)

## 基础功能

### SDK 初始化

#### `init(config: SDKConfig): Promise<void>`

初始化 SDK 并配置基本参数。

```typescript
import { init } from '@anyigame/ad-sdk';

await init({
  appid: '1001',
  channel: '1',
  debug: true,
  timeout: 15000,
  maxRetries: 3,
  batchSize: 10,
  reportInterval: 5000
});
```

## 用户管理

### 获取用户信息

#### `getUserInfo(): User | null`

同步获取缓存的用户信息。

```typescript
import { getUserInfo } from '@anyigame/ad-sdk';

const userInfo = getUserInfo();
if (userInfo) {
  console.log('用户ID:', userInfo.id);
  console.log('用户名:', userInfo.name);
  console.log('头像:', userInfo.avatar);
}
```

### 刷新用户信息

#### `refreshUserInfo(): Promise<User>`

异步刷新用户信息，清除缓存并从 API 获取最新数据。

```typescript
import { refreshUserInfo } from '@anyigame/ad-sdk';

try {
  const userInfo = await refreshUserInfo();
  console.log('刷新后的用户信息:', userInfo);
} catch (error) {
  console.error('刷新用户信息失败:', error);
}
```

## 广告功能

### 显示广告

#### `showAd(callback?: AdCloseCallback): Promise<void>`

显示广告并处理用户行为回调。

```typescript
import { showAd, AdCloseType } from '@anyigame/ad-sdk';

await showAd((type) => {
  if (type === AdCloseType.COMPLETED) {
    console.log('用户观看完成，发放奖励');
    // 在这里处理奖励逻辑
  } else if (type === AdCloseType.CANCELLED) {
    console.log('用户取消观看');
  }
});
```

### 检查广告可用性

#### `canShowAd(): boolean`

检查当前是否可以显示广告。

```typescript
import { canShowAd, showAd } from '@anyigame/ad-sdk';

if (canShowAd()) {
  await showAd();
} else {
  console.log('广告当前不可用');
}
```

## 玩家数据备份

### 备份玩家数据

#### `backupPlayerData(backupKey: string, data: Record<string, unknown>, options?: PlayerDataBackupOptions): Promise<PlayerDataBackupResult>`

将玩家游戏数据备份到服务器。

**参数:**
- `backupKey: string` - 备份数据键名，用于区分不同类型的数据（如 'inventory', 'progress', 'settings'）
- `data: Record<string, unknown>` - 要备份的 JSON 数据对象
- `options?: PlayerDataBackupOptions` - 可选的备份配置选项

**返回值:** `Promise<PlayerDataBackupResult>` - 包含备份ID、数据大小、创建时间等信息

```typescript
import { backupPlayerData, BackupType } from '@anyigame/ad-sdk';

// 备份玩家库存数据
const inventoryData = {
  items: [
    { id: 1, name: 'sword', count: 1, rarity: 'epic' },
    { id: 2, name: 'potion', count: 5, rarity: 'common' }
  ],
  gold: 1500,
  gems: 50
};

try {
  const result = await backupPlayerData('inventory', inventoryData, {
    backupType: BackupType.MANUAL,
    description: '玩家库存备份',
    deviceInfo: {
      platform: 'web',
      version: '1.0.0'
    }
  });
  
  console.log('备份成功:', result);
  console.log('备份ID:', result.backupId);
  console.log('数据大小:', result.dataSize, '字节');
} catch (error) {
  console.error('备份失败:', error);
}
```

### 检索特定备份数据

#### `retrievePlayerData(backupKey: string): Promise<PlayerDataRetrieveResult>`

根据备份键名检索特定的备份数据。

**参数:**
- `backupKey: string` - 要检索的备份数据键名

**返回值:** `Promise<PlayerDataRetrieveResult>` - 包含备份数据、版本信息、创建时间等

```typescript
import { retrievePlayerData } from '@anyigame/ad-sdk';

try {
  const result = await retrievePlayerData('inventory');
  
  console.log('检索成功:', result);
  console.log('备份数据:', result.backupData);
  console.log('数据版本:', result.dataVersion);
  console.log('备份类型:', result.backupType);
  console.log('创建时间:', result.createdAt);
  
  // 使用检索到的库存数据
  const inventory = result.backupData;
  if (inventory.items) {
    console.log(`玩家有 ${inventory.items.length} 种物品`);
  }
} catch (error) {
  console.error('检索失败:', error);
}
```

### 检索所有备份数据

#### `retrieveAllPlayerData(): Promise<PlayerDataRetrieveResult[]>`

检索当前用户的所有备份数据。

**返回值:** `Promise<PlayerDataRetrieveResult[]>` - 所有备份数据的数组

```typescript
import { retrieveAllPlayerData } from '@anyigame/ad-sdk';

try {
  const allBackups = await retrieveAllPlayerData();
  
  console.log(`检索到 ${allBackups.length} 个备份:`);
  
  allBackups.forEach((backup, index) => {
    console.log(`备份 ${index + 1}:`);
    console.log(`  键名: ${backup.backupKey}`);
    console.log(`  大小: ${backup.dataSize} 字节`);
    console.log(`  类型: ${backup.backupTypeDescription}`);
    console.log(`  创建时间: ${backup.createdAt}`);
    
    if (backup.description) {
      console.log(`  描述: ${backup.description}`);
    }
  });
  
  // 查找特定类型的备份
  const inventoryBackup = allBackups.find(backup => backup.backupKey === 'inventory');
  if (inventoryBackup) {
    console.log('找到库存备份:', inventoryBackup.backupData);
  }
} catch (error) {
  console.error('检索所有数据失败:', error);
}
```

### 获取备份统计信息

#### `getPlayerDataStats(): Promise<PlayerDataStats>`

获取当前用户的备份统计信息。

**返回值:** `Promise<PlayerDataStats>` - 包含总备份数、总大小、按类型分组的统计等

```typescript
import { getPlayerDataStats } from '@anyigame/ad-sdk';

try {
  const stats = await getPlayerDataStats();
  
  console.log('备份统计信息:');
  console.log(`总备份数量: ${stats.totalBackups}`);
  console.log(`总数据大小: ${(stats.totalDataSize / 1024).toFixed(2)} KB`);
  
  console.log('按备份类型分组:');
  Object.entries(stats.backupTypes).forEach(([type, count]) => {
    console.log(`  ${type}: ${count} 个`);
  });
  
  if (stats.latestBackup) {
    console.log(`最新备份时间: ${stats.latestBackup}`);
  }
} catch (error) {
  console.error('获取统计信息失败:', error);
}
```

### 备份类型枚举

```typescript
import { BackupType } from '@anyigame/ad-sdk';

// 可用的备份类型
console.log('手动备份:', BackupType.MANUAL);        // 'manual'
console.log('自动备份:', BackupType.AUTO);          // 'auto'  
console.log('关键节点备份:', BackupType.CHECKPOINT); // 'checkpoint'
```

### 完整的玩家数据备份示例

```typescript
import { 
  backupPlayerData, 
  retrievePlayerData, 
  retrieveAllPlayerData, 
  getPlayerDataStats,
  BackupType 
} from '@anyigame/ad-sdk';

class PlayerDataManager {
  // 备份玩家进度
  async backupProgress(level: number, experience: number, currentStage: string) {
    const progressData = {
      level,
      experience,
      currentStage,
      completedQuests: ['tutorial', 'first_battle'],
      achievements: ['first_kill', 'level_10'],
      stats: {
        health: 100,
        mana: 80,
        strength: 45
      }
    };

    try {
      const result = await backupPlayerData('progress', progressData, {
        backupType: BackupType.AUTO,
        description: `等级 ${level} 自动进度备份`
      });
      
      console.log('进度备份成功:', result.backupId);
      return result;
    } catch (error) {
      console.error('进度备份失败:', error);
      throw error;
    }
  }

  // 加载玩家进度
  async loadProgress() {
    try {
      const result = await retrievePlayerData('progress');
      const progress = result.backupData;
      
      console.log(`加载进度: 等级 ${progress.level}, 经验 ${progress.experience}`);
      return progress;
    } catch (error) {
      console.error('加载进度失败:', error);
      return null;
    }
  }

  // 获取备份概览
  async getBackupOverview() {
    try {
      const [allBackups, stats] = await Promise.all([
        retrieveAllPlayerData(),
        getPlayerDataStats()
      ]);

      return {
        backups: allBackups,
        statistics: stats,
        hasProgress: allBackups.some(backup => backup.backupKey === 'progress'),
        hasInventory: allBackups.some(backup => backup.backupKey === 'inventory')
      };
    } catch (error) {
      console.error('获取备份概览失败:', error);
      return null;
    }
  }
}
```

## 跨 iframe 功能

### 跨 iframe 玩家数据备份

当游戏运行在 iframe 中时，可以通过代理访问父窗口的 SDK 功能：

```typescript
import { createIframeSDKProxy, BackupType } from '@anyigame/ad-sdk';

// 在 iframe 中创建 SDK 代理
const sdkProxy = createIframeSDKProxy();

if (sdkProxy.isAvailable()) {
  // 在 iframe 中备份数据
  const result = await sdkProxy.backupPlayerData('iframe_data', {
    gameState: 'playing',
    score: 1500,
    timestamp: Date.now()
  }, {
    backupType: BackupType.MANUAL,
    description: 'iframe 游戏数据备份'
  });

  // 在 iframe 中检索数据
  const data = await sdkProxy.retrievePlayerData('iframe_data');
  
  // 在 iframe 中获取统计信息
  const stats = await sdkProxy.getPlayerDataStats();
}
```

## 工具方法

### 获取 SDK 状态

#### `getState(): string`

获取当前 SDK 的状态。

```typescript
import { getState } from '@anyigame/ad-sdk';

const state = getState();
console.log('SDK 状态:', state);
```

### 获取 SDK 版本

#### `getVersion(): string`

获取当前 SDK 的版本号。

```typescript
import { getVersion } from '@anyigame/ad-sdk';

const version = getVersion();
console.log('SDK 版本:', version);
```

### 获取调试信息

#### `getDebugInfo(): object`

获取详细的调试信息，包括 SDK 状态、配置和模块状态。

```typescript
import { getDebugInfo } from '@anyigame/ad-sdk';

const debugInfo = getDebugInfo();
console.log('调试信息:', debugInfo);
```

## 错误处理

### 玩家数据备份错误处理最佳实践

```typescript
import { backupPlayerData, retrievePlayerData, BackupType } from '@anyigame/ad-sdk';

class RobustPlayerDataManager {
  async safeBackupData(key: string, data: any, options = {}) {
    try {
      // 数据验证
      if (!key || typeof key !== 'string') {
        throw new Error('备份键名无效');
      }
      
      if (!data || typeof data !== 'object') {
        throw new Error('备份数据无效');
      }

      // 检查数据大小（1MB 限制）
      const dataSize = JSON.stringify(data).length;
      if (dataSize > 1024 * 1024) {
        throw new Error('数据大小超过 1MB 限制');
      }

      const result = await backupPlayerData(key, data, {
        backupType: BackupType.MANUAL,
        ...options
      });

      console.log(`数据备份成功: ${key} (${result.dataSize} 字节)`);
      return result;
    } catch (error) {
      if (error.message.includes('用户未登录')) {
        console.error('请先登录后再进行数据备份');
      } else if (error.message.includes('网络')) {
        console.error('网络连接失败，请检查网络后重试');
      } else {
        console.error('数据备份失败:', error.message);
      }
      throw error;
    }
  }

  async safeRetrieveData(key: string) {
    try {
      const result = await retrievePlayerData(key);
      
      // 数据完整性检查
      if (!result.backupData) {
        console.warn(`备份数据 ${key} 为空`);
        return null;
      }

      console.log(`数据检索成功: ${key}`);
      return result;
    } catch (error) {
      if (error.message.includes('不存在')) {
        console.log(`备份数据 ${key} 不存在`);
        return null;
      } else {
        console.error('数据检索失败:', error.message);
        throw error;
      }
    }
  }
}
```

---

更多详细信息和高级用法，请参考主 README.md 文件和 API 文档。
