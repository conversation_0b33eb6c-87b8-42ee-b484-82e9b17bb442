<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页面关闭功能示例</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: background-color 0.3s;
        }
        .button:hover {
            background: #0056b3;
        }
        .button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .danger {
            background: #dc3545;
        }
        .danger:hover {
            background: #c82333;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .code {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        .section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>SDK 页面关闭功能示例</h1>
        
        <div class="section">
            <h3>SDK 状态</h3>
            <div id="sdkStatus" class="status info">SDK 未初始化</div>
            <button id="initBtn" class="button">初始化 SDK</button>
            <button id="destroyBtn" class="button danger" disabled>销毁 SDK</button>
        </div>

        <div class="section">
            <h3>页面关闭功能</h3>
            <p>此功能调用原生接口关闭当前页面或返回上一页。支持以下原生接口：</p>
            <ul>
                <li><strong>iOS:</strong> webkit.messageHandlers.returnHome</li>
                <li><strong>Android:</strong> DsmJSInterface.goPreviewPage</li>
            </ul>
            <button id="closePageBtn" class="button danger" disabled>关闭页面</button>
            <div id="closePageStatus"></div>
        </div>

        <div class="section">
            <h3>使用示例代码</h3>
            <div class="code">
// 基本用法
import { init, closePage } from '@anyigame/ad-sdk';

// 初始化 SDK
await init({
    appid: '1001',
    channel: '1',
    debug: true
});

// 关闭页面
try {
    closePage();
    console.log('页面关闭调用成功');
} catch (error) {
    console.error('关闭页面失败:', error);
}
            </div>
        </div>

        <div class="section">
            <h3>跨 iframe 使用</h3>
            <div class="code">
// 在 iframe 中使用
import { createIframeSDKProxy } from '@anyigame/ad-sdk';

const sdkProxy = createIframeSDKProxy();

if (sdkProxy.isAvailable()) {
    try {
        sdkProxy.closePage();
        console.log('iframe 中页面关闭调用成功');
    } catch (error) {
        console.error('iframe 中关闭页面失败:', error);
    }
} else {
    console.log('SDK 在顶层窗口中不可用');
}
            </div>
        </div>
    </div>

    <!-- 引入 SDK -->
    <script src="../dist/index.js"></script>
    <script>
        const { init, closePage, destroy, getState } = window.AdSDK;
        
        // DOM 元素
        const sdkStatus = document.getElementById('sdkStatus');
        const initBtn = document.getElementById('initBtn');
        const destroyBtn = document.getElementById('destroyBtn');
        const closePageBtn = document.getElementById('closePageBtn');
        const closePageStatus = document.getElementById('closePageStatus');

        // 更新状态显示
        function updateStatus() {
            const state = getState();
            sdkStatus.textContent = `SDK 状态: ${state}`;
            
            if (state === 'initialized') {
                sdkStatus.className = 'status success';
                initBtn.disabled = true;
                destroyBtn.disabled = false;
                closePageBtn.disabled = false;
            } else {
                sdkStatus.className = 'status info';
                initBtn.disabled = false;
                destroyBtn.disabled = true;
                closePageBtn.disabled = true;
            }
        }

        // 显示消息
        function showMessage(element, message, type = 'info') {
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
            setTimeout(() => {
                element.innerHTML = '';
            }, 5000);
        }

        // 初始化 SDK
        initBtn.addEventListener('click', async () => {
            try {
                // 模拟原生环境（仅用于演示）
                if (!window.DsmJSInterface && !window.webkit) {
                    window.DsmJSInterface = {
                        showAd: () => console.log('模拟 Android showAd 调用'),
                        goPreviewPage: () => console.log('模拟 Android goPreviewPage 调用')
                    };
                }

                await init({
                    appid: '1001',
                    channel: '1',
                    debug: true
                });
                
                updateStatus();
                showMessage(closePageStatus, 'SDK 初始化成功！', 'success');
            } catch (error) {
                console.error('SDK 初始化失败:', error);
                showMessage(closePageStatus, `SDK 初始化失败: ${error.message}`, 'error');
            }
        });

        // 销毁 SDK
        destroyBtn.addEventListener('click', () => {
            try {
                destroy();
                updateStatus();
                showMessage(closePageStatus, 'SDK 已销毁', 'info');
            } catch (error) {
                console.error('SDK 销毁失败:', error);
                showMessage(closePageStatus, `SDK 销毁失败: ${error.message}`, 'error');
            }
        });

        // 关闭页面
        closePageBtn.addEventListener('click', () => {
            try {
                closePage();
                showMessage(closePageStatus, '页面关闭调用成功！原生接口已被调用。', 'success');
            } catch (error) {
                console.error('关闭页面失败:', error);
                showMessage(closePageStatus, `关闭页面失败: ${error.message}`, 'error');
            }
        });

        // 初始状态更新
        updateStatus();
    </script>
</body>
</html>
