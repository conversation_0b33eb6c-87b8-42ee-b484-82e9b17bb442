{"name": "@anyigame/ad-sdk", "version": "1.0.4", "type": "module", "description": "Ad SDK with H5 bridge for multiple native clients", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "browser": "dist/ad-sdk.umd.min.js", "unpkg": "dist/ad-sdk.umd.min.js", "jsdelivr": "dist/ad-sdk.umd.min.js", "files": ["dist"], "scripts": {"dev": "bun run src/index.ts", "build": "rollup -c", "build:types": "tsc --emitDeclarationOnly", "api:generate": "openapi-ts", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,json,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,json,md}\"", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "type-check": "tsc --noEmit", "prepare": "husky install", "prepublishOnly": "npm run build"}, "keywords": ["ad", "sdk", "h5", "bridge", "webview", "typescript"], "author": "anyigame", "license": "MIT", "devDependencies": {"@hey-api/openapi-ts": "^0.72.0", "@rollup/plugin-commonjs": "^28.0.3", "@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.2", "@types/jest": "^29.5.14", "@typescript-eslint/eslint-plugin": "^8.34.0", "@typescript-eslint/parser": "^8.34.0", "eslint": "^9.28.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.1", "husky": "^9.1.7", "jest": "^30.0.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^16.1.0", "prettier": "^3.5.3", "rollup": "^4.42.0", "rollup-plugin-dts": "^6.2.1", "rollup-plugin-filesize": "^10.0.0", "ts-jest": "^29.3.4", "tslib": "^2.8.1", "typescript": "^5.8.3"}, "dependencies": {"@hey-api/client-fetch": "^0.13.0"}, "lint-staged": {"*.{ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}}