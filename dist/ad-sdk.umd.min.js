!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).AdSDK={})}(this,(function(e){"use strict";var t,s,a;e.AdCloseType=void 0,(t=e.AdCloseType||(e.AdCloseType={}))[t.COMPLETED=1]="COMPLETED",t[t.CANCELLED=2]="CANCELLED",function(e){e.SDK_INIT_START="sdk_init_start",e.SDK_INIT_SUCCESS="sdk_init_success",e.SDK_INIT_FAILED="sdk_init_failed",e.AD_REQUEST="ad_request",e.AD_IMPRESSION="ad_impression",e.AD_IMPRESSION_FAILED="ad_impression_failed",e.AD_CLOSE="ad_close",e.AD_REWARD_GRANT="ad_reward_grant"}(s||(s={}));class i extends Error{constructor(e,t,s){super(e),this.code=t,this.originalError=s,this.name="SDKError"}}class r{static isValidAppId(e){return/^[0-9]+$/.test(e)}static isValidChannelCode(e){return/^[0-9]+$/.test(e)}static validateConfig(e){if(!e.appid||!this.isValidAppId(e.appid))throw new i('应用标识符必须为数字字符串格式，如 "1001"');if(!e.channel||!this.isValidChannelCode(e.channel))throw new i('渠道代码必须为数字字符串格式，如 "1"');if(void 0!==e.timeout&&(e.timeout<=0||e.timeout>6e4))throw new i("超时时间必须在 1-60000 毫秒之间");if(void 0!==e.maxRetries&&(e.maxRetries<0||e.maxRetries>10))throw new i("重试次数必须在 0-10 之间");if(void 0!==e.batchSize&&(e.batchSize<1||e.batchSize>10))throw new i("批次大小必须在 1-10 之间");if(void 0!==e.reportInterval&&(e.reportInterval<1e3||e.reportInterval>6e4))throw new i("上报间隔必须在 1000-60000 毫秒之间")}static getConfigWithDefaults(e){return{appid:e.appid,channel:e.channel,debug:e.debug??!1,timeout:e.timeout??1e4,maxRetries:e.maxRetries??3,batchSize:e.batchSize??10,reportInterval:e.reportInterval??5e3}}}e.BackupType=void 0,(a=e.BackupType||(e.BackupType={})).MANUAL="manual",a.AUTO="auto",a.CHECKPOINT="checkpoint";const n={[e.BackupType.MANUAL]:1,[e.BackupType.AUTO]:2,[e.BackupType.CHECKPOINT]:3},o={1:e.BackupType.MANUAL,2:e.BackupType.AUTO,3:e.BackupType.CHECKPOINT};var d=async(e,t)=>{let s="function"==typeof t?await t(e):t;if(s)return"bearer"===e.scheme?`Bearer ${s}`:"basic"===e.scheme?`Basic ${btoa(s)}`:s},h={bodySerializer:e=>JSON.stringify(e,((e,t)=>"bigint"==typeof t?t.toString():t))},c=({allowReserved:e,explode:t,name:s,style:a,value:i})=>{if(!t){let t=(e?i:i.map((e=>encodeURIComponent(e)))).join((e=>{switch(e){case"form":default:return",";case"pipeDelimited":return"|";case"spaceDelimited":return"%20"}})(a));switch(a){case"label":return`.${t}`;case"matrix":return`;${s}=${t}`;case"simple":return t;default:return`${s}=${t}`}}let r=(e=>{switch(e){case"label":return".";case"matrix":return";";case"simple":return",";default:return"&"}})(a),n=i.map((t=>"label"===a||"simple"===a?e?t:encodeURIComponent(t):l({allowReserved:e,name:s,value:t}))).join(r);return"label"===a||"matrix"===a?r+n:n},l=({allowReserved:e,name:t,value:s})=>{if(null==s)return"";if("object"==typeof s)throw new Error("Deeply-nested arrays/objects aren’t supported. Provide your own `querySerializer()` to handle these.");return`${t}=${e?s:encodeURIComponent(s)}`},u=({allowReserved:e,explode:t,name:s,style:a,value:i})=>{if(i instanceof Date)return`${s}=${i.toISOString()}`;if("deepObject"!==a&&!t){let t=[];Object.entries(i).forEach((([s,a])=>{t=[...t,s,e?a:encodeURIComponent(a)]}));let r=t.join(",");switch(a){case"form":return`${s}=${r}`;case"label":return`.${r}`;case"matrix":return`;${s}=${r}`;default:return r}}let r=(e=>{switch(e){case"label":return".";case"matrix":return";";case"simple":return",";default:return"&"}})(a),n=Object.entries(i).map((([t,i])=>l({allowReserved:e,name:"deepObject"===a?`${s}[${t}]`:t,value:i}))).join(r);return"label"===a||"matrix"===a?r+n:n},p=/\{[^{}]+\}/g,g=({allowReserved:e,array:t,object:s}={})=>a=>{let i=[];if(a&&"object"==typeof a)for(let r in a){let n=a[r];if(null!=n)if(Array.isArray(n)){let s=c({allowReserved:e,explode:!0,name:r,style:"form",value:n,...t});s&&i.push(s)}else if("object"==typeof n){let t=u({allowReserved:e,explode:!0,name:r,style:"deepObject",value:n,...s});t&&i.push(t)}else{let t=l({allowReserved:e,name:r,value:n});t&&i.push(t)}}return i.join("&")},f=e=>w({baseUrl:e.baseUrl,path:e.path,query:e.query,querySerializer:"function"==typeof e.querySerializer?e.querySerializer:g(e.querySerializer),url:e.url}),w=({baseUrl:e,path:t,query:s,querySerializer:a,url:i})=>{let r=(e??"")+(i.startsWith("/")?i:`/${i}`);t&&(r=(({path:e,url:t})=>{let s=t,a=t.match(p);if(a)for(let t of a){let a=!1,i=t.substring(1,t.length-1),r="simple";i.endsWith("*")&&(a=!0,i=i.substring(0,i.length-1)),i.startsWith(".")?(i=i.substring(1),r="label"):i.startsWith(";")&&(i=i.substring(1),r="matrix");let n=e[i];if(null==n)continue;if(Array.isArray(n)){s=s.replace(t,c({explode:a,name:i,style:r,value:n}));continue}if("object"==typeof n){s=s.replace(t,u({explode:a,name:i,style:r,value:n}));continue}if("matrix"===r){s=s.replace(t,`;${l({name:i,value:n})}`);continue}let o=encodeURIComponent("label"===r?`.${n}`:n);s=s.replace(t,o)}return s})({path:t,url:r}));let n=s?a(s):"";return n.startsWith("?")&&(n=n.substring(1)),n&&(r+=`?${n}`),r},I=(e,t)=>{let s={...e,...t};return s.baseUrl?.endsWith("/")&&(s.baseUrl=s.baseUrl.substring(0,s.baseUrl.length-1)),s.headers=y(e.headers,t.headers),s},y=(...e)=>{let t=new Headers;for(let s of e){if(!s||"object"!=typeof s)continue;let e=s instanceof Headers?s.entries():Object.entries(s);for(let[s,a]of e)if(null===a)t.delete(s);else if(Array.isArray(a))for(let e of a)t.append(s,e);else void 0!==a&&t.set(s,"object"==typeof a?JSON.stringify(a):a)}return t},E=class{_fns;constructor(){this._fns=[]}clear(){this._fns=[]}getInterceptorIndex(e){return"number"==typeof e?this._fns[e]?e:-1:this._fns.indexOf(e)}exists(e){let t=this.getInterceptorIndex(e);return!!this._fns[t]}eject(e){let t=this.getInterceptorIndex(e);this._fns[t]&&(this._fns[t]=null)}update(e,t){let s=this.getInterceptorIndex(e);return!!this._fns[s]&&(this._fns[s]=t,e)}use(e){return this._fns=[...this._fns,e],this._fns.length-1}},S=g({allowReserved:!1,array:{explode:!0,style:"form"},object:{explode:!0,style:"deepObject"}}),b={"Content-Type":"application/json"},D=(e={})=>({...h,headers:b,parseAs:"auto",querySerializer:S,...e});const A=((e={})=>{let t=I(D(),e),s=()=>({...t}),a={error:new E,request:new E,response:new E},i=async e=>{let s={...t,...e,fetch:e.fetch??t.fetch??globalThis.fetch,headers:y(t.headers,e.headers)};s.security&&await(async({security:e,...t})=>{for(let s of e){let e=await d(s,t.auth);if(!e)continue;let a=s.name??"Authorization";switch(s.in){case"query":t.query||(t.query={}),t.query[a]=e;break;case"cookie":t.headers.append("Cookie",`${a}=${e}`);break;default:t.headers.set(a,e)}return}})({...s,security:s.security}),s.body&&s.bodySerializer&&(s.body=s.bodySerializer(s.body)),(void 0===s.body||""===s.body)&&s.headers.delete("Content-Type");let i=f(s),r={redirect:"follow",...s},n=new Request(i,r);for(let e of a.request._fns)e&&(n=await e(n,s));let o=s.fetch,h=await o(n);for(let e of a.response._fns)e&&(h=await e(h,n,s));let c={request:n,response:h};if(h.ok){if(204===h.status||"0"===h.headers.get("Content-Length"))return"data"===s.responseStyle?{}:{data:{},...c};let e=("auto"===s.parseAs?(e=>{if(!e)return"stream";let t=e.split(";")[0]?.trim();if(t){if(t.startsWith("application/json")||t.endsWith("+json"))return"json";if("multipart/form-data"===t)return"formData";if(["application/","audio/","image/","video/"].some((e=>t.startsWith(e))))return"blob";if(t.startsWith("text/"))return"text"}})(h.headers.get("Content-Type")):s.parseAs)??"json";if("stream"===e)return"data"===s.responseStyle?h.body:{data:h.body,...c};let t=await h[e]();return"json"===e&&(s.responseValidator&&await s.responseValidator(t),s.responseTransformer&&(t=await s.responseTransformer(t))),"data"===s.responseStyle?t:{data:t,...c}}let l=await h.text();try{l=JSON.parse(l)}catch{}let u=l;for(let e of a.error._fns)e&&(u=await e(l,h,n,s));if(u=u||{},s.throwOnError)throw u;return"data"===s.responseStyle?void 0:{error:u,...c}};return{buildUrl:f,connect:e=>i({...e,method:"CONNECT"}),delete:e=>i({...e,method:"DELETE"}),get:e=>i({...e,method:"GET"}),getConfig:s,head:e=>i({...e,method:"HEAD"}),interceptors:a,options:e=>i({...e,method:"OPTIONS"}),patch:e=>i({...e,method:"PATCH"}),post:e=>i({...e,method:"POST"}),put:e=>i({...e,method:"PUT"}),request:i,setConfig:e=>(t=I(t,e),s()),trace:e=>i({...e,method:"TRACE"})}})(D());class m{constructor(){this.debug=!1}setDebugMode(e){this.debug=e}setAdCloseCallback(e){this.adCloseCallback=e}cleanup(){this.adCloseCallback=void 0}triggerAdCloseCallback(t){if(this.debug,!this.adCloseCallback)return void this.debug;let s;if("string"==typeof t){const e=parseInt(t,10);if(isNaN(e))return void this.debug;s=e}else s=t;s!==e.AdCloseType.COMPLETED&&s!==e.AdCloseType.CANCELLED&&(this.debug,s=e.AdCloseType.CANCELLED);try{this.adCloseCallback(s),this.debug}catch(e){this.debug}}isAndroidEnvironment(){return!!window.DsmJSInterface?.showAd}isIOSEnvironment(){return!!window.webkit?.messageHandlers?.showAd}hasNativeSupport(){return this.isAndroidEnvironment()||this.isIOSEnvironment()}}class v extends m{constructor(){super(),this.setupGlobalCallbacks()}detectNativeEnvironment(){return{isAndroid:this.isAndroidEnvironment(),isIOS:this.isIOSEnvironment(),hasNativeSupport:this.hasNativeSupport()}}showAd(e){const t=this.detectNativeEnvironment();if(!t.hasNativeSupport)throw new Error("未检测到原生环境，无法显示广告");this.setAdCloseCallback(e),this.debug;try{t.isAndroid?(this.debug,window.DsmJSInterface?.showAd()):t.isIOS&&(this.debug,window.webkit?.messageHandlers?.showAd?.postMessage({body:""})),this.debug}catch(e){throw this.debug,new Error(`显示广告失败: ${e instanceof Error?e.message:String(e)}`)}}cleanup(){super.cleanup();const e=this.getTargetWindow();e.closeAd&&delete e.closeAd;try{window.top&&window!==window.top&&delete window.top[v.CROSS_IFRAME_KEY],window[v.CROSS_IFRAME_KEY]&&delete window[v.CROSS_IFRAME_KEY]}catch(e){this.debug}}setupGlobalCallbacks(){this.getTargetWindow().closeAd=e=>{this.debug,this.adCloseCallback||window!==window.top?this.adCloseCallback&&this.triggerAdCloseCallback(e):this.handleCrossIframeCallback(e)},window!==window.top&&window.top&&this.setupCrossIframeCallback()}getTargetWindow(){return window.top||window}setupCrossIframeCallback(){try{window.top&&(window.top[v.CROSS_IFRAME_KEY]={callback:e=>{this.adCloseCallback&&this.triggerAdCloseCallback(e)},debug:this.debug})}catch(e){this.debug}}handleCrossIframeCallback(e){try{const t=window[v.CROSS_IFRAME_KEY];t&&t.callback?(this.debug,t.callback(e)):this.debug}catch(e){this.debug}}}v.CROSS_IFRAME_KEY="__ANYIGAME_AD_SDK_CALLBACK__";const _="ad_sdk_user_info",R="ad_sdk_session_id";class C{static saveUserInfo(e){try{const t=JSON.stringify(e);localStorage.setItem(_,t)}catch(e){}}static getUserInfo(){try{const e=localStorage.getItem(_);if(!e)return null;const t=JSON.parse(e);return Date.now()>t.cachedAt+t.expiresIn?(this.removeUserInfo(),null):t}catch(e){return null}}static removeUserInfo(){try{localStorage.removeItem(_)}catch(e){}}static saveSessionId(e){try{localStorage.setItem(R,e)}catch(e){}}static getSessionId(){try{return localStorage.getItem(R)}catch(e){return null}}static removeSessionId(){try{localStorage.removeItem(R)}catch(e){}}static clearAll(){this.removeUserInfo(),this.removeSessionId()}}class T{constructor(e){this.cachedUserInfo=null,this.config=e}async initialize(){try{const e=C.getUserInfo();if(e)return this.cachedUserInfo=e,void this.config.debug;const t={...await this.fetchUserInfoFromAPI(),cachedAt:Date.now(),expiresIn:864e5};this.cachedUserInfo=t,C.saveUserInfo(t),this.config.debug}catch(e){const t=`用户模块初始化失败: ${e instanceof Error?e.message:String(e)}`;throw this.config.debug,new Error(t)}}getUserInfo(){if(!this.cachedUserInfo)return null;if(Date.now()>this.cachedUserInfo.cachedAt+this.cachedUserInfo.expiresIn)return this.clearCache(),null;const{cachedAt:e,expiresIn:t,...s}=this.cachedUserInfo;return s}async refreshUserInfo(){try{this.clearCache();const e=await this.fetchUserInfoFromAPI(),t={...e,cachedAt:Date.now(),expiresIn:864e5};return this.cachedUserInfo=t,C.saveUserInfo(t),this.config.debug,e}catch(e){const t=`刷新用户信息失败: ${e instanceof Error?e.message:String(e)}`;throw this.config.debug,new Error(t)}}clearCache(){this.cachedUserInfo=null,C.removeUserInfo(),this.config.debug}isUserInfoCached(){if(!this.cachedUserInfo)return!1;return Date.now()<=this.cachedUserInfo.cachedAt+this.cachedUserInfo.expiresIn}getUserId(){const e=this.getUserInfo();return e?.id??null}cleanup(){this.cachedUserInfo=null}async fetchUserInfoFromAPI(){try{this.config.debug;const t=await(e?.client??A).get({url:"/api/user/me",...e});if(this.config.debug,t.data?.data)return t.data.data;throw new Error(`API 返回的用户信息格式不正确: ${JSON.stringify(t)}`)}catch(e){this.config.debug;const t=`获取用户信息失败: ${e instanceof Error?e.message:String(e)}`;throw new Error(t)}var e}}var k,M,P,U;!function(e){e.IDLE="idle",e.REQUESTING="requesting",e.SHOWING="showing"}(k||(k={}));class K{constructor(e,t,s){this.currentState=k.IDLE,this.adapter=e,this.config=t,this.eventModule=s}async showAd(e){if(this.currentState!==k.IDLE){const e=new Error(`广告正在${this.currentState===k.REQUESTING?"请求":"展示"}中，请稍后再试`);throw this.config.debug,e}try{this.currentState=k.REQUESTING,this.currentCallback=e,this.eventModule.reportEvent(s.AD_REQUEST,{custom_data:{request_time:Date.now()}}),this.config.debug,this.adapter.showAd(this.handleAdClose.bind(this)),this.currentState=k.SHOWING,this.eventModule.reportEvent(s.AD_IMPRESSION,{custom_data:{impression_time:Date.now()}}),this.config.debug}catch(e){this.currentState=k.IDLE,this.currentCallback=void 0,this.eventModule.reportEvent(s.AD_IMPRESSION_FAILED,{error_message:e instanceof Error?e.message:String(e),custom_data:{error_time:Date.now()}});const t=`显示广告失败: ${e instanceof Error?e.message:String(e)}`;throw this.config.debug,new Error(t)}}getAdState(){return this.currentState}canShowAd(){return this.currentState===k.IDLE}resetState(){this.config.debug,this.currentState=k.IDLE,this.currentCallback=void 0}cleanup(){this.resetState()}handleAdClose(t){if(this.config.debug,this.currentState!==k.SHOWING&&this.currentState!==k.REQUESTING)return this.config.debug,void this.resetState();try{if(this.config.debug,this.eventModule.reportEvent(s.AD_CLOSE,{custom_data:{close_type:e.AdCloseType[t],close_time:Date.now(),is_completed:t===e.AdCloseType.COMPLETED}}),t===e.AdCloseType.COMPLETED&&this.eventModule.reportEvent(s.AD_REWARD_GRANT,{reward_name:"广告奖励",reward_amount:1,custom_data:{reward_time:Date.now()}}),this.config.debug,this.currentCallback)try{this.config.debug,this.currentCallback(t),this.config.debug}catch(e){this.config.debug}else this.config.debug}catch(e){this.config.debug}finally{this.config.debug,this.resetState(),this.config.debug}}}function N(){return"undefined"!=typeof crypto&&crypto.randomUUID?crypto.randomUUID():"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){const t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}))}class L{constructor(e,t){this.eventQueue=[],this.isReporting=!1,this.MAX_QUEUE_SIZE=50,this.config=e,this.getUserId=t,this.sessionId=this.initializeSessionId(),this.MAX_RETRY_COUNT=e.maxRetries??3,this.BATCH_SIZE=Math.min(e.batchSize??10,10),this.REPORT_INTERVAL=e.reportInterval??5e3,this.startPeriodicReporting()}reportEvent(e,t={}){const s=this.getUserId();if(!s)return void this.config.debug;const a={event_name:e,event_time:Date.now(),user_id:s,game_id:parseInt(this.config.appid,10),channel_id:parseInt(this.config.channel,10),client_event_id:`ceid-${N()}`,session_id:this.sessionId,...t};this.addToQueue(a),this.config.debug}async flushEvents(){if(!this.isReporting&&0!==this.eventQueue.length){this.isReporting=!0;try{const t=this.eventQueue.splice(0,this.BATCH_SIZE),s=t.map((e=>e.event));this.config.debug;const a=await(e={body:s,headers:{"X-Channel-Code":this.config.channel}},(e.client??A).post({url:"/api/adevent/report",...e,headers:{"Content-Type":"application/json",...e.headers}}));this.config.debug,a.data?.data?.failures&&a.data.data.failures.length>0&&this.handleFailedEvents(t,a.data.data.failures)}catch(e){this.config.debug;const t=this.eventQueue.splice(0,this.BATCH_SIZE);this.handleRetryEvents(t)}finally{this.isReporting=!1}var e}}getQueueStatus(){return{queueSize:this.eventQueue.length,isReporting:this.isReporting}}cleanup(){this.stopPeriodicReporting(),this.eventQueue.length>0&&this.flushEvents().catch((e=>{this.config.debug})),this.eventQueue=[]}initializeSessionId(){let e=C.getSessionId();return e||(e=`sess-${N()}`,C.saveSessionId(e)),e}addToQueue(e){this.eventQueue.length>=this.MAX_QUEUE_SIZE&&(this.eventQueue.shift(),this.config.debug),this.eventQueue.push({event:e,retryCount:0}),this.eventQueue.length>=this.BATCH_SIZE&&this.flushEvents()}handleFailedEvents(e,t){t.forEach((t=>{if(void 0!==t.index&&t.index<e.length){const s=e[t.index];s&&s.retryCount<this.MAX_RETRY_COUNT?(s.retryCount++,this.eventQueue.unshift(s),this.config.debug):s&&this.config.debug}}))}handleRetryEvents(e){e.forEach((e=>{e.retryCount<this.MAX_RETRY_COUNT?(e.retryCount++,this.eventQueue.unshift(e)):this.config.debug}))}startPeriodicReporting(){this.reportTimer=window.setInterval((()=>{this.eventQueue.length>0&&this.flushEvents()}),this.REPORT_INTERVAL)}stopPeriodicReporting(){this.reportTimer&&(window.clearInterval(this.reportTimer),this.reportTimer=void 0)}}!function(e){e.STOPPED="stopped",e.RUNNING="running",e.ERROR="error"}(M||(M={}));class O{constructor(e){this.state=M.STOPPED,this.heartbeatCount=0,this.HEARTBEAT_INTERVAL=3e4,this.config=e}start(){this.state!==M.RUNNING?(this.state=M.RUNNING,this.startTime=Date.now(),this.heartbeatCount=0,this.config.debug,this.sendHeartbeatSafely(),this.heartbeatTimer=window.setInterval((()=>{this.sendHeartbeatSafely()}),this.HEARTBEAT_INTERVAL)):this.config.debug}stop(){this.state!==M.STOPPED&&(this.config.debug,this.state=M.STOPPED,this.heartbeatTimer&&(window.clearInterval(this.heartbeatTimer),this.heartbeatTimer=void 0))}getStatus(){const e=this.startTime?Date.now()-this.startTime:0;return{state:this.state,heartbeatCount:this.heartbeatCount,uptime:e}}cleanup(){this.stop(),this.heartbeatCount=0,this.startTime=void 0,this.config.debug}async sendHeartbeatSafely(){try{await this.sendHeartbeat()}catch(e){this.config.debug,this.state=M.ERROR,setTimeout((()=>{this.state===M.ERROR&&(this.state=M.RUNNING)}),this.HEARTBEAT_INTERVAL)}}async sendHeartbeat(){this.heartbeatCount++;const e={heartbeat_count:this.heartbeatCount,uptime_seconds:this.startTime?Math.floor((Date.now()-this.startTime)/1e3):0,sdk_version:"1.0.4",timestamp:Date.now()};this.config.debug;await(t={body:{custom_data:e}},(t?.client??A).post({url:"/api/heartbeat/heartbeat",...t,headers:{"Content-Type":"application/json",...t?.headers}}));var t;this.config.debug}}!function(e){e.IDLE="idle",e.BACKING_UP="backing_up",e.RETRIEVING="retrieving",e.ERROR="error"}(P||(P={}));class x{constructor(e,t){this.state=P.IDLE,this.config=e,this.getUserId=t}async backupData(t,s,a={}){if(this.state!==P.IDLE)throw new Error(`玩家数据模块正在${this.state===P.BACKING_UP?"备份":"检索"}中，请稍后再试`);if(!this.getUserId())throw new Error("用户未登录，无法备份数据");try{this.state=P.BACKING_UP,this.config.debug;const i=a.backupType||e.BackupType.MANUAL,r={game_id:parseInt(this.config.appid,10),backup_key:t,backup_data:s,backup_type:n[i],device_info:a.deviceInfo||{platform:"web",user_agent:navigator.userAgent,timestamp:Date.now()}};a.description&&(r.description=a.description);const d=await(e=>(e.client??A).post({url:"/api/player-data/backup",...e,headers:{"Content-Type":"application/json",...e.headers}}))({body:r});if(0!==d.data?.code)throw new Error(d.data?.msg||"备份数据失败");const h=d.data.data;if(!h)throw new Error("备份响应数据格式错误");const c={backupId:h.backup_id||0,backupKey:h.backup_key||t,dataSize:h.data_size||0,backupType:o[h.backup_type||1]||e.BackupType.MANUAL,backupTypeDescription:h.backup_type_description||"",createdAt:h.created_at||"",checksum:h.checksum||""};return this.config.debug,c}catch(e){this.state=P.ERROR;const t=`备份数据失败: ${e instanceof Error?e.message:String(e)}`;throw this.config.debug,new Error(t)}finally{this.state!==P.ERROR&&(this.state=P.IDLE)}}async retrieveData(e){if(this.state!==P.IDLE)throw new Error(`玩家数据模块正在${this.state===P.BACKING_UP?"备份":"检索"}中，请稍后再试`);if(!this.getUserId())throw new Error("用户未登录，无法检索数据");try{this.state=P.RETRIEVING,this.config.debug;const s=await(t={path:{backup_key:e},query:{game_id:parseInt(this.config.appid,10)}},(t.client??A).get({url:"/api/player-data/retrieve/{backup_key}",...t}));if(0!==s.data?.code)throw new Error(s.data?.msg||"检索数据失败");const a=s.data.data;if(!a)throw new Error("检索响应数据格式错误");const i=this.convertPlayerBackupDataToResult(a);return this.config.debug,i}catch(e){this.state=P.ERROR;const t=`检索数据失败: ${e instanceof Error?e.message:String(e)}`;throw this.config.debug,new Error(t)}finally{this.state!==P.ERROR&&(this.state=P.IDLE)}var t}async retrieveAllData(){if(this.state!==P.IDLE)throw new Error(`玩家数据模块正在${this.state===P.BACKING_UP?"备份":"检索"}中，请稍后再试`);if(!this.getUserId())throw new Error("用户未登录，无法检索数据");try{this.state=P.RETRIEVING,this.config.debug;const t=await(e={query:{game_id:parseInt(this.config.appid,10)}},(e.client??A).get({url:"/api/player-data/retrieve",...e}));if(0!==t.data?.code)throw new Error(t.data?.msg||"检索所有数据失败");const s=t.data.data;if(!s||!s.backups)return[];const a=s.backups.map((e=>this.convertPlayerBackupDataToResult(e)));return this.config.debug,a}catch(e){this.state=P.ERROR;const t=`检索所有数据失败: ${e instanceof Error?e.message:String(e)}`;throw this.config.debug,new Error(t)}finally{this.state!==P.ERROR&&(this.state=P.IDLE)}var e}async getStats(){if(!this.getUserId())throw new Error("用户未登录，无法获取统计信息");try{this.config.debug;const s=await(t={query:{game_id:parseInt(this.config.appid,10)}},(t.client??A).get({url:"/api/player-data/stats",...t}));if(0!==s.data?.code)throw new Error(s.data?.msg||"获取统计信息失败");const a=s.data.data;if(!a)throw new Error("统计信息响应数据格式错误");const i={[e.BackupType.MANUAL]:0,[e.BackupType.AUTO]:0,[e.BackupType.CHECKPOINT]:0};a.backup_types&&Object.entries(a.backup_types).forEach((([e,t])=>{const s=parseInt(e,10),a=o[s];a&&(i[a]=t)}));const r={totalBackups:a.total_backups||0,totalDataSize:a.total_data_size||0,backupTypes:i,latestBackup:a.latest_backup||void 0};return this.config.debug,r}catch(e){const t=`获取统计信息失败: ${e instanceof Error?e.message:String(e)}`;throw this.config.debug,new Error(t)}var t}getModuleStatus(){return{state:this.state}}cleanup(){this.state=P.IDLE,this.config.debug}convertPlayerBackupDataToResult(t){return{backupKey:t.backup_key||"",backupData:t.backup_data||{},dataVersion:t.data_version||1,backupType:o[t.backup_type||1]||e.BackupType.MANUAL,backupTypeDescription:t.backup_type_description||"",description:t.description?t.description:void 0,createdAt:t.created_at||"",updatedAt:t.updated_at||"",dataSize:t.data_size||0}}}!function(e){e.UNINITIALIZED="uninitialized",e.INITIALIZING="initializing",e.INITIALIZED="initialized",e.ERROR="error"}(U||(U={}));class ${static getInstance(){return $.instance||($.instance=new $),$.instance}constructor(){this.state=U.UNINITIALIZED}async init(e,t){if(this.state===U.INITIALIZING)throw new i("SDK 正在初始化中，请勿重复调用");if(this.state!==U.INITIALIZED){this.state=U.INITIALIZING;try{r.validateConfig(e);const a=r.getConfigWithDefaults(e);this.config=a;try{A.setConfig({baseUrl:"https://open.anyigame.cn",headers:{"X-Channel-Code":this.config.channel,"Content-Type":"application/json"}}),this.config.debug}catch(e){throw this.config.debug,new i(`API 客户端配置失败: ${e instanceof Error?e.message:String(e)}`)}this.adapter=t||new v,"setDebugMode"in this.adapter&&"function"==typeof this.adapter.setDebugMode&&this.adapter.setDebugMode(a.debug);const n=this.adapter.detectNativeEnvironment();if(!n.hasNativeSupport)throw new i("未检测到支持的原生环境");e.debug,this.userModule=new T(e),await this.userModule.initialize(),this.eventModule=new L(e,(()=>this.userModule?.getUserId()??null)),this.eventModule.reportEvent(s.SDK_INIT_START,{custom_data:{config:{appid:this.config.appid,channel:this.config.channel,debug:this.config.debug},nativeEnv:n}}),this.adModule=new K(this.adapter,e,this.eventModule),this.playerDataModule=new x(e,(()=>this.userModule?.getUserId()||null)),this.state=U.INITIALIZED,this.heartbeatModule=new O(e),this.heartbeatModule.start(),this.eventModule.reportEvent(s.SDK_INIT_SUCCESS,{custom_data:{init_duration:Date.now(),user_id:this.userModule.getUserId()}}),e.debug}catch(t){this.state=U.ERROR,this.initError=t instanceof Error?t:new Error(String(t)),this.eventModule&&this.eventModule.reportEvent(s.SDK_INIT_FAILED,{error_message:this.initError.message,custom_data:{error_time:Date.now()}});const a=`SDK 初始化失败: ${this.initError.message}`;throw e.debug,new i(a,void 0,this.initError)}}else this.config}getUserInfo(){return this.checkInitialized(),this.userModule?.getUserInfo()??null}async refreshUserInfo(){if(this.checkInitialized(),!this.userModule)throw new i("用户模块未初始化");return await this.userModule.refreshUserInfo()}async getGameConfig(){if(this.checkInitialized(),!this.config)throw new i("SDK 配置未初始化");try{const t=await(e={client:A,query:{code:this.config.appid},headers:{"X-Channel-Code":this.config.channel}},(e.client??A).get({url:"/api/game/config",...e}));if(t.data?.data)return t.data.data;throw new i("API 返回的游戏配置格式不正确")}catch(e){const t=`获取游戏配置失败: ${e instanceof Error?e.message:String(e)}`;throw this.config.debug,new i(t,void 0,e instanceof Error?e:void 0)}var e}async showAd(e){if(this.checkInitialized(),!this.adModule)throw new i("广告模块未初始化");await this.adModule.showAd(e)}canShowAd(){return!(this.state!==U.INITIALIZED||!this.adModule)&&this.adModule.canShowAd()}async backupPlayerData(e,t,s){if(this.checkInitialized(),!this.playerDataModule)throw new i("玩家数据模块未初始化");return await this.playerDataModule.backupData(e,t,s)}async retrievePlayerData(e){if(this.checkInitialized(),!this.playerDataModule)throw new i("玩家数据模块未初始化");return await this.playerDataModule.retrieveData(e)}async retrieveAllPlayerData(){if(this.checkInitialized(),!this.playerDataModule)throw new i("玩家数据模块未初始化");return await this.playerDataModule.retrieveAllData()}async getPlayerDataStats(){if(this.checkInitialized(),!this.playerDataModule)throw new i("玩家数据模块未初始化");return await this.playerDataModule.getStats()}resetState(){this.config,this.adModule?this.adModule.resetState():this.config}getState(){return this.state}getVersion(){return"1.0.4"}getDebugInfo(){return{state:this.state,version:this.getVersion(),config:this.config,userInfo:this.getUserInfo(),canShowAd:this.canShowAd(),adState:this.adModule?.getAdState(),eventQueueStatus:this.eventModule?.getQueueStatus(),heartbeatStatus:this.heartbeatModule?.getStatus(),playerDataStatus:this.playerDataModule?.getModuleStatus(),initError:this.initError?.message}}destroy(){this.config;this.heartbeatModule?.cleanup(),this.playerDataModule?.cleanup(),this.eventModule?.cleanup(),this.adModule?.cleanup(),this.userModule?.cleanup(),this.adapter?.cleanup(),this.state=U.UNINITIALIZED,this.config=void 0,this.adapter=void 0,this.userModule=void 0,this.adModule=void 0,this.eventModule=void 0,this.heartbeatModule=void 0,this.playerDataModule=void 0,this.initError=void 0,$.instance=null}checkInitialized(){if(this.state===U.UNINITIALIZED)throw new i("SDK 未初始化，请先调用 init() 方法");if(this.state===U.INITIALIZING)throw new i("SDK 正在初始化中，请等待初始化完成");if(this.state===U.ERROR)throw new i(`SDK 初始化失败: ${this.initError?.message??"未知错误"}`)}}$.instance=null;class G{static async initInTopWindow(e,t){if(window!==window.top)throw new Error("initInTopWindow 必须在顶层窗口中调用");const s=$.getInstance();await s.init(e,t),window[this.GLOBAL_SDK_KEY]={sdk:s,config:e,initialized:!0,version:"1.0.4"},this.setupMessageListener(),e.debug}static getSDKFromTopWindow(){try{const e=window.top;if(!e)return null;const t=e[this.GLOBAL_SDK_KEY];return t&&t.initialized&&t.sdk?t.sdk:null}catch(e){return null}}static isSDKInitializedInTopWindow(){try{const e=window.top;if(!e)return!1;const t=e[this.GLOBAL_SDK_KEY];return!(!t||!t.initialized)}catch(e){return!1}}static async showAdFromIframe(e){const t=this.getSDKFromTopWindow();return t?t.showAd(e):new Promise(((t,s)=>{if(!window.top)return void s(new Error("无法访问顶层窗口"));const a=`showAd_${Date.now()}_${Math.random().toString(36).substring(2,11)}`,i=r=>{r.data?.type===`${this.MESSAGE_PREFIX}showAd-response`&&r.data?.requestId===a?(window.removeEventListener("message",i),r.data.success?t():s(new Error(r.data.error||"显示广告失败"))):r.data?.type===`${this.MESSAGE_PREFIX}adClose`&&r.data?.requestId===a&&e&&e(r.data.closeType)};window.addEventListener("message",i),window.top.postMessage({type:`${this.MESSAGE_PREFIX}showAd`,requestId:a,hasCallback:!!e},"*"),setTimeout((()=>{window.removeEventListener("message",i),s(new Error("显示广告请求超时"))}),1e4)}))}static cleanup(){if(window===window.top){const e=window[this.GLOBAL_SDK_KEY];e&&e.sdk&&e.sdk.destroy(),delete window[this.GLOBAL_SDK_KEY]}}static setupMessageListener(){window.addEventListener("message",(e=>{if(!e.data?.type?.startsWith(this.MESSAGE_PREFIX))return;const{type:t}=e.data;t===`${this.MESSAGE_PREFIX}showAd`?this.handleShowAdRequest(e):t===`${this.MESSAGE_PREFIX}backupPlayerData`?this.handleBackupPlayerDataRequest(e):t===`${this.MESSAGE_PREFIX}retrievePlayerData`?this.handleRetrievePlayerDataRequest(e):t===`${this.MESSAGE_PREFIX}retrieveAllPlayerData`?this.handleRetrieveAllPlayerDataRequest(e):t===`${this.MESSAGE_PREFIX}getPlayerDataStats`&&this.handleGetPlayerDataStatsRequest(e)}))}static async handleShowAdRequest(e){const{requestId:t,hasCallback:s}=e.data,a=e.source;try{const e=window[this.GLOBAL_SDK_KEY];if(!e||!e.sdk)throw new Error("SDK 未初始化");const i=e.sdk,r=s?e=>{a.postMessage({type:`${this.MESSAGE_PREFIX}adClose`,requestId:t,closeType:e},"*")}:void 0;await i.showAd(r),a.postMessage({type:`${this.MESSAGE_PREFIX}showAd-response`,requestId:t,success:!0},"*")}catch(e){a.postMessage({type:`${this.MESSAGE_PREFIX}showAd-response`,requestId:t,success:!1,error:e instanceof Error?e.message:String(e)},"*")}}static async handleBackupPlayerDataRequest(e){const{requestId:t,backupKey:s,data:a,options:i}=e.data,r=e.source;try{const e=window[this.GLOBAL_SDK_KEY];if(!e||!e.sdk)throw new Error("SDK 未初始化");const n=e.sdk,o=await n.backupPlayerData(s,a,i);r.postMessage({type:`${this.MESSAGE_PREFIX}backupPlayerData-response`,requestId:t,success:!0,result:o},"*")}catch(e){r.postMessage({type:`${this.MESSAGE_PREFIX}backupPlayerData-response`,requestId:t,success:!1,error:e instanceof Error?e.message:String(e)},"*")}}static async handleRetrievePlayerDataRequest(e){const{requestId:t,backupKey:s}=e.data,a=e.source;try{const e=window[this.GLOBAL_SDK_KEY];if(!e||!e.sdk)throw new Error("SDK 未初始化");const i=e.sdk,r=await i.retrievePlayerData(s);a.postMessage({type:`${this.MESSAGE_PREFIX}retrievePlayerData-response`,requestId:t,success:!0,result:r},"*")}catch(e){a.postMessage({type:`${this.MESSAGE_PREFIX}retrievePlayerData-response`,requestId:t,success:!1,error:e instanceof Error?e.message:String(e)},"*")}}static async handleRetrieveAllPlayerDataRequest(e){const{requestId:t}=e.data,s=e.source;try{const e=window[this.GLOBAL_SDK_KEY];if(!e||!e.sdk)throw new Error("SDK 未初始化");const a=e.sdk,i=await a.retrieveAllPlayerData();s.postMessage({type:`${this.MESSAGE_PREFIX}retrieveAllPlayerData-response`,requestId:t,success:!0,result:i},"*")}catch(e){s.postMessage({type:`${this.MESSAGE_PREFIX}retrieveAllPlayerData-response`,requestId:t,success:!1,error:e instanceof Error?e.message:String(e)},"*")}}static async handleGetPlayerDataStatsRequest(e){const{requestId:t}=e.data,s=e.source;try{const e=window[this.GLOBAL_SDK_KEY];if(!e||!e.sdk)throw new Error("SDK 未初始化");const a=e.sdk,i=await a.getPlayerDataStats();s.postMessage({type:`${this.MESSAGE_PREFIX}getPlayerDataStats-response`,requestId:t,success:!0,result:i},"*")}catch(e){s.postMessage({type:`${this.MESSAGE_PREFIX}getPlayerDataStats-response`,requestId:t,success:!1,error:e instanceof Error?e.message:String(e)},"*")}}}G.GLOBAL_SDK_KEY="__ANYIGAME_AD_SDK__",G.MESSAGE_PREFIX="anyigame-ad-sdk:";class q{constructor(){this.topWindowSDK=null,this.topWindowSDK=G.getSDKFromTopWindow(),this.isAvailable=this.isAvailable.bind(this),this.showAd=this.showAd.bind(this),this.canShowAd=this.canShowAd.bind(this),this.getUserInfo=this.getUserInfo.bind(this),this.refreshUserInfo=this.refreshUserInfo.bind(this),this.getGameConfig=this.getGameConfig.bind(this),this.getState=this.getState.bind(this),this.getVersion=this.getVersion.bind(this),this.getDebugInfo=this.getDebugInfo.bind(this),this.backupPlayerData=this.backupPlayerData.bind(this),this.retrievePlayerData=this.retrievePlayerData.bind(this),this.retrieveAllPlayerData=this.retrieveAllPlayerData.bind(this),this.getPlayerDataStats=this.getPlayerDataStats.bind(this)}isAvailable(){return G.isSDKInitializedInTopWindow()}async showAd(e){return this.topWindowSDK?this.topWindowSDK.showAd(e):G.showAdFromIframe(e)}canShowAd(){return!!this.topWindowSDK&&this.topWindowSDK.canShowAd()}getUserInfo(){return this.topWindowSDK?this.topWindowSDK.getUserInfo():null}async refreshUserInfo(){if(this.topWindowSDK)return this.topWindowSDK.refreshUserInfo();throw new Error("SDK 不可用")}async getGameConfig(){if(this.topWindowSDK)return this.topWindowSDK.getGameConfig();throw new Error("SDK 不可用")}getState(){return this.topWindowSDK?this.topWindowSDK.getState():"unavailable"}getVersion(){return this.topWindowSDK?this.topWindowSDK.getVersion():"1.0.4"}getDebugInfo(){return this.topWindowSDK?this.topWindowSDK.getDebugInfo():{status:"SDK not available in iframe"}}async backupPlayerData(e,t,s){return this.topWindowSDK?this.topWindowSDK.backupPlayerData(e,t,s):this.sendPlayerDataMessage("backupPlayerData",{backupKey:e,data:t,options:s})}async retrievePlayerData(e){return this.topWindowSDK?this.topWindowSDK.retrievePlayerData(e):this.sendPlayerDataMessage("retrievePlayerData",{backupKey:e})}async retrieveAllPlayerData(){return this.topWindowSDK?this.topWindowSDK.retrieveAllPlayerData():this.sendPlayerDataMessage("retrieveAllPlayerData",{})}async getPlayerDataStats(){return this.topWindowSDK?this.topWindowSDK.getPlayerDataStats():this.sendPlayerDataMessage("getPlayerDataStats",{})}async sendPlayerDataMessage(e,t){return new Promise(((s,a)=>{if(!window.top)return void a(new Error("无法访问顶层窗口"));const i=`${e}_${Date.now()}_${Math.random().toString(36).substring(2,11)}`,r=t=>{t.data?.type===`${G.MESSAGE_PREFIX}${e}-response`&&t.data?.requestId===i&&(window.removeEventListener("message",r),t.data.success?s(t.data.result):a(new Error(t.data.error||`${e} 失败`)))};window.addEventListener("message",r),window.top.postMessage({type:`${G.MESSAGE_PREFIX}${e}`,requestId:i,...t},"*"),setTimeout((()=>{window.removeEventListener("message",r),a(new Error(`${e} 请求超时`))}),1e4)}))}}const W=$.getInstance(),B=W.init.bind(W),z=W.showAd.bind(W),F=W.canShowAd.bind(W),j=W.getUserInfo.bind(W),X=W.refreshUserInfo.bind(W),Q=W.getGameConfig.bind(W),H=W.backupPlayerData.bind(W),V=W.retrievePlayerData.bind(W),Y=W.retrieveAllPlayerData.bind(W),Z=W.getPlayerDataStats.bind(W),J=W.getState.bind(W),ee=W.getVersion.bind(W),te=W.getDebugInfo.bind(W),se=W.resetState.bind(W),ae=W.destroy.bind(W),ie=G.initInTopWindow.bind(G),re=G.isSDKInitializedInTopWindow.bind(G),ne=G.cleanup.bind(G);e.BaseNativeBridgeAdapter=m,e.CrossIframeSDKManager=G,e.CrossIframeSDKProxy=q,e.DsmAdapter=v,e.GameSDK=$,e.SDKConfigValidator=r,e.SDKError=i,e.backupPlayerData=H,e.canShowAd=F,e.cleanupCrossIframeSDK=ne,e.createIframeSDKProxy=function(){return new q},e.default=W,e.destroy=ae,e.getDebugInfo=te,e.getGameConfig=Q,e.getPlayerDataStats=Z,e.getState=J,e.getUserInfo=j,e.getVersion=ee,e.init=B,e.initInTopWindow=ie,e.isSDKInitializedInTopWindow=re,e.refreshUserInfo=X,e.resetState=se,e.retrieveAllPlayerData=Y,e.retrievePlayerData=V,e.showAd=z,Object.defineProperty(e,"__esModule",{value:!0})}));
//# sourceMappingURL=ad-sdk.umd.min.js.map
