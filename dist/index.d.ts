type User = {
    /**
     * 用户ID
     */
    id?: number;
    /**
     * 用户名
     */
    username?: string;
    /**
     * 用户昵称
     */
    nickname?: string;
    /**
     * 用户头像URL
     */
    avatar?: string;
    /**
     * 用户邮箱
     */
    email?: string;
    /**
     * 用户手机号
     */
    mobile?: string;
};
type UserResponse = {
    /**
     * 响应代码 (200表示成功)
     */
    code?: number;
    /**
     * 响应消息
     */
    msg?: string;
    data?: User;
};
type GameConfig = {
    /**
     * 游戏ID（主键）
     */
    id?: number;
    /**
     * 游戏编码
     */
    code?: string;
    /**
     * 游戏名称
     */
    name?: string;
    /**
     * 游戏封面图片URL
     */
    cover?: string;
    /**
     * 关联的渠道ID列表（逗号分隔）
     */
    channel_ids?: string;
    /**
     * 游戏状态（1为启用，0为禁用）
     */
    status?: number;
    /**
     * 游戏最新版本号
     */
    latest_version?: string;
    /**
     * 创建时间
     */
    created_at?: string;
    /**
     * 更新时间
     */
    updated_at?: string;
};
type GameConfigResponse = {
    /**
     * 响应代码 (0表示成功)
     */
    code?: number;
    /**
     * 响应消息
     */
    msg?: string;
    data?: GameConfig;
};
type PlayerDataBackupRequest = {
    /**
     * 游戏ID
     */
    game_id: number;
    /**
     * 备份数据键名，用于区分不同类型的备份数据
     */
    backup_key: string;
    /**
     * 要备份的JSON数据，支持任意结构（最大1MB）
     */
    backup_data: {
        [key: string]: unknown;
    };
    /**
     * 备份类型（1=手动备份, 2=自动备份, 3=关键节点备份）
     */
    backup_type?: 1 | 2 | 3;
    /**
     * 备份描述
     */
    description?: string;
    /**
     * 设备信息
     */
    device_info?: {
        [key: string]: unknown;
    };
};
type PlayerDataBackupResponse = {
    /**
     * 响应代码 (0表示成功)
     */
    code?: number;
    /**
     * 响应消息
     */
    msg?: string;
    data?: {
        /**
         * 备份记录ID
         */
        backup_id?: number;
        /**
         * 备份数据键名
         */
        backup_key?: string;
        /**
         * 数据大小（字节）
         */
        data_size?: number;
        /**
         * 备份类型
         */
        backup_type?: number;
        /**
         * 备份类型描述
         */
        backup_type_description?: string;
        /**
         * 创建时间
         */
        created_at?: string;
        /**
         * 数据校验和（SHA-256）
         */
        checksum?: string;
    };
};
type PlayerDataRetrieveResponse = {
    /**
     * 响应代码 (0表示成功)
     */
    code?: number;
    /**
     * 响应消息
     */
    msg?: string;
    data?: PlayerBackupData;
};
type PlayerDataRetrieveAllResponse = {
    /**
     * 响应代码 (0表示成功)
     */
    code?: number;
    /**
     * 响应消息
     */
    msg?: string;
    data?: {
        /**
         * 备份数据列表
         */
        backups?: Array<PlayerBackupData>;
        /**
         * 总备份数量
         */
        total_count?: number;
    };
};
type PlayerDataStatsResponse = {
    /**
     * 响应代码 (0表示成功)
     */
    code?: number;
    /**
     * 响应消息
     */
    msg?: string;
    data?: {
        /**
         * 总备份数量
         */
        total_backups?: number;
        /**
         * 总数据大小（字节）
         */
        total_data_size?: number;
        /**
         * 按备份类型分组的统计（键为备份类型，值为数量）
         */
        backup_types?: {
            [key: string]: number;
        };
        /**
         * 最新备份时间
         */
        latest_backup?: string;
    };
};
type PlayerBackupData = {
    /**
     * 备份数据键名
     */
    backup_key?: string;
    /**
     * 备份的JSON数据
     */
    backup_data?: {
        [key: string]: unknown;
    };
    /**
     * 数据版本号
     */
    data_version?: number;
    /**
     * 备份类型（1=手动, 2=自动, 3=关键节点）
     */
    backup_type?: number;
    /**
     * 备份类型描述
     */
    backup_type_description?: string;
    /**
     * 备份描述
     */
    description?: string;
    /**
     * 创建时间
     */
    created_at?: string;
    /**
     * 更新时间
     */
    updated_at?: string;
    /**
     * 数据大小（字节）
     */
    data_size?: number;
};

/**
 * SDK 核心类型定义
 */

/**
 * SDK 初始化配置
 */
interface SDKConfig {
    /**
     * 应用/游戏标识符（数字字符串）
     * 必须为纯数字格式，如 "1001"
     * 对应 API 中的 code 参数
     */
    appid: string;
    /**
     * 渠道代码（数字字符串）
     * 必须为纯数字格式，如 "1"
     * 对应 API 中的 X-Channel-Code 头部参数
     */
    channel: string;
    /**
     * 是否启用调试模式
     * 启用后将输出详细的日志信息
     * @default false
     */
    debug?: boolean;
    /**
     * API 请求超时时间（毫秒）
     * @default 10000
     */
    timeout?: number;
    /**
     * 事件上报重试次数
     * @default 3
     */
    maxRetries?: number;
    /**
     * 事件上报批次大小
     * 单次请求最大事件数量，不能超过 10
     * @default 10
     */
    batchSize?: number;
    /**
     * 事件上报间隔时间（毫秒）
     * @default 5000
     */
    reportInterval?: number;
}
/**
 * 用户信息缓存结构
 */
interface CachedUserInfo extends User {
    /** 缓存时间戳 */
    cachedAt: number;
    /** 缓存有效期（毫秒） */
    expiresIn: number;
}
/**
 * 广告关闭类型
 */
declare enum AdCloseType {
    /** 观看完成 */
    COMPLETED = 1,
    /** 用户取消 */
    CANCELLED = 2
}
/**
 * 广告关闭回调函数类型
 */
type AdCloseCallback = (type: AdCloseType) => void;
/**
 * SDK 错误类型
 */
declare class SDKError extends Error {
    code?: number | undefined;
    originalError?: Error | undefined;
    constructor(message: string, code?: number | undefined, originalError?: Error | undefined);
}
/**
 * 原生环境检测结果
 */
interface NativeEnvironment {
    /** 是否为 Android 环境 */
    isAndroid: boolean;
    /** 是否为 iOS 环境 */
    isIOS: boolean;
    /** 是否检测到原生环境 */
    hasNativeSupport: boolean;
}
/**
 * SDK 配置验证工具
 */
declare class SDKConfigValidator {
    /**
     * 验证应用标识符格式
     * @param appid 应用标识符
     * @returns 是否有效
     */
    static isValidAppId(appid: string): boolean;
    /**
     * 验证渠道代码格式
     * @param channelCode 渠道代码
     * @returns 是否有效
     */
    static isValidChannelCode(channelCode: string): boolean;
    /**
     * 验证 SDK 配置
     * @param config SDK 配置
     * @throws {SDKError} 当配置无效时抛出错误
     */
    static validateConfig(config: SDKConfig): void;
    /**
     * 获取配置的默认值
     * @param config 用户配置
     * @returns 合并默认值后的完整配置
     */
    static getConfigWithDefaults(config: SDKConfig): Required<SDKConfig>;
}
/**
 * 备份类型枚举
 * 使用字符串枚举以符合用户偏好
 */
declare enum BackupType {
    /** 手动备份 */
    MANUAL = "manual",
    /** 自动备份 */
    AUTO = "auto",
    /** 关键节点备份 */
    CHECKPOINT = "checkpoint"
}
/**
 * 玩家数据备份选项
 */
interface PlayerDataBackupOptions {
    /** 备份类型 */
    backupType?: BackupType;
    /** 备份描述 */
    description?: string;
    /** 设备信息 */
    deviceInfo?: Record<string, unknown>;
}
/**
 * 玩家数据备份结果
 */
interface PlayerDataBackupResult {
    /** 备份ID */
    backupId: number;
    /** 备份键名 */
    backupKey: string;
    /** 数据大小（字节） */
    dataSize: number;
    /** 备份类型 */
    backupType: BackupType;
    /** 备份类型描述 */
    backupTypeDescription: string;
    /** 创建时间 */
    createdAt: string;
    /** 数据校验和 */
    checksum: string;
}
/**
 * 玩家数据检索结果
 */
interface PlayerDataRetrieveResult {
    /** 备份键名 */
    backupKey: string;
    /** 备份数据 */
    backupData: Record<string, unknown>;
    /** 数据版本号 */
    dataVersion: number;
    /** 备份类型 */
    backupType: BackupType;
    /** 备份类型描述 */
    backupTypeDescription: string;
    /** 备份描述 */
    description?: string | undefined;
    /** 创建时间 */
    createdAt: string;
    /** 更新时间 */
    updatedAt: string;
    /** 数据大小（字节） */
    dataSize: number;
}
/**
 * 玩家数据统计信息
 */
interface PlayerDataStats {
    /** 总备份数量 */
    totalBackups: number;
    /** 总数据大小（字节） */
    totalDataSize: number;
    /** 按备份类型分组的统计 */
    backupTypes: Record<BackupType, number>;
    /** 最新备份时间 */
    latestBackup?: string | undefined;
}

/**
 * 原生桥接适配器接口
 *
 * 这个接口定义了 SDK 与原生应用交互的标准方法。
 * 不同的客户可以实现这个接口来适配他们的原生应用。
 */

/**
 * 原生桥接适配器接口
 */
interface NativeBridgeAdapter {
    /**
     * 检测原生环境
     * @returns 原生环境信息
     */
    detectNativeEnvironment(): NativeEnvironment;
    /**
     * 显示广告
     * @param callback 广告关闭时的回调函数
     * @throws {Error} 当显示广告失败时抛出错误
     */
    showAd(callback: AdCloseCallback): void;
    /**
     * 设置广告关闭回调
     * @param callback 广告关闭回调函数
     */
    setAdCloseCallback(callback: AdCloseCallback): void;
    /**
     * 清理资源
     * 在 SDK 销毁时调用，用于清理事件监听器等资源
     */
    cleanup(): void;
}
/**
 * 抽象适配器基类
 * 提供一些通用的实现和工具方法
 */
declare abstract class BaseNativeBridgeAdapter implements NativeBridgeAdapter {
    protected adCloseCallback?: AdCloseCallback | undefined;
    protected debug: boolean;
    abstract detectNativeEnvironment(): NativeEnvironment;
    abstract showAd(callback: AdCloseCallback): void;
    /**
     * 设置调试模式
     */
    setDebugMode(debug: boolean): void;
    /**
     * 设置广告关闭回调
     */
    setAdCloseCallback(callback: AdCloseCallback): void;
    /**
     * 清理资源
     */
    cleanup(): void;
    /**
     * 触发广告关闭回调
     */
    protected triggerAdCloseCallback(type: number | string): void;
    /**
     * 检查是否为 Android 环境
     */
    protected isAndroidEnvironment(): boolean;
    /**
     * 检查是否为 iOS 环境
     */
    protected isIOSEnvironment(): boolean;
    /**
     * 检查是否有原生支持
     */
    protected hasNativeSupport(): boolean;
}

/**
 * H5 游戏 SDK 主类
 *
 * 采用单例模式，提供统一的 SDK 接口
 */
declare class GameSDK {
    private static instance;
    /**
     * 获取 SDK 实例（单例模式）
     */
    static getInstance(): GameSDK;
    private config?;
    private adapter?;
    private userModule?;
    private adModule?;
    private eventModule?;
    private heartbeatModule?;
    private playerDataModule?;
    private state;
    private initError?;
    /**
     * 私有构造函数，确保单例模式
     */
    private constructor();
    /**
     * 初始化 SDK
     * @param config SDK 配置
     * @param adapter 可选的自定义适配器，如果不提供则使用默认的 DsmAdapter
     */
    init(config: SDKConfig, adapter?: NativeBridgeAdapter): Promise<void>;
    /**
     * 获取用户信息（同步）
     * @returns 用户信息，如果未初始化或用户信息不存在则返回 null
     */
    getUserInfo(): User | null;
    /**
     * 刷新用户信息
     * @returns Promise<User> 最新的用户信息
     */
    refreshUserInfo(): Promise<User>;
    /**
     * 获取游戏配置
     * @returns Promise<GameConfig> 游戏配置信息
     */
    getGameConfig(): Promise<GameConfig>;
    /**
     * 显示广告
     * @param callback 广告关闭时的回调函数
     */
    showAd(callback?: AdCloseCallback): Promise<void>;
    /**
     * 检查是否可以显示广告
     */
    canShowAd(): boolean;
    /**
     * 备份玩家数据
     * @param backupKey 备份数据键名，用于区分不同类型的数据
     * @param data 要备份的数据
     * @param options 备份选项
     * @returns 备份结果
     */
    backupPlayerData(backupKey: string, data: Record<string, unknown>, options?: PlayerDataBackupOptions): Promise<PlayerDataBackupResult>;
    /**
     * 检索特定备份数据
     * @param backupKey 备份数据键名
     * @returns 备份数据
     */
    retrievePlayerData(backupKey: string): Promise<PlayerDataRetrieveResult>;
    /**
     * 检索所有备份数据
     * @returns 所有备份数据列表
     */
    retrieveAllPlayerData(): Promise<PlayerDataRetrieveResult[]>;
    /**
     * 获取备份统计信息
     * @returns 备份统计信息
     */
    getPlayerDataStats(): Promise<PlayerDataStats>;
    /**
     * 强制重置广告状态
     * 用于异常情况下的状态恢复
     */
    resetState(): void;
    /**
     * 获取 SDK 状态
     */
    getState(): string;
    /**
     * 获取 SDK 版本信息
     */
    getVersion(): string;
    /**
     * 获取调试信息
     */
    getDebugInfo(): object;
    /**
     * 销毁 SDK
     * 清理所有资源和事件监听器
     */
    destroy(): void;
    /**
     * 检查 SDK 是否已初始化
     */
    private checkInitialized;
}

/**
 * DSM 客户端适配器实现
 *
 * 基于 H5交互文档.md 实现的具体适配器，用于与当前客户的原生应用交互。
 * 支持跨 iframe 的回调处理。
 */

/**
 * DSM 客户端的原生接口声明
 */
declare global {
    interface Window {
        DsmJSInterface?: {
            showAd(): void;
        };
        webkit?: {
            messageHandlers?: {
                showAd?: {
                    postMessage(data: {
                        body: string;
                    }): void;
                };
            };
        };
        closeAd?: (type: number | string) => void;
    }
}
/**
 * DSM 适配器实现
 */
declare class DsmAdapter extends BaseNativeBridgeAdapter {
    private static readonly CROSS_IFRAME_KEY;
    constructor();
    /**
     * 检测原生环境
     */
    detectNativeEnvironment(): NativeEnvironment;
    /**
     * 显示广告
     */
    showAd(callback: AdCloseCallback): void;
    /**
     * 清理资源
     */
    cleanup(): void;
    /**
     * 设置全局回调函数
     * 支持跨 iframe 的回调处理
     */
    private setupGlobalCallbacks;
    /**
     * 获取目标窗口（用于设置回调）
     */
    private getTargetWindow;
    /**
     * 设置跨 iframe 回调引用
     */
    private setupCrossIframeCallback;
    /**
     * 处理跨 iframe 回调
     */
    private handleCrossIframeCallback;
}

/**
 * 跨 iframe SDK 管理器
 * 负责在顶层窗口初始化 SDK，并处理来自 iframe 的请求
 */
declare class CrossIframeSDKManager {
    private static readonly GLOBAL_SDK_KEY;
    private static readonly MESSAGE_PREFIX;
    /**
     * 在顶层窗口初始化 SDK
     * @param config SDK 配置
     * @param adapter 可选的自定义适配器
     */
    static initInTopWindow(config: SDKConfig, adapter?: NativeBridgeAdapter): Promise<void>;
    /**
     * 从 iframe 中获取顶层窗口的 SDK 实例
     */
    static getSDKFromTopWindow(): GameSDK | null;
    /**
     * 检查顶层窗口是否已初始化 SDK
     */
    static isSDKInitializedInTopWindow(): boolean;
    /**
     * 在 iframe 中显示广告（通过消息传递）
     * @param callback 广告关闭回调
     */
    static showAdFromIframe(callback?: AdCloseCallback): Promise<void>;
    /**
     * 清理跨 iframe SDK 资源
     */
    static cleanup(): void;
    /**
     * 设置消息监听器（在顶层窗口中）
     */
    private static setupMessageListener;
    /**
     * 处理来自 iframe 的显示广告请求
     */
    private static handleShowAdRequest;
    /**
     * 处理来自 iframe 的备份玩家数据请求
     */
    private static handleBackupPlayerDataRequest;
    /**
     * 处理来自 iframe 的检索玩家数据请求
     */
    private static handleRetrievePlayerDataRequest;
    /**
     * 处理来自 iframe 的检索所有玩家数据请求
     */
    private static handleRetrieveAllPlayerDataRequest;
    /**
     * 处理来自 iframe 的获取玩家数据统计请求
     */
    private static handleGetPlayerDataStatsRequest;
}
/**
 * 跨 iframe SDK 代理类
 * 在 iframe 中使用，提供与普通 SDK 相同的接口
 */
declare class CrossIframeSDKProxy {
    private topWindowSDK;
    constructor();
    /**
     * 检查 SDK 是否可用
     */
    isAvailable(): boolean;
    /**
     * 显示广告
     */
    showAd(callback?: AdCloseCallback): Promise<void>;
    /**
     * 检查是否可以显示广告
     */
    canShowAd(): boolean;
    /**
     * 获取用户信息
     */
    getUserInfo(): User | null;
    /**
     * 刷新用户信息
     */
    refreshUserInfo(): Promise<User>;
    /**
     * 获取游戏配置
     */
    getGameConfig(): Promise<GameConfig>;
    /**
     * 获取 SDK 状态
     */
    getState(): string;
    /**
     * 获取 SDK 版本
     */
    getVersion(): string;
    /**
     * 获取调试信息
     */
    getDebugInfo(): object;
    /**
     * 备份玩家数据
     * @param backupKey 备份数据键名，用于区分不同类型的数据
     * @param data 要备份的数据
     * @param options 备份选项
     * @returns 备份结果
     */
    backupPlayerData(backupKey: string, data: Record<string, unknown>, options?: PlayerDataBackupOptions): Promise<PlayerDataBackupResult>;
    /**
     * 检索特定备份数据
     * @param backupKey 备份数据键名
     * @returns 备份数据
     */
    retrievePlayerData(backupKey: string): Promise<PlayerDataRetrieveResult>;
    /**
     * 检索所有备份数据
     * @returns 所有备份数据列表
     */
    retrieveAllPlayerData(): Promise<PlayerDataRetrieveResult[]>;
    /**
     * 获取备份统计信息
     * @returns 备份统计信息
     */
    getPlayerDataStats(): Promise<PlayerDataStats>;
    /**
     * 发送玩家数据相关消息到顶层窗口
     */
    private sendPlayerDataMessage;
}

declare const sdk: GameSDK;
/**
 * 初始化 SDK
 *
 * @param config SDK 配置
 * @example
 * ```typescript
 * import { init, AdCloseType } from '@anyigame/ad-sdk';
 *
 * // 初始化 SDK
 * await init({
 *   appid: '1001',
 *   channel: '1',
 *   debug: true
 * });
 *
 * // 显示广告
 * await showAd((type) => {
 *   if (type === AdCloseType.COMPLETED) {
 *     console.log('用户观看完成，发放奖励');
 *   } else {
 *     console.log('用户取消观看');
 *   }
 * });
 * ```
 */
declare const init: (config: SDKConfig, adapter?: NativeBridgeAdapter) => Promise<void>;
/**
 * 显示广告
 *
 * @param callback 广告关闭时的回调函数
 * @example
 * ```typescript
 * await showAd((type) => {
 *   if (type === AdCloseType.COMPLETED) {
 *     // 用户观看完成，发放奖励
 *     grantReward();
 *   } else {
 *     // 用户取消观看
 *     console.log('用户取消观看广告');
 *   }
 * });
 * ```
 */
declare const showAd: (callback?: AdCloseCallback) => Promise<void>;
/**
 * 检查是否可以显示广告
 *
 * @returns boolean 是否可以显示广告
 * @example
 * ```typescript
 * if (canShowAd()) {
 *   await showAd();
 * } else {
 *   console.log('当前无法显示广告');
 * }
 * ```
 */
declare const canShowAd: () => boolean;
/**
 * 获取用户信息（同步）
 *
 * @returns 用户信息，如果未初始化或用户信息不存在则返回 null
 * @example
 * ```typescript
 * const userInfo = getUserInfo();
 * if (userInfo) {
 *   console.log('用户昵称:', userInfo.nickname);
 * }
 * ```
 */
declare const getUserInfo: () => User | null;
/**
 * 刷新用户信息
 *
 * @returns Promise<User> 最新的用户信息
 * @example
 * ```typescript
 * try {
 *   const userInfo = await refreshUserInfo();
 *   console.log('刷新后的用户信息:', userInfo);
 * } catch (error) {
 *   console.error('刷新用户信息失败:', error);
 * }
 * ```
 */
declare const refreshUserInfo: () => Promise<User>;
/**
 * 获取游戏配置
 *
 * @returns Promise<GameConfig> 游戏配置信息
 * @example
 * ```typescript
 * try {
 *   const gameConfig = await getGameConfig();
 *   console.log('游戏配置:', gameConfig);
 * } catch (error) {
 *   console.error('获取游戏配置失败:', error);
 * }
 * ```
 */
declare const getGameConfig: () => Promise<GameConfig>;
/**
 * 备份玩家数据
 *
 * @param backupKey 备份数据键名，用于区分不同类型的数据
 * @param data 要备份的数据
 * @param options 备份选项
 * @returns Promise<PlayerDataBackupResult> 备份结果
 * @example
 * ```typescript
 * import { backupPlayerData, BackupType } from '@anyigame/ad-sdk';
 *
 * try {
 *   const result = await backupPlayerData('inventory', {
 *     items: [{ id: 1, name: 'sword', count: 1 }],
 *     gold: 1000
 *   }, {
 *     backupType: BackupType.MANUAL,
 *     description: 'Player inventory backup'
 *   });
 *   console.log('备份成功:', result);
 * } catch (error) {
 *   console.error('备份失败:', error);
 * }
 * ```
 */
declare const backupPlayerData: (backupKey: string, data: Record<string, unknown>, options?: PlayerDataBackupOptions) => Promise<PlayerDataBackupResult>;
/**
 * 检索特定备份数据
 *
 * @param backupKey 备份数据键名
 * @returns Promise<PlayerDataRetrieveResult> 备份数据
 * @example
 * ```typescript
 * import { retrievePlayerData } from '@anyigame/ad-sdk';
 *
 * try {
 *   const data = await retrievePlayerData('inventory');
 *   console.log('检索到的数据:', data.backupData);
 * } catch (error) {
 *   console.error('检索失败:', error);
 * }
 * ```
 */
declare const retrievePlayerData: (backupKey: string) => Promise<PlayerDataRetrieveResult>;
/**
 * 检索所有备份数据
 *
 * @returns Promise<PlayerDataRetrieveResult[]> 所有备份数据列表
 * @example
 * ```typescript
 * import { retrieveAllPlayerData } from '@anyigame/ad-sdk';
 *
 * try {
 *   const allData = await retrieveAllPlayerData();
 *   allData.forEach(backup => {
 *     console.log(`备份键: ${backup.backupKey}, 数据:`, backup.backupData);
 *   });
 * } catch (error) {
 *   console.error('检索失败:', error);
 * }
 * ```
 */
declare const retrieveAllPlayerData: () => Promise<PlayerDataRetrieveResult[]>;
/**
 * 获取备份统计信息
 *
 * @returns Promise<PlayerDataStats> 备份统计信息
 * @example
 * ```typescript
 * import { getPlayerDataStats } from '@anyigame/ad-sdk';
 *
 * try {
 *   const stats = await getPlayerDataStats();
 *   console.log(`总备份数: ${stats.totalBackups}, 总大小: ${stats.totalDataSize} 字节`);
 * } catch (error) {
 *   console.error('获取统计信息失败:', error);
 * }
 * ```
 */
declare const getPlayerDataStats: () => Promise<PlayerDataStats>;
/**
 * 获取 SDK 状态
 *
 * @returns string SDK 当前状态
 */
declare const getState: () => string;
/**
 * 获取 SDK 版本信息
 *
 * @returns string SDK 版本号
 */
declare const getVersion: () => string;
/**
 * 获取调试信息
 *
 * @returns object 包含 SDK 各种状态信息的对象
 */
declare const getDebugInfo: () => object;
/**
 * 强制重置广告状态
 * 用于异常情况下的状态恢复
 *
 * @example
 * ```typescript
 * // 当广告状态异常时，强制重置
 * if (!canShowAd()) {
 *   resetState();
 *   console.log('广告状态已重置');
 * }
 * ```
 */
declare const resetState: () => void;
/**
 * 销毁 SDK
 * 清理所有资源和事件监听器
 *
 * @example
 * ```typescript
 * // 在页面卸载时销毁 SDK
 * window.addEventListener('beforeunload', () => {
 *   destroy();
 * });
 * ```
 */
declare const destroy: () => void;

/**
 * 在顶层窗口初始化 SDK（跨 iframe 模式）
 *
 * @param config SDK 配置
 * @param adapter 可选的自定义适配器
 * @example
 * ```typescript
 * // 在顶层窗口（父页面）中初始化 SDK
 * import { initInTopWindow } from '@anyigame/ad-sdk';
 *
 * await initInTopWindow({
 *   appid: '1001',
 *   channel: '1',
 *   debug: true
 * });
 * ```
 */
declare const initInTopWindow: typeof CrossIframeSDKManager.initInTopWindow;
/**
 * 检查顶层窗口是否已初始化 SDK
 *
 * @returns boolean 是否已初始化
 * @example
 * ```typescript
 * import { isSDKInitializedInTopWindow } from '@anyigame/ad-sdk';
 *
 * if (isSDKInitializedInTopWindow()) {
 *   console.log('SDK 已在顶层窗口初始化');
 * }
 * ```
 */
declare const isSDKInitializedInTopWindow: typeof CrossIframeSDKManager.isSDKInitializedInTopWindow;
/**
 * 清理跨 iframe SDK 资源
 *
 * @example
 * ```typescript
 * import { cleanupCrossIframeSDK } from '@anyigame/ad-sdk';
 *
 * // 在页面卸载时清理
 * window.addEventListener('beforeunload', () => {
 *   cleanupCrossIframeSDK();
 * });
 * ```
 */
declare const cleanupCrossIframeSDK: typeof CrossIframeSDKManager.cleanup;
/**
 * 创建 iframe SDK 代理实例
 *
 * @returns CrossIframeSDKProxy 代理实例
 * @example
 * ```typescript
 * import { createIframeSDKProxy, AdCloseType } from '@anyigame/ad-sdk';
 *
 * // 在 iframe 中创建代理
 * const sdkProxy = createIframeSDKProxy();
 *
 * if (sdkProxy.isAvailable()) {
 *   await sdkProxy.showAd((type) => {
 *     if (type === AdCloseType.COMPLETED) {
 *       console.log('用户观看完成');
 *     }
 *   });
 * }
 * ```
 */
declare function createIframeSDKProxy(): CrossIframeSDKProxy;

export { AdCloseType, BackupType, BaseNativeBridgeAdapter, CrossIframeSDKManager, CrossIframeSDKProxy, DsmAdapter, GameSDK, SDKConfigValidator, SDKError, backupPlayerData, canShowAd, cleanupCrossIframeSDK, createIframeSDKProxy, sdk as default, destroy, getDebugInfo, getGameConfig, getPlayerDataStats, getState, getUserInfo, getVersion, init, initInTopWindow, isSDKInitializedInTopWindow, refreshUserInfo, resetState, retrieveAllPlayerData, retrievePlayerData, showAd };
export type { AdCloseCallback, CachedUserInfo, GameConfig, GameConfigResponse, NativeBridgeAdapter, NativeEnvironment, PlayerBackupData, PlayerDataBackupOptions, PlayerDataBackupRequest, PlayerDataBackupResponse, PlayerDataBackupResult, PlayerDataRetrieveAllResponse, PlayerDataRetrieveResponse, PlayerDataRetrieveResult, PlayerDataStats, PlayerDataStatsResponse, SDKConfig, User, UserResponse };
