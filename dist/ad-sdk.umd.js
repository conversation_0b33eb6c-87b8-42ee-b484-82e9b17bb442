(function (global, factory) {
    typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
    typeof define === 'function' && define.amd ? define(['exports'], factory) :
    (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.AdSDK = {}));
})(this, (function (exports) { 'use strict';

    /**
     * SDK 核心类型定义
     */
    /**
     * 广告关闭类型
     */
    exports.AdCloseType = void 0;
    (function (AdCloseType) {
        /** 观看完成 */
        AdCloseType[AdCloseType["COMPLETED"] = 1] = "COMPLETED";
        /** 用户取消 */
        AdCloseType[AdCloseType["CANCELLED"] = 2] = "CANCELLED";
    })(exports.AdCloseType || (exports.AdCloseType = {}));
    /**
     * SDK 事件名称枚举
     */
    var SDKEventName;
    (function (SDKEventName) {
        /** 开始初始化 SDK */
        SDKEventName["SDK_INIT_START"] = "sdk_init_start";
        /** SDK 初始化成功 */
        SDKEventName["SDK_INIT_SUCCESS"] = "sdk_init_success";
        /** SDK 初始化失败 */
        SDKEventName["SDK_INIT_FAILED"] = "sdk_init_failed";
        /** 广告请求 */
        SDKEventName["AD_REQUEST"] = "ad_request";
        /** 广告展示成功 */
        SDKEventName["AD_IMPRESSION"] = "ad_impression";
        /** 广告展示失败 */
        SDKEventName["AD_IMPRESSION_FAILED"] = "ad_impression_failed";
        /** 广告关闭 */
        SDKEventName["AD_CLOSE"] = "ad_close";
        /** 广告奖励发放 */
        SDKEventName["AD_REWARD_GRANT"] = "ad_reward_grant";
    })(SDKEventName || (SDKEventName = {}));
    /**
     * SDK 错误类型
     */
    class SDKError extends Error {
        constructor(message, code, originalError) {
            super(message);
            this.code = code;
            this.originalError = originalError;
            this.name = 'SDKError';
        }
    }
    /**
     * SDK 配置验证工具
     */
    class SDKConfigValidator {
        /**
         * 验证应用标识符格式
         * @param appid 应用标识符
         * @returns 是否有效
         */
        static isValidAppId(appid) {
            return /^[0-9]+$/.test(appid);
        }
        /**
         * 验证渠道代码格式
         * @param channelCode 渠道代码
         * @returns 是否有效
         */
        static isValidChannelCode(channelCode) {
            return /^[0-9]+$/.test(channelCode);
        }
        /**
         * 验证 SDK 配置
         * @param config SDK 配置
         * @throws {SDKError} 当配置无效时抛出错误
         */
        static validateConfig(config) {
            if (!config.appid || !this.isValidAppId(config.appid)) {
                throw new SDKError('应用标识符必须为数字字符串格式，如 "1001"');
            }
            if (!config.channel || !this.isValidChannelCode(config.channel)) {
                throw new SDKError('渠道代码必须为数字字符串格式，如 "1"');
            }
            if (config.timeout !== undefined && (config.timeout <= 0 || config.timeout > 60000)) {
                throw new SDKError('超时时间必须在 1-60000 毫秒之间');
            }
            if (config.maxRetries !== undefined && (config.maxRetries < 0 || config.maxRetries > 10)) {
                throw new SDKError('重试次数必须在 0-10 之间');
            }
            if (config.batchSize !== undefined && (config.batchSize < 1 || config.batchSize > 10)) {
                throw new SDKError('批次大小必须在 1-10 之间');
            }
            if (config.reportInterval !== undefined && (config.reportInterval < 1000 || config.reportInterval > 60000)) {
                throw new SDKError('上报间隔必须在 1000-60000 毫秒之间');
            }
        }
        /**
         * 获取配置的默认值
         * @param config 用户配置
         * @returns 合并默认值后的完整配置
         */
        static getConfigWithDefaults(config) {
            return {
                appid: config.appid,
                channel: config.channel,
                debug: config.debug ?? false,
                timeout: config.timeout ?? 10000,
                maxRetries: config.maxRetries ?? 3,
                batchSize: config.batchSize ?? 10,
                reportInterval: config.reportInterval ?? 5000,
            };
        }
    }
    // ============================================================================
    // 玩家数据备份相关类型
    // ============================================================================
    /**
     * 备份类型枚举
     * 使用字符串枚举以符合用户偏好
     */
    exports.BackupType = void 0;
    (function (BackupType) {
        /** 手动备份 */
        BackupType["MANUAL"] = "manual";
        /** 自动备份 */
        BackupType["AUTO"] = "auto";
        /** 关键节点备份 */
        BackupType["CHECKPOINT"] = "checkpoint";
    })(exports.BackupType || (exports.BackupType = {}));
    /**
     * 备份类型到数字的映射（用于API调用）
     */
    const BackupTypeToNumber = {
        [exports.BackupType.MANUAL]: 1,
        [exports.BackupType.AUTO]: 2,
        [exports.BackupType.CHECKPOINT]: 3,
    };
    /**
     * 数字到备份类型的映射（用于API响应解析）
     */
    const NumberToBackupType = {
        1: exports.BackupType.MANUAL,
        2: exports.BackupType.AUTO,
        3: exports.BackupType.CHECKPOINT,
    };

    var I=async(n,r)=>{let e=typeof r=="function"?await r(n):r;if(e)return n.scheme==="bearer"?`Bearer ${e}`:n.scheme==="basic"?`Basic ${btoa(e)}`:e},O={bodySerializer:n=>JSON.stringify(n,(r,e)=>typeof e=="bigint"?e.toString():e)},B=n=>{switch(n){case "label":return ".";case "matrix":return ";";case "simple":return ",";default:return "&"}},N=n=>{switch(n){case "form":return ",";case "pipeDelimited":return "|";case "spaceDelimited":return "%20";default:return ","}},Q=n=>{switch(n){case "label":return ".";case "matrix":return ";";case "simple":return ",";default:return "&"}},S=({allowReserved:n,explode:r,name:e,style:a,value:i})=>{if(!r){let t=(n?i:i.map(l=>encodeURIComponent(l))).join(N(a));switch(a){case "label":return `.${t}`;case "matrix":return `;${e}=${t}`;case "simple":return t;default:return `${e}=${t}`}}let o=B(a),s=i.map(t=>a==="label"||a==="simple"?n?t:encodeURIComponent(t):m({allowReserved:n,name:e,value:t})).join(o);return a==="label"||a==="matrix"?o+s:s},m=({allowReserved:n,name:r,value:e})=>{if(e==null)return "";if(typeof e=="object")throw new Error("Deeply-nested arrays/objects aren\u2019t supported. Provide your own `querySerializer()` to handle these.");return `${r}=${n?e:encodeURIComponent(e)}`},q=({allowReserved:n,explode:r,name:e,style:a,value:i})=>{if(i instanceof Date)return `${e}=${i.toISOString()}`;if(a!=="deepObject"&&!r){let t=[];Object.entries(i).forEach(([f,u])=>{t=[...t,f,n?u:encodeURIComponent(u)];});let l=t.join(",");switch(a){case "form":return `${e}=${l}`;case "label":return `.${l}`;case "matrix":return `;${e}=${l}`;default:return l}}let o=Q(a),s=Object.entries(i).map(([t,l])=>m({allowReserved:n,name:a==="deepObject"?`${e}[${t}]`:t,value:l})).join(o);return a==="label"||a==="matrix"?o+s:s};var J=/\{[^{}]+\}/g,M=({path:n,url:r})=>{let e=r,a=r.match(J);if(a)for(let i of a){let o=false,s=i.substring(1,i.length-1),t="simple";s.endsWith("*")&&(o=true,s=s.substring(0,s.length-1)),s.startsWith(".")?(s=s.substring(1),t="label"):s.startsWith(";")&&(s=s.substring(1),t="matrix");let l=n[s];if(l==null)continue;if(Array.isArray(l)){e=e.replace(i,S({explode:o,name:s,style:t,value:l}));continue}if(typeof l=="object"){e=e.replace(i,q({explode:o,name:s,style:t,value:l}));continue}if(t==="matrix"){e=e.replace(i,`;${m({name:s,value:l})}`);continue}let f=encodeURIComponent(t==="label"?`.${l}`:l);e=e.replace(i,f);}return e},k=({allowReserved:n,array:r,object:e}={})=>i=>{let o=[];if(i&&typeof i=="object")for(let s in i){let t=i[s];if(t!=null)if(Array.isArray(t)){let l=S({allowReserved:n,explode:true,name:s,style:"form",value:t,...r});l&&o.push(l);}else if(typeof t=="object"){let l=q({allowReserved:n,explode:true,name:s,style:"deepObject",value:t,...e});l&&o.push(l);}else {let l=m({allowReserved:n,name:s,value:t});l&&o.push(l);}}return o.join("&")},E=n=>{if(!n)return "stream";let r=n.split(";")[0]?.trim();if(r){if(r.startsWith("application/json")||r.endsWith("+json"))return "json";if(r==="multipart/form-data")return "formData";if(["application/","audio/","image/","video/"].some(e=>r.startsWith(e)))return "blob";if(r.startsWith("text/"))return "text"}},$=async({security:n,...r})=>{for(let e of n){let a=await I(e,r.auth);if(!a)continue;let i=e.name??"Authorization";switch(e.in){case "query":r.query||(r.query={}),r.query[i]=a;break;case "cookie":r.headers.append("Cookie",`${i}=${a}`);break;case "header":default:r.headers.set(i,a);break}return}},C=n=>L({baseUrl:n.baseUrl,path:n.path,query:n.query,querySerializer:typeof n.querySerializer=="function"?n.querySerializer:k(n.querySerializer),url:n.url}),L=({baseUrl:n,path:r,query:e,querySerializer:a,url:i})=>{let o=i.startsWith("/")?i:`/${i}`,s=(n??"")+o;r&&(s=M({path:r,url:s}));let t=e?a(e):"";return t.startsWith("?")&&(t=t.substring(1)),t&&(s+=`?${t}`),s},x=(n,r)=>{let e={...n,...r};return e.baseUrl?.endsWith("/")&&(e.baseUrl=e.baseUrl.substring(0,e.baseUrl.length-1)),e.headers=j(n.headers,r.headers),e},j=(...n)=>{let r=new Headers;for(let e of n){if(!e||typeof e!="object")continue;let a=e instanceof Headers?e.entries():Object.entries(e);for(let[i,o]of a)if(o===null)r.delete(i);else if(Array.isArray(o))for(let s of o)r.append(i,s);else o!==void 0&&r.set(i,typeof o=="object"?JSON.stringify(o):o);}return r},g=class{_fns;constructor(){this._fns=[];}clear(){this._fns=[];}getInterceptorIndex(r){return typeof r=="number"?this._fns[r]?r:-1:this._fns.indexOf(r)}exists(r){let e=this.getInterceptorIndex(r);return !!this._fns[e]}eject(r){let e=this.getInterceptorIndex(r);this._fns[e]&&(this._fns[e]=null);}update(r,e){let a=this.getInterceptorIndex(r);return this._fns[a]?(this._fns[a]=e,r):false}use(r){return this._fns=[...this._fns,r],this._fns.length-1}},v=()=>({error:new g,request:new g,response:new g}),V=k({allowReserved:false,array:{explode:true,style:"form"},object:{explode:true,style:"deepObject"}}),F={"Content-Type":"application/json"},w=(n={})=>({...O,headers:F,parseAs:"auto",querySerializer:V,...n});var G=(n={})=>{let r=x(w(),n),e=()=>({...r}),a=s=>(r=x(r,s),e()),i=v(),o=async s=>{let t={...r,...s,fetch:s.fetch??r.fetch??globalThis.fetch,headers:j(r.headers,s.headers)};t.security&&await $({...t,security:t.security}),t.body&&t.bodySerializer&&(t.body=t.bodySerializer(t.body)),(t.body===void 0||t.body==="")&&t.headers.delete("Content-Type");let l=C(t),f={redirect:"follow",...t},u=new Request(l,f);for(let p of i.request._fns)p&&(u=await p(u,t));let d=t.fetch,c=await d(u);for(let p of i.response._fns)p&&(c=await p(c,u,t));let b={request:u,response:c};if(c.ok){if(c.status===204||c.headers.get("Content-Length")==="0")return t.responseStyle==="data"?{}:{data:{},...b};let p=(t.parseAs==="auto"?E(c.headers.get("Content-Type")):t.parseAs)??"json";if(p==="stream")return t.responseStyle==="data"?c.body:{data:c.body,...b};let h=await c[p]();return p==="json"&&(t.responseValidator&&await t.responseValidator(h),t.responseTransformer&&(h=await t.responseTransformer(h))),t.responseStyle==="data"?h:{data:h,...b}}let R=await c.text();try{R=JSON.parse(R);}catch{}let y=R;for(let p of i.error._fns)p&&(y=await p(R,c,u,t));if(y=y||{},t.throwOnError)throw y;return t.responseStyle==="data"?void 0:{error:y,...b}};return {buildUrl:C,connect:s=>o({...s,method:"CONNECT"}),delete:s=>o({...s,method:"DELETE"}),get:s=>o({...s,method:"GET"}),getConfig:e,head:s=>o({...s,method:"HEAD"}),interceptors:i,options:s=>o({...s,method:"OPTIONS"}),patch:s=>o({...s,method:"PATCH"}),post:s=>o({...s,method:"POST"}),put:s=>o({...s,method:"PUT"}),request:o,setConfig:a,trace:s=>o({...s,method:"TRACE"})}};

    // This file is auto-generated by @hey-api/openapi-ts
    const client = G(w());

    // This file is auto-generated by @hey-api/openapi-ts
    /**
     * 获取当前登录用户信息
     */
    const getCurrentUser = (options) => {
        return (options?.client ?? client).get({
            url: '/api/user/me',
            ...options
        });
    };
    /**
     * 获取游戏配置
     * 根据游戏代码和渠道代码获取游戏配置信息。此接口不需要用户登录。
     *
     * **验证逻辑:**
     * - 验证游戏代码和渠道代码为数字格式
     * - 检查渠道是否启用
     * - 验证游戏是否与指定渠道关联
     *
     */
    const getGameConfig$1 = (options) => {
        return (options.client ?? client).get({
            url: '/api/game/config',
            ...options
        });
    };
    /**
     * 上报激励视频广告事件
     * 批量上报激励视频广告事件数据。支持部分成功处理，即使部分事件处理失败，成功的事件仍会被处理。
     *
     * **请求限制:**
     * - 最大请求体大小: 1MB
     * - 单次请求最大事件数量: 10个
     * - 支持部分成功响应
     *
     * **验证逻辑:**
     * - 验证X-Channel-Code为数字格式
     * - 验证渠道代码有效性
     * - 验证事件数据格式和数量
     *
     */
    const reportAdEvent = (options) => {
        return (options.client ?? client).post({
            url: '/api/adevent/report',
            ...options,
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            }
        });
    };
    /**
     * 发送心跳信号
     * 发送心跳信号以维持会话活跃状态。需要用户登录验证。
     *
     * **功能说明:**
     * - 系统会使用当前用户的登录会话ID（session()->getId()）来查找对应的心跳会话
     * - 不需要在请求中传递session_id，会从当前登录会话中自动获取
     * - 支持传递自定义数据用于业务扩展
     * - 具有频率限制：10次/分钟
     *
     * **会话管理:**
     * - 心跳会话在用户登录时自动创建（事件驱动）
     * - 会话ID使用用户的登录会话ID，确保唯一性和一致性
     * - 系统会自动检测和标记超时会话
     * - 所有心跳数据永久保存，不会自动删除
     *
     */
    const sendHeartbeat = (options) => {
        return (options?.client ?? client).post({
            url: '/api/heartbeat/heartbeat',
            ...options,
            headers: {
                'Content-Type': 'application/json',
                ...options?.headers
            }
        });
    };
    /**
     * 备份玩家数据
     * 存储任意JSON格式的玩家数据。支持灵活的数据结构，用于客户端全量数据存储。
     *
     * **功能特性:**
     * - 支持任意JSON数据结构（最大1MB）
     * - 使用backup_key进行数据分类（如inventory、progress、settings等）
     * - 自动数据完整性校验（SHA-256）
     * - 支持软删除，同一backup_key的新备份会替换旧备份
     * - 需要用户登录验证
     *
     * **数据限制:**
     * - 最大数据大小: 1MB
     * - backup_key最大长度: 128字符
     * - description最大长度: 255字符
     * - 频率限制: 30次/分钟
     *
     */
    const backupPlayerData$1 = (options) => {
        return (options.client ?? client).post({
            url: '/api/player-data/backup',
            ...options,
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            }
        });
    };
    /**
     * 检索所有玩家备份数据
     * 获取当前用户在指定游戏中的所有备份数据。
     *
     * **功能特性:**
     * - 返回用户的所有活跃备份数据
     * - 自动验证数据完整性
     * - 损坏的备份会被自动标记并排除
     * - 按backup_key和创建时间排序
     * - 需要用户登录验证
     * - 频率限制: 60次/分钟
     *
     */
    const retrieveAllPlayerData$1 = (options) => {
        return (options.client ?? client).get({
            url: '/api/player-data/retrieve',
            ...options
        });
    };
    /**
     * 检索特定备份数据
     * 根据backup_key获取特定的备份数据。
     *
     * **功能特性:**
     * - 返回指定backup_key的最新备份数据
     * - 自动验证数据完整性
     * - 如果数据损坏会返回错误并标记备份状态
     * - 需要用户登录验证
     * - 频率限制: 60次/分钟
     *
     */
    const retrieveSpecificPlayerData = (options) => {
        return (options.client ?? client).get({
            url: '/api/player-data/retrieve/{backup_key}',
            ...options
        });
    };
    /**
     * 获取备份统计信息
     * 获取当前用户在指定游戏中的备份统计信息。
     *
     * **统计内容:**
     * - 总备份数量
     * - 总数据大小
     * - 按备份类型分组的统计
     * - 最新备份时间
     * - 需要用户登录验证
     * - 频率限制: 30次/分钟
     *
     */
    const getPlayerBackupStats = (options) => {
        return (options.client ?? client).get({
            url: '/api/player-data/stats',
            ...options
        });
    };

    /**
     * 原生桥接适配器接口
     *
     * 这个接口定义了 SDK 与原生应用交互的标准方法。
     * 不同的客户可以实现这个接口来适配他们的原生应用。
     */
    // ============================================================================
    // 抽象基类
    // ============================================================================
    /**
     * 抽象适配器基类
     * 提供一些通用的实现和工具方法
     */
    class BaseNativeBridgeAdapter {
        constructor() {
            // ------------------------------------------------------------------------
            // 属性定义
            // ------------------------------------------------------------------------
            this.debug = false;
        }
        // ------------------------------------------------------------------------
        // 公共方法
        // ------------------------------------------------------------------------
        /**
         * 设置调试模式
         */
        setDebugMode(debug) {
            this.debug = debug;
        }
        /**
         * 设置广告关闭回调
         */
        setAdCloseCallback(callback) {
            this.adCloseCallback = callback;
        }
        /**
         * 清理资源
         */
        cleanup() {
            this.adCloseCallback = undefined;
        }
        // ------------------------------------------------------------------------
        // 受保护的工具方法
        // ------------------------------------------------------------------------
        /**
         * 触发广告关闭回调
         */
        triggerAdCloseCallback(type) {
            if (this.debug) {
                console.log(`[NativeBridgeAdapter] 收到广告关闭回调: ${type} (类型: ${typeof type})`);
                console.log(`[NativeBridgeAdapter] 当前回调函数存在: ${!!this.adCloseCallback}`);
            }
            if (!this.adCloseCallback) {
                if (this.debug) {
                    console.warn(`[NativeBridgeAdapter] 广告关闭回调函数不存在，忽略回调`);
                }
                return;
            }
            // 将原生传入的数字或字符串类型转换为 AdCloseType 枚举类型
            let adCloseType;
            if (typeof type === 'string') {
                // 如果是字符串，转换为数字
                const numericType = parseInt(type, 10);
                if (isNaN(numericType)) {
                    if (this.debug) {
                        console.warn(`[NativeBridgeAdapter] 无效的广告关闭类型: ${type}`);
                    }
                    return;
                }
                adCloseType = numericType;
            }
            else {
                // 如果是数字，直接转换
                adCloseType = type;
            }
            // 验证类型是否有效
            if (adCloseType !== exports.AdCloseType.COMPLETED && adCloseType !== exports.AdCloseType.CANCELLED) {
                if (this.debug) {
                    console.warn(`[NativeBridgeAdapter] 未知的广告关闭类型: ${type} (转换后: ${adCloseType})`);
                }
                // 对于未知类型，默认当作取消处理
                adCloseType = exports.AdCloseType.CANCELLED;
            }
            try {
                this.adCloseCallback(adCloseType);
                if (this.debug) {
                    console.log(`[NativeBridgeAdapter] 广告关闭回调执行成功: ${adCloseType}`);
                }
            }
            catch (error) {
                if (this.debug) {
                    console.error(`[NativeBridgeAdapter] 广告关闭回调执行失败:`, error);
                }
            }
        }
        /**
         * 检查是否为 Android 环境
         */
        isAndroidEnvironment() {
            return !!window.DsmJSInterface?.showAd;
        }
        /**
         * 检查是否为 iOS 环境
         */
        isIOSEnvironment() {
            return !!window.webkit?.messageHandlers?.showAd;
        }
        /**
         * 检查是否有原生支持
         */
        hasNativeSupport() {
            return this.isAndroidEnvironment() || this.isIOSEnvironment();
        }
    }

    /**
     * DSM 客户端适配器实现
     *
     * 基于 H5交互文档.md 实现的具体适配器，用于与当前客户的原生应用交互。
     * 支持跨 iframe 的回调处理。
     */
    // ============================================================================
    // DSM 适配器实现
    // ============================================================================
    /**
     * DSM 适配器实现
     */
    class DsmAdapter extends BaseNativeBridgeAdapter {
        // ------------------------------------------------------------------------
        // 构造函数
        // ------------------------------------------------------------------------
        constructor() {
            super();
            this.setupGlobalCallbacks();
        }
        // ------------------------------------------------------------------------
        // 公共方法 (实现接口)
        // ------------------------------------------------------------------------
        /**
         * 检测原生环境
         */
        detectNativeEnvironment() {
            const isAndroid = this.isAndroidEnvironment();
            const isIOS = this.isIOSEnvironment();
            const hasNativeSupport = this.hasNativeSupport();
            return {
                isAndroid,
                isIOS,
                hasNativeSupport,
            };
        }
        /**
         * 显示广告
         */
        showAd(callback) {
            const env = this.detectNativeEnvironment();
            if (!env.hasNativeSupport) {
                throw new Error('未检测到原生环境，无法显示广告');
            }
            // 设置回调
            this.setAdCloseCallback(callback);
            if (this.debug) {
                console.log('[DsmAdapter] 开始调用原生广告显示方法');
                console.log('[DsmAdapter] 环境信息:', env);
            }
            try {
                if (env.isAndroid) {
                    // Android 环境
                    if (this.debug) {
                        console.log('[DsmAdapter] 调用 Android 原生方法: window.DsmJSInterface.showAd()');
                    }
                    window.DsmJSInterface?.showAd();
                }
                else if (env.isIOS) {
                    // iOS 环境
                    if (this.debug) {
                        console.log('[DsmAdapter] 调用 iOS 原生方法: webkit.messageHandlers.showAd.postMessage()');
                    }
                    window.webkit?.messageHandlers?.showAd?.postMessage({ body: '' });
                }
                if (this.debug) {
                    console.log('[DsmAdapter] 原生广告显示方法调用完成');
                }
            }
            catch (error) {
                if (this.debug) {
                    console.error('[DsmAdapter] 调用原生广告显示方法失败:', error);
                }
                throw new Error(`显示广告失败: ${error instanceof Error ? error.message : String(error)}`);
            }
        }
        /**
         * 清理资源
         */
        cleanup() {
            super.cleanup();
            // 清理全局回调
            const targetWindow = this.getTargetWindow();
            if (targetWindow.closeAd) {
                delete targetWindow.closeAd;
            }
            // 清理跨 iframe 回调引用
            try {
                if (window.top && window !== window.top) {
                    delete window.top[DsmAdapter.CROSS_IFRAME_KEY];
                }
                if (window[DsmAdapter.CROSS_IFRAME_KEY]) {
                    delete window[DsmAdapter.CROSS_IFRAME_KEY];
                }
            }
            catch (error) {
                // 跨域访问可能失败，忽略错误
                if (this.debug) {
                    console.warn('[DsmAdapter] 清理跨 iframe 回调引用失败:', error);
                }
            }
        }
        // ------------------------------------------------------------------------
        // 私有方法 (内部实现)
        // ------------------------------------------------------------------------
        /**
         * 设置全局回调函数
         * 支持跨 iframe 的回调处理
         */
        setupGlobalCallbacks() {
            // 获取目标窗口（优先使用顶层窗口）
            const targetWindow = this.getTargetWindow();
            // 设置广告关闭回调
            targetWindow.closeAd = (type) => {
                if (this.debug) {
                    console.log(`[DsmAdapter] window.closeAd 被调用: ${type}`);
                    console.log(`[DsmAdapter] 当前窗口是顶层窗口: ${window === window.top}`);
                    console.log(`[DsmAdapter] 当前 adCloseCallback 存在: ${!!this.adCloseCallback}`);
                }
                // 如果在顶层窗口但回调不存在，尝试查找跨 iframe 回调
                if (!this.adCloseCallback && window === window.top) {
                    this.handleCrossIframeCallback(type);
                    return;
                }
                // 确保回调函数存在
                if (!this.adCloseCallback) {
                    console.warn('[DsmAdapter] 广告关闭回调函数不存在，可能是状态管理问题');
                    return;
                }
                this.triggerAdCloseCallback(type);
            };
            // 如果当前不是顶层窗口，在顶层窗口也设置回调引用
            if (window !== window.top && window.top) {
                this.setupCrossIframeCallback();
            }
        }
        /**
         * 获取目标窗口（用于设置回调）
         */
        getTargetWindow() {
            // 优先在顶层窗口设置回调，这样原生调用更容易访问
            return window.top || window;
        }
        /**
         * 设置跨 iframe 回调引用
         */
        setupCrossIframeCallback() {
            try {
                if (window.top) {
                    // 在顶层窗口存储当前 iframe 的回调引用
                    window.top[DsmAdapter.CROSS_IFRAME_KEY] = {
                        callback: (type) => {
                            if (this.adCloseCallback) {
                                this.triggerAdCloseCallback(type);
                            }
                        },
                        debug: this.debug
                    };
                }
            }
            catch (error) {
                // 跨域访问可能失败，忽略错误
                if (this.debug) {
                    console.warn('[DsmAdapter] 无法设置跨 iframe 回调引用:', error);
                }
            }
        }
        /**
         * 处理跨 iframe 回调
         */
        handleCrossIframeCallback(type) {
            try {
                const crossIframeCallback = window[DsmAdapter.CROSS_IFRAME_KEY];
                if (crossIframeCallback && crossIframeCallback.callback) {
                    if (this.debug) {
                        console.log('[DsmAdapter] 使用跨 iframe 回调处理广告关闭');
                    }
                    crossIframeCallback.callback(type);
                }
                else {
                    if (this.debug) {
                        console.warn('[DsmAdapter] 未找到跨 iframe 回调');
                    }
                }
            }
            catch (error) {
                if (this.debug) {
                    console.error('[DsmAdapter] 跨 iframe 回调处理失败:', error);
                }
            }
        }
    }
    // ------------------------------------------------------------------------
    // 静态常量
    // ------------------------------------------------------------------------
    DsmAdapter.CROSS_IFRAME_KEY = '__ANYIGAME_AD_SDK_CALLBACK__';

    /**
     * 本地存储工具类
     */
    /**
     * 存储键名常量
     */
    const STORAGE_KEYS = {
        USER_INFO: 'ad_sdk_user_info',
        SESSION_ID: 'ad_sdk_session_id',
    };
    /**
     * 本地存储工具类
     */
    class StorageUtil {
        /**
         * 保存用户信息到本地存储
         */
        static saveUserInfo(userInfo) {
            try {
                const data = JSON.stringify(userInfo);
                localStorage.setItem(STORAGE_KEYS.USER_INFO, data);
            }
            catch (error) {
                console.warn('Failed to save user info to localStorage:', error);
            }
        }
        /**
         * 从本地存储获取用户信息
         */
        static getUserInfo() {
            try {
                const data = localStorage.getItem(STORAGE_KEYS.USER_INFO);
                if (!data) {
                    return null;
                }
                const userInfo = JSON.parse(data);
                // 检查是否过期
                const now = Date.now();
                if (now > userInfo.cachedAt + userInfo.expiresIn) {
                    // 已过期，删除缓存
                    this.removeUserInfo();
                    return null;
                }
                return userInfo;
            }
            catch (error) {
                console.warn('Failed to get user info from localStorage:', error);
                return null;
            }
        }
        /**
         * 删除用户信息缓存
         */
        static removeUserInfo() {
            try {
                localStorage.removeItem(STORAGE_KEYS.USER_INFO);
            }
            catch (error) {
                console.warn('Failed to remove user info from localStorage:', error);
            }
        }
        /**
         * 保存会话 ID
         */
        static saveSessionId(sessionId) {
            try {
                localStorage.setItem(STORAGE_KEYS.SESSION_ID, sessionId);
            }
            catch (error) {
                console.warn('Failed to save session ID to localStorage:', error);
            }
        }
        /**
         * 获取会话 ID
         */
        static getSessionId() {
            try {
                return localStorage.getItem(STORAGE_KEYS.SESSION_ID);
            }
            catch (error) {
                console.warn('Failed to get session ID from localStorage:', error);
                return null;
            }
        }
        /**
         * 删除会话 ID
         */
        static removeSessionId() {
            try {
                localStorage.removeItem(STORAGE_KEYS.SESSION_ID);
            }
            catch (error) {
                console.warn('Failed to remove session ID from localStorage:', error);
            }
        }
        /**
         * 清除所有 SDK 相关的存储数据
         */
        static clearAll() {
            this.removeUserInfo();
            this.removeSessionId();
        }
    }

    /**
     * 用户模块
     *
     * 负责用户信息的获取、缓存和管理
     */
    // ============================================================================
    // 用户模块实现
    // ============================================================================
    /**
     * 用户模块类
     */
    class UserModule {
        // ------------------------------------------------------------------------
        // 构造函数
        // ------------------------------------------------------------------------
        constructor(config) {
            this.cachedUserInfo = null;
            this.config = config;
        }
        // ------------------------------------------------------------------------
        // 公共方法
        // ------------------------------------------------------------------------
        /**
         * 初始化用户模块
         * 异步获取用户信息并缓存
         */
        async initialize() {
            try {
                // 先尝试从缓存获取
                const cached = StorageUtil.getUserInfo();
                if (cached) {
                    this.cachedUserInfo = cached;
                    if (this.config.debug) {
                        console.log('[UserModule] 从缓存加载用户信息:', cached);
                    }
                    return;
                }
                // 缓存不存在或已过期，从 API 获取
                const userInfo = await this.fetchUserInfoFromAPI();
                // 创建缓存对象
                const cachedUserInfo = {
                    ...userInfo,
                    cachedAt: Date.now(),
                    expiresIn: 24 * 60 * 60 * 1000, // 24小时过期
                };
                // 保存到内存和本地存储
                this.cachedUserInfo = cachedUserInfo;
                StorageUtil.saveUserInfo(cachedUserInfo);
                if (this.config.debug) {
                    console.log('[UserModule] 从 API 获取并缓存用户信息:', userInfo);
                }
            }
            catch (error) {
                const errorMessage = `用户模块初始化失败: ${error instanceof Error ? error.message : String(error)}`;
                if (this.config.debug) {
                    console.error('[UserModule]', errorMessage, error);
                }
                throw new Error(errorMessage);
            }
        }
        /**
         * 同步获取用户信息
         * 从缓存中返回用户信息，如果缓存不存在则返回 null
         */
        getUserInfo() {
            if (!this.cachedUserInfo) {
                return null;
            }
            // 检查缓存是否过期
            const now = Date.now();
            if (now > this.cachedUserInfo.cachedAt + this.cachedUserInfo.expiresIn) {
                // 缓存已过期
                this.clearCache();
                return null;
            }
            // 返回用户信息（不包含缓存相关字段）
            const { cachedAt, expiresIn, ...userInfo } = this.cachedUserInfo;
            return userInfo;
        }
        /**
         * 强制刷新用户信息
         * 清除缓存并重新从原生获取
         */
        async refreshUserInfo() {
            try {
                this.clearCache();
                const userInfo = await this.fetchUserInfoFromAPI();
                // 创建新的缓存对象
                const cachedUserInfo = {
                    ...userInfo,
                    cachedAt: Date.now(),
                    expiresIn: 24 * 60 * 60 * 1000, // 24小时过期
                };
                // 保存到内存和本地存储
                this.cachedUserInfo = cachedUserInfo;
                StorageUtil.saveUserInfo(cachedUserInfo);
                if (this.config.debug) {
                    console.log('[UserModule] 刷新用户信息成功:', userInfo);
                }
                return userInfo;
            }
            catch (error) {
                const errorMessage = `刷新用户信息失败: ${error instanceof Error ? error.message : String(error)}`;
                if (this.config.debug) {
                    console.error('[UserModule]', errorMessage, error);
                }
                throw new Error(errorMessage);
            }
        }
        /**
         * 清除用户信息缓存
         */
        clearCache() {
            this.cachedUserInfo = null;
            StorageUtil.removeUserInfo();
            if (this.config.debug) {
                console.log('[UserModule] 清除用户信息缓存');
            }
        }
        /**
         * 检查用户信息是否已缓存且有效
         */
        isUserInfoCached() {
            if (!this.cachedUserInfo) {
                return false;
            }
            const now = Date.now();
            return now <= this.cachedUserInfo.cachedAt + this.cachedUserInfo.expiresIn;
        }
        /**
         * 获取用户 ID
         */
        getUserId() {
            const userInfo = this.getUserInfo();
            return userInfo?.id ?? null;
        }
        /**
         * 清理模块资源
         */
        cleanup() {
            this.cachedUserInfo = null;
        }
        // ------------------------------------------------------------------------
        // 私有方法
        // ------------------------------------------------------------------------
        /**
         * 从 API 获取用户信息
         */
        async fetchUserInfoFromAPI() {
            try {
                if (this.config.debug) {
                    console.log('[UserModule] 开始获取用户信息...');
                }
                const response = await getCurrentUser();
                if (this.config.debug) {
                    console.log('[UserModule] API 响应:', response);
                    console.log('[UserModule] response.data:', response.data);
                    console.log('[UserModule] response.data?.data:', response.data?.data);
                }
                // 根据 API 规范，响应格式应该是 { data: { code, msg, data: User } }
                if (response.data?.data) {
                    return response.data.data;
                }
                else {
                    throw new Error(`API 返回的用户信息格式不正确: ${JSON.stringify(response)}`);
                }
            }
            catch (error) {
                if (this.config.debug) {
                    console.error('[UserModule] API 调用失败:', error);
                }
                const errorMessage = `获取用户信息失败: ${error instanceof Error ? error.message : String(error)}`;
                throw new Error(errorMessage);
            }
        }
    }

    /**
     * 广告模块
     *
     * 负责广告的展示和相关事件的处理
     */
    // ============================================================================
    // 类型定义
    // ============================================================================
    /**
     * 广告展示状态
     */
    var AdState;
    (function (AdState) {
        AdState["IDLE"] = "idle";
        AdState["REQUESTING"] = "requesting";
        AdState["SHOWING"] = "showing";
    })(AdState || (AdState = {}));
    // ============================================================================
    // 广告模块实现
    // ============================================================================
    /**
     * 广告模块类
     */
    class AdModule {
        // ------------------------------------------------------------------------
        // 构造函数
        // ------------------------------------------------------------------------
        constructor(adapter, config, eventModule) {
            this.currentState = AdState.IDLE;
            this.adapter = adapter;
            this.config = config;
            this.eventModule = eventModule;
        }
        // ------------------------------------------------------------------------
        // 公共方法
        // ------------------------------------------------------------------------
        /**
         * 显示广告
         * @param callback 广告关闭时的回调函数
         * @throws {Error} 当显示广告失败时抛出错误
         */
        async showAd(callback) {
            if (this.currentState !== AdState.IDLE) {
                const error = new Error(`广告正在${this.currentState === AdState.REQUESTING ? '请求' : '展示'}中，请稍后再试`);
                if (this.config.debug) {
                    console.error('[AdModule]', error.message);
                }
                throw error;
            }
            try {
                this.currentState = AdState.REQUESTING;
                this.currentCallback = callback;
                // 上报广告请求事件
                this.eventModule.reportEvent(SDKEventName.AD_REQUEST, {
                    custom_data: {
                        request_time: Date.now(),
                    },
                });
                if (this.config.debug) {
                    console.log('[AdModule] 开始请求广告');
                }
                // 调用适配器显示广告
                this.adapter.showAd(this.handleAdClose.bind(this));
                this.currentState = AdState.SHOWING;
                // 上报广告展示成功事件
                this.eventModule.reportEvent(SDKEventName.AD_IMPRESSION, {
                    custom_data: {
                        impression_time: Date.now(),
                    },
                });
                if (this.config.debug) {
                    console.log('[AdModule] 广告展示成功');
                }
            }
            catch (error) {
                this.currentState = AdState.IDLE;
                this.currentCallback = undefined;
                // 上报广告展示失败事件
                this.eventModule.reportEvent(SDKEventName.AD_IMPRESSION_FAILED, {
                    error_message: error instanceof Error ? error.message : String(error),
                    custom_data: {
                        error_time: Date.now(),
                    },
                });
                const errorMessage = `显示广告失败: ${error instanceof Error ? error.message : String(error)}`;
                if (this.config.debug) {
                    console.error('[AdModule]', errorMessage, error);
                }
                throw new Error(errorMessage);
            }
        }
        /**
         * 获取当前广告状态
         */
        getAdState() {
            return this.currentState;
        }
        /**
         * 检查是否可以显示广告
         */
        canShowAd() {
            return this.currentState === AdState.IDLE;
        }
        /**
         * 强制重置广告状态
         * 用于异常情况下的状态恢复
         */
        resetState() {
            if (this.config.debug) {
                console.log('[AdModule] 强制重置广告状态');
            }
            this.currentState = AdState.IDLE;
            this.currentCallback = undefined;
        }
        /**
         * 清理模块资源
         */
        cleanup() {
            this.resetState();
        }
        // ------------------------------------------------------------------------
        // 私有方法
        // ------------------------------------------------------------------------
        /**
         * 处理广告关闭
         */
        handleAdClose(type) {
            if (this.config.debug) {
                console.log(`[AdModule] 收到广告关闭回调: ${type}, 当前状态: ${this.currentState}`);
            }
            // 更宽松的状态检查 - 允许在 REQUESTING 或 SHOWING 状态下处理关闭
            if (this.currentState !== AdState.SHOWING && this.currentState !== AdState.REQUESTING) {
                if (this.config.debug) {
                    console.warn('[AdModule] 收到广告关闭回调，但当前状态不是展示中:', this.currentState);
                    console.warn('[AdModule] 强制重置状态以恢复功能');
                }
                // 强制重置状态而不是直接返回
                this.resetState();
                return;
            }
            try {
                if (this.config.debug) {
                    console.log('[AdModule] 开始处理广告关闭逻辑');
                }
                // 上报广告关闭事件
                this.eventModule.reportEvent(SDKEventName.AD_CLOSE, {
                    custom_data: {
                        close_type: exports.AdCloseType[type],
                        close_time: Date.now(),
                        is_completed: type === exports.AdCloseType.COMPLETED,
                    },
                });
                // 如果是观看完成，上报奖励发放事件
                if (type === exports.AdCloseType.COMPLETED) {
                    this.eventModule.reportEvent(SDKEventName.AD_REWARD_GRANT, {
                        reward_name: '广告奖励',
                        reward_amount: 1,
                        custom_data: {
                            reward_time: Date.now(),
                        },
                    });
                }
                if (this.config.debug) {
                    console.log('[AdModule] 广告关闭:', {
                        type,
                        isCompleted: type === exports.AdCloseType.COMPLETED,
                    });
                }
                // 调用用户回调
                if (this.currentCallback) {
                    try {
                        if (this.config.debug) {
                            console.log('[AdModule] 调用用户回调函数');
                        }
                        this.currentCallback(type);
                        if (this.config.debug) {
                            console.log('[AdModule] 用户回调函数执行完成');
                        }
                    }
                    catch (error) {
                        if (this.config.debug) {
                            console.error('[AdModule] 用户回调执行失败:', error);
                        }
                    }
                }
                else {
                    if (this.config.debug) {
                        console.warn('[AdModule] 用户回调函数不存在');
                    }
                }
            }
            catch (error) {
                if (this.config.debug) {
                    console.error('[AdModule] 处理广告关闭失败:', error);
                }
            }
            finally {
                // 重置状态 - 这是关键步骤，确保状态被正确重置
                if (this.config.debug) {
                    console.log('[AdModule] 重置广告状态到 IDLE');
                }
                this.resetState();
                if (this.config.debug) {
                    console.log(`[AdModule] 状态重置完成，当前状态: ${this.currentState}, 可以显示广告: ${this.canShowAd()}`);
                }
            }
        }
    }

    /**
     * UUID 生成工具
     */
    /**
     * 生成 UUID v4
     * @returns UUID 字符串
     */
    function generateUUID() {
        // 使用 crypto.randomUUID() 如果可用
        if (typeof crypto !== 'undefined' && crypto.randomUUID) {
            return crypto.randomUUID();
        }
        // 回退到自定义实现
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
            const r = (Math.random() * 16) | 0;
            const v = c === 'x' ? r : (r & 0x3) | 0x8;
            return v.toString(16);
        });
    }
    /**
     * 生成客户端事件 ID
     * @returns 客户端事件 ID
     */
    function generateClientEventId() {
        return `ceid-${generateUUID()}`;
    }
    /**
     * 生成会话 ID
     * @returns 会话 ID
     */
    function generateSessionId() {
        return `sess-${generateUUID()}`;
    }

    /**
     * 事件上报模块
     *
     * 负责收集和上报各种 SDK 事件
     */
    // ============================================================================
    // 事件模块实现
    // ============================================================================
    /**
     * 事件模块类
     */
    class EventModule {
        // ------------------------------------------------------------------------
        // 构造函数
        // ------------------------------------------------------------------------
        constructor(config, getUserId) {
            this.eventQueue = [];
            this.isReporting = false;
            // 配置常量
            this.MAX_QUEUE_SIZE = 50;
            this.config = config;
            this.getUserId = getUserId;
            this.sessionId = this.initializeSessionId();
            // 从配置中获取参数，如果配置中没有则使用默认值
            this.MAX_RETRY_COUNT = config.maxRetries ?? 3;
            this.BATCH_SIZE = Math.min(config.batchSize ?? 10, 10); // 确保不超过 API 限制
            this.REPORT_INTERVAL = config.reportInterval ?? 5000;
            this.startPeriodicReporting();
        }
        // ------------------------------------------------------------------------
        // 公共方法
        // ------------------------------------------------------------------------
        /**
         * 上报事件
         */
        reportEvent(eventName, eventData = {}) {
            const userId = this.getUserId();
            if (!userId) {
                if (this.config.debug) {
                    console.warn('[EventModule] 用户 ID 不存在，跳过事件上报:', eventName);
                }
                return;
            }
            const event = {
                event_name: eventName,
                event_time: Date.now(),
                user_id: userId,
                game_id: parseInt(this.config.appid, 10),
                channel_id: parseInt(this.config.channel, 10),
                client_event_id: generateClientEventId(),
                session_id: this.sessionId,
                ...eventData,
            };
            this.addToQueue(event);
            if (this.config.debug) {
                console.log('[EventModule] 事件已加入队列:', event);
            }
        }
        /**
         * 立即上报所有队列中的事件
         */
        async flushEvents() {
            if (this.isReporting || this.eventQueue.length === 0) {
                return;
            }
            this.isReporting = true;
            try {
                // 取出要上报的事件
                const eventsToReport = this.eventQueue.splice(0, this.BATCH_SIZE);
                const events = eventsToReport.map(item => item.event);
                if (this.config.debug) {
                    console.log('[EventModule] 开始上报事件:', events);
                }
                // 调用 API 上报事件
                const response = await reportAdEvent({
                    body: events,
                    headers: {
                        'X-Channel-Code': this.config.channel,
                    },
                });
                if (this.config.debug) {
                    console.log('[EventModule] 事件上报成功:', response);
                }
                // 检查是否有失败的事件需要重试
                if (response.data?.data?.failures && response.data.data.failures.length > 0) {
                    this.handleFailedEvents(eventsToReport, response.data.data.failures);
                }
            }
            catch (error) {
                if (this.config.debug) {
                    console.error('[EventModule] 事件上报失败:', error);
                }
                // 将失败的事件重新加入队列进行重试
                const failedEvents = this.eventQueue.splice(0, this.BATCH_SIZE);
                this.handleRetryEvents(failedEvents);
            }
            finally {
                this.isReporting = false;
            }
        }
        /**
         * 获取队列状态
         */
        getQueueStatus() {
            return {
                queueSize: this.eventQueue.length,
                isReporting: this.isReporting,
            };
        }
        /**
         * 清理模块资源
         */
        cleanup() {
            this.stopPeriodicReporting();
            // 尝试上报剩余事件
            if (this.eventQueue.length > 0) {
                this.flushEvents().catch(error => {
                    if (this.config.debug) {
                        console.error('[EventModule] 清理时上报事件失败:', error);
                    }
                });
            }
            this.eventQueue = [];
        }
        // ------------------------------------------------------------------------
        // 私有方法
        // ------------------------------------------------------------------------
        /**
         * 初始化会话 ID
         */
        initializeSessionId() {
            let sessionId = StorageUtil.getSessionId();
            if (!sessionId) {
                sessionId = generateSessionId();
                StorageUtil.saveSessionId(sessionId);
            }
            return sessionId;
        }
        /**
         * 添加事件到队列
         */
        addToQueue(event) {
            // 检查队列大小
            if (this.eventQueue.length >= this.MAX_QUEUE_SIZE) {
                // 移除最旧的事件
                this.eventQueue.shift();
                if (this.config.debug) {
                    console.warn('[EventModule] 事件队列已满，移除最旧的事件');
                }
            }
            this.eventQueue.push({
                event,
                retryCount: 0,
            });
            // 如果队列达到批次大小，立即上报
            if (this.eventQueue.length >= this.BATCH_SIZE) {
                this.flushEvents();
            }
        }
        /**
         * 处理失败的事件
         */
        handleFailedEvents(originalEvents, failures) {
            failures.forEach(failure => {
                if (failure.index !== undefined && failure.index < originalEvents.length) {
                    const failedEvent = originalEvents[failure.index];
                    if (failedEvent && failedEvent.retryCount < this.MAX_RETRY_COUNT) {
                        failedEvent.retryCount++;
                        this.eventQueue.unshift(failedEvent); // 重新加入队列头部
                        if (this.config.debug) {
                            console.warn('[EventModule] 事件上报失败，加入重试队列:', {
                                event: failedEvent.event.event_name,
                                reason: failure.reason,
                                retryCount: failedEvent.retryCount,
                            });
                        }
                    }
                    else if (failedEvent) {
                        if (this.config.debug) {
                            console.error('[EventModule] 事件重试次数已达上限，丢弃事件:', {
                                event: failedEvent.event.event_name,
                                reason: failure.reason,
                            });
                        }
                    }
                }
            });
        }
        /**
         * 处理需要重试的事件
         */
        handleRetryEvents(events) {
            events.forEach(eventItem => {
                if (eventItem.retryCount < this.MAX_RETRY_COUNT) {
                    eventItem.retryCount++;
                    this.eventQueue.unshift(eventItem); // 重新加入队列头部
                }
                else {
                    if (this.config.debug) {
                        console.error('[EventModule] 事件重试次数已达上限，丢弃事件:', eventItem.event.event_name);
                    }
                }
            });
        }
        /**
         * 开始定期上报
         */
        startPeriodicReporting() {
            this.reportTimer = window.setInterval(() => {
                if (this.eventQueue.length > 0) {
                    this.flushEvents();
                }
            }, this.REPORT_INTERVAL);
        }
        /**
         * 停止定期上报
         */
        stopPeriodicReporting() {
            if (this.reportTimer) {
                window.clearInterval(this.reportTimer);
                this.reportTimer = undefined;
            }
        }
    }

    /**
     * 心跳上报模块
     *
     * 负责定期发送心跳信号以维持会话活跃状态
     */
    // ============================================================================
    // 类型定义
    // ============================================================================
    /**
     * 心跳模块状态
     */
    var HeartbeatState;
    (function (HeartbeatState) {
        HeartbeatState["STOPPED"] = "stopped";
        HeartbeatState["RUNNING"] = "running";
        HeartbeatState["ERROR"] = "error";
    })(HeartbeatState || (HeartbeatState = {}));
    // ============================================================================
    // 心跳模块实现
    // ============================================================================
    /**
     * 心跳模块类
     */
    class HeartbeatModule {
        // ------------------------------------------------------------------------
        // 构造函数
        // ------------------------------------------------------------------------
        constructor(config) {
            this.state = HeartbeatState.STOPPED;
            this.heartbeatCount = 0;
            // 配置常量
            this.HEARTBEAT_INTERVAL = 30000; // 30 seconds
            this.config = config;
        }
        // ------------------------------------------------------------------------
        // 公共方法
        // ------------------------------------------------------------------------
        /**
         * 启动心跳上报
         */
        start() {
            if (this.state === HeartbeatState.RUNNING) {
                if (this.config.debug) {
                    console.warn('[HeartbeatModule] 心跳已在运行中，跳过重复启动');
                }
                return;
            }
            this.state = HeartbeatState.RUNNING;
            this.startTime = Date.now();
            this.heartbeatCount = 0;
            if (this.config.debug) {
                console.log('[HeartbeatModule] 启动心跳上报，间隔:', this.HEARTBEAT_INTERVAL, 'ms');
            }
            // 立即发送第一次心跳
            this.sendHeartbeatSafely();
            // 设置定时器
            this.heartbeatTimer = window.setInterval(() => {
                this.sendHeartbeatSafely();
            }, this.HEARTBEAT_INTERVAL);
        }
        /**
         * 停止心跳上报
         */
        stop() {
            if (this.state === HeartbeatState.STOPPED) {
                return;
            }
            if (this.config.debug) {
                console.log('[HeartbeatModule] 停止心跳上报');
            }
            this.state = HeartbeatState.STOPPED;
            if (this.heartbeatTimer) {
                window.clearInterval(this.heartbeatTimer);
                this.heartbeatTimer = undefined;
            }
        }
        /**
         * 获取心跳状态
         */
        getStatus() {
            const uptime = this.startTime ? Date.now() - this.startTime : 0;
            return {
                state: this.state,
                heartbeatCount: this.heartbeatCount,
                uptime,
            };
        }
        /**
         * 清理模块资源
         */
        cleanup() {
            this.stop();
            this.heartbeatCount = 0;
            this.startTime = undefined;
            if (this.config.debug) {
                console.log('[HeartbeatModule] 心跳模块已清理');
            }
        }
        // ------------------------------------------------------------------------
        // 私有方法
        // ------------------------------------------------------------------------
        /**
         * 安全地发送心跳（带错误处理）
         */
        async sendHeartbeatSafely() {
            try {
                await this.sendHeartbeat();
            }
            catch (error) {
                // 静默处理错误，不影响其他模块
                if (this.config.debug) {
                    console.error('[HeartbeatModule] 心跳发送失败:', error);
                }
                // 如果连续失败，可以考虑设置错误状态
                this.state = HeartbeatState.ERROR;
                // 但不停止心跳，继续尝试
                setTimeout(() => {
                    if (this.state === HeartbeatState.ERROR) {
                        this.state = HeartbeatState.RUNNING;
                    }
                }, this.HEARTBEAT_INTERVAL);
            }
        }
        /**
         * 发送心跳请求
         */
        async sendHeartbeat() {
            this.heartbeatCount++;
            const customData = {
                heartbeat_count: this.heartbeatCount,
                uptime_seconds: this.startTime ? Math.floor((Date.now() - this.startTime) / 1000) : 0,
                sdk_version: '1.0.4',
                timestamp: Date.now(),
            };
            if (this.config.debug) {
                console.log('[HeartbeatModule] 发送心跳:', customData);
            }
            const response = await sendHeartbeat({
                body: {
                    custom_data: customData,
                },
            });
            if (this.config.debug) {
                console.log('[HeartbeatModule] 心跳响应:', response);
            }
        }
    }

    /**
     * 玩家数据备份模块
     *
     * 负责玩家游戏数据的备份和恢复功能
     */
    // ============================================================================
    // 类型定义
    // ============================================================================
    /**
     * 玩家数据模块状态
     */
    var PlayerDataModuleState;
    (function (PlayerDataModuleState) {
        PlayerDataModuleState["IDLE"] = "idle";
        PlayerDataModuleState["BACKING_UP"] = "backing_up";
        PlayerDataModuleState["RETRIEVING"] = "retrieving";
        PlayerDataModuleState["ERROR"] = "error";
    })(PlayerDataModuleState || (PlayerDataModuleState = {}));
    // ============================================================================
    // 玩家数据模块实现
    // ============================================================================
    /**
     * 玩家数据备份模块类
     */
    class PlayerDataModule {
        // ------------------------------------------------------------------------
        // 构造函数
        // ------------------------------------------------------------------------
        constructor(config, getUserId) {
            this.state = PlayerDataModuleState.IDLE;
            this.config = config;
            this.getUserId = getUserId;
        }
        // ------------------------------------------------------------------------
        // 公共方法
        // ------------------------------------------------------------------------
        /**
         * 备份玩家数据
         * @param backupKey 备份数据键名，用于区分不同类型的数据
         * @param data 要备份的数据
         * @param options 备份选项
         * @returns 备份结果
         */
        async backupData(backupKey, data, options = {}) {
            if (this.state !== PlayerDataModuleState.IDLE) {
                throw new Error(`玩家数据模块正在${this.state === PlayerDataModuleState.BACKING_UP ? '备份' : '检索'}中，请稍后再试`);
            }
            const userId = this.getUserId();
            if (!userId) {
                throw new Error('用户未登录，无法备份数据');
            }
            try {
                this.state = PlayerDataModuleState.BACKING_UP;
                if (this.config.debug) {
                    console.log('[PlayerDataModule] 开始备份数据:', { backupKey, dataSize: JSON.stringify(data).length });
                }
                // 准备备份请求数据
                const backupType = options.backupType || exports.BackupType.MANUAL;
                const requestData = {
                    game_id: parseInt(this.config.appid, 10),
                    backup_key: backupKey,
                    backup_data: data,
                    backup_type: BackupTypeToNumber[backupType],
                    device_info: options.deviceInfo || {
                        platform: 'web',
                        user_agent: navigator.userAgent,
                        timestamp: Date.now(),
                    },
                };
                // 只有在有描述时才添加描述字段
                if (options.description) {
                    requestData.description = options.description;
                }
                // 调用 API 备份数据
                const response = await backupPlayerData$1({
                    body: requestData,
                });
                if (response.data?.code !== 0) {
                    throw new Error(response.data?.msg || '备份数据失败');
                }
                const apiData = response.data.data;
                if (!apiData) {
                    throw new Error('备份响应数据格式错误');
                }
                // 转换响应数据为标准格式
                const result = {
                    backupId: apiData.backup_id || 0,
                    backupKey: apiData.backup_key || backupKey,
                    dataSize: apiData.data_size || 0,
                    backupType: NumberToBackupType[apiData.backup_type || 1] || exports.BackupType.MANUAL,
                    backupTypeDescription: apiData.backup_type_description || '',
                    createdAt: apiData.created_at || '',
                    checksum: apiData.checksum || '',
                };
                if (this.config.debug) {
                    console.log('[PlayerDataModule] 数据备份成功:', result);
                }
                return result;
            }
            catch (error) {
                this.state = PlayerDataModuleState.ERROR;
                const errorMessage = `备份数据失败: ${error instanceof Error ? error.message : String(error)}`;
                if (this.config.debug) {
                    console.error('[PlayerDataModule]', errorMessage, error);
                }
                throw new Error(errorMessage);
            }
            finally {
                if (this.state !== PlayerDataModuleState.ERROR) {
                    this.state = PlayerDataModuleState.IDLE;
                }
            }
        }
        /**
         * 检索特定备份数据
         * @param backupKey 备份数据键名
         * @returns 备份数据
         */
        async retrieveData(backupKey) {
            if (this.state !== PlayerDataModuleState.IDLE) {
                throw new Error(`玩家数据模块正在${this.state === PlayerDataModuleState.BACKING_UP ? '备份' : '检索'}中，请稍后再试`);
            }
            const userId = this.getUserId();
            if (!userId) {
                throw new Error('用户未登录，无法检索数据');
            }
            try {
                this.state = PlayerDataModuleState.RETRIEVING;
                if (this.config.debug) {
                    console.log('[PlayerDataModule] 开始检索数据:', backupKey);
                }
                // 调用 API 检索数据
                const response = await retrieveSpecificPlayerData({
                    path: { backup_key: backupKey },
                    query: { game_id: parseInt(this.config.appid, 10) },
                });
                if (response.data?.code !== 0) {
                    throw new Error(response.data?.msg || '检索数据失败');
                }
                const apiData = response.data.data;
                if (!apiData) {
                    throw new Error('检索响应数据格式错误');
                }
                // 转换响应数据为标准格式
                const result = this.convertPlayerBackupDataToResult(apiData);
                if (this.config.debug) {
                    console.log('[PlayerDataModule] 数据检索成功:', { backupKey, dataSize: result.dataSize });
                }
                return result;
            }
            catch (error) {
                this.state = PlayerDataModuleState.ERROR;
                const errorMessage = `检索数据失败: ${error instanceof Error ? error.message : String(error)}`;
                if (this.config.debug) {
                    console.error('[PlayerDataModule]', errorMessage, error);
                }
                throw new Error(errorMessage);
            }
            finally {
                if (this.state !== PlayerDataModuleState.ERROR) {
                    this.state = PlayerDataModuleState.IDLE;
                }
            }
        }
        /**
         * 检索所有备份数据
         * @returns 所有备份数据列表
         */
        async retrieveAllData() {
            if (this.state !== PlayerDataModuleState.IDLE) {
                throw new Error(`玩家数据模块正在${this.state === PlayerDataModuleState.BACKING_UP ? '备份' : '检索'}中，请稍后再试`);
            }
            const userId = this.getUserId();
            if (!userId) {
                throw new Error('用户未登录，无法检索数据');
            }
            try {
                this.state = PlayerDataModuleState.RETRIEVING;
                if (this.config.debug) {
                    console.log('[PlayerDataModule] 开始检索所有备份数据');
                }
                // 调用 API 检索所有数据
                const response = await retrieveAllPlayerData$1({
                    query: { game_id: parseInt(this.config.appid, 10) },
                });
                if (response.data?.code !== 0) {
                    throw new Error(response.data?.msg || '检索所有数据失败');
                }
                const apiData = response.data.data;
                if (!apiData || !apiData.backups) {
                    return [];
                }
                // 转换响应数据为标准格式
                const results = apiData.backups.map(backup => this.convertPlayerBackupDataToResult(backup));
                if (this.config.debug) {
                    console.log('[PlayerDataModule] 所有数据检索成功:', { count: results.length });
                }
                return results;
            }
            catch (error) {
                this.state = PlayerDataModuleState.ERROR;
                const errorMessage = `检索所有数据失败: ${error instanceof Error ? error.message : String(error)}`;
                if (this.config.debug) {
                    console.error('[PlayerDataModule]', errorMessage, error);
                }
                throw new Error(errorMessage);
            }
            finally {
                if (this.state !== PlayerDataModuleState.ERROR) {
                    this.state = PlayerDataModuleState.IDLE;
                }
            }
        }
        /**
         * 获取备份统计信息
         * @returns 备份统计信息
         */
        async getStats() {
            const userId = this.getUserId();
            if (!userId) {
                throw new Error('用户未登录，无法获取统计信息');
            }
            try {
                if (this.config.debug) {
                    console.log('[PlayerDataModule] 开始获取备份统计信息');
                }
                // 调用 API 获取统计信息
                const response = await getPlayerBackupStats({
                    query: { game_id: parseInt(this.config.appid, 10) },
                });
                if (response.data?.code !== 0) {
                    throw new Error(response.data?.msg || '获取统计信息失败');
                }
                const apiData = response.data.data;
                if (!apiData) {
                    throw new Error('统计信息响应数据格式错误');
                }
                // 转换备份类型统计
                const backupTypes = {
                    [exports.BackupType.MANUAL]: 0,
                    [exports.BackupType.AUTO]: 0,
                    [exports.BackupType.CHECKPOINT]: 0,
                };
                if (apiData.backup_types) {
                    Object.entries(apiData.backup_types).forEach(([key, value]) => {
                        const numKey = parseInt(key, 10);
                        const backupType = NumberToBackupType[numKey];
                        if (backupType) {
                            backupTypes[backupType] = value;
                        }
                    });
                }
                const result = {
                    totalBackups: apiData.total_backups || 0,
                    totalDataSize: apiData.total_data_size || 0,
                    backupTypes,
                    latestBackup: apiData.latest_backup || undefined,
                };
                if (this.config.debug) {
                    console.log('[PlayerDataModule] 统计信息获取成功:', result);
                }
                return result;
            }
            catch (error) {
                const errorMessage = `获取统计信息失败: ${error instanceof Error ? error.message : String(error)}`;
                if (this.config.debug) {
                    console.error('[PlayerDataModule]', errorMessage, error);
                }
                throw new Error(errorMessage);
            }
        }
        /**
         * 获取模块状态
         */
        getModuleStatus() {
            return { state: this.state };
        }
        /**
         * 清理模块资源
         */
        cleanup() {
            this.state = PlayerDataModuleState.IDLE;
            if (this.config.debug) {
                console.log('[PlayerDataModule] 玩家数据模块已清理');
            }
        }
        // ------------------------------------------------------------------------
        // 私有方法
        // ------------------------------------------------------------------------
        /**
         * 转换 API 响应数据为标准格式
         */
        convertPlayerBackupDataToResult(apiData) {
            return {
                backupKey: apiData.backup_key || '',
                backupData: apiData.backup_data || {},
                dataVersion: apiData.data_version || 1,
                backupType: NumberToBackupType[apiData.backup_type || 1] || exports.BackupType.MANUAL,
                backupTypeDescription: apiData.backup_type_description || '',
                description: apiData.description ? apiData.description : undefined,
                createdAt: apiData.created_at || '',
                updatedAt: apiData.updated_at || '',
                dataSize: apiData.data_size || 0,
            };
        }
    }

    /**
     * H5 游戏 SDK 核心类
     *
     * 提供统一的 SDK 接口，管理各个模块的生命周期
     */
    // ============================================================================
    // 类型定义
    // ============================================================================
    /**
     * SDK 初始化状态枚举
     */
    var SDKState;
    (function (SDKState) {
        SDKState["UNINITIALIZED"] = "uninitialized";
        SDKState["INITIALIZING"] = "initializing";
        SDKState["INITIALIZED"] = "initialized";
        SDKState["ERROR"] = "error";
    })(SDKState || (SDKState = {}));
    // ============================================================================
    // 主类定义
    // ============================================================================
    /**
     * H5 游戏 SDK 主类
     *
     * 采用单例模式，提供统一的 SDK 接口
     */
    class GameSDK {
        /**
         * 获取 SDK 实例（单例模式）
         */
        static getInstance() {
            if (!GameSDK.instance) {
                GameSDK.instance = new GameSDK();
            }
            return GameSDK.instance;
        }
        // ========================================================================
        // 构造函数
        // ========================================================================
        /**
         * 私有构造函数，确保单例模式
         */
        constructor() {
            this.state = SDKState.UNINITIALIZED;
        }
        // ========================================================================
        // 初始化相关方法
        // ========================================================================
        /**
         * 初始化 SDK
         * @param config SDK 配置
         * @param adapter 可选的自定义适配器，如果不提供则使用默认的 DsmAdapter
         */
        async init(config, adapter) {
            if (this.state === SDKState.INITIALIZING) {
                throw new SDKError('SDK 正在初始化中，请勿重复调用');
            }
            if (this.state === SDKState.INITIALIZED) {
                if (this.config?.debug) {
                    console.warn('[GameSDK] SDK 已经初始化，跳过重复初始化');
                }
                return;
            }
            this.state = SDKState.INITIALIZING;
            try {
                // 验证配置
                SDKConfigValidator.validateConfig(config);
                // 获取带默认值的完整配置
                const fullConfig = SDKConfigValidator.getConfigWithDefaults(config);
                this.config = fullConfig;
                // 配置 API 客户端
                try {
                    client.setConfig({
                        baseUrl: 'https://open.anyigame.cn',
                        headers: {
                            'X-Channel-Code': this.config.channel,
                            'Content-Type': 'application/json',
                        },
                    });
                    if (this.config.debug) {
                        console.log('[GameSDK] API 客户端配置成功:', {
                            baseUrl: 'https://open.anyigame.cn',
                            headers: {
                                'X-Channel-Code': this.config.channel,
                                'Content-Type': 'application/json',
                            }
                        });
                    }
                }
                catch (error) {
                    if (this.config.debug) {
                        console.error('[GameSDK] API 客户端配置失败:', error);
                    }
                    throw new SDKError(`API 客户端配置失败: ${error instanceof Error ? error.message : String(error)}`);
                }
                // 初始化适配器
                this.adapter = adapter || new DsmAdapter();
                // 设置适配器的调试模式
                if ('setDebugMode' in this.adapter && typeof this.adapter.setDebugMode === 'function') {
                    this.adapter.setDebugMode(fullConfig.debug);
                }
                // 检查原生环境
                const nativeEnv = this.adapter.detectNativeEnvironment();
                if (!nativeEnv.hasNativeSupport) {
                    throw new SDKError('未检测到支持的原生环境');
                }
                if (config.debug) {
                    console.log('[GameSDK] 检测到原生环境:', nativeEnv);
                }
                // 初始化用户模块
                this.userModule = new UserModule(config);
                await this.userModule.initialize();
                // 初始化事件模块
                this.eventModule = new EventModule(config, () => this.userModule?.getUserId() ?? null);
                // 上报 SDK 初始化开始事件
                this.eventModule.reportEvent(SDKEventName.SDK_INIT_START, {
                    custom_data: {
                        config: {
                            appid: this.config.appid,
                            channel: this.config.channel,
                            debug: this.config.debug,
                        },
                        nativeEnv
                    },
                });
                // 初始化广告模块
                this.adModule = new AdModule(this.adapter, config, this.eventModule);
                // 初始化玩家数据模块
                this.playerDataModule = new PlayerDataModule(config, () => this.userModule?.getUserId() || null);
                this.state = SDKState.INITIALIZED;
                // 初始化心跳模块并启动
                this.heartbeatModule = new HeartbeatModule(config);
                this.heartbeatModule.start();
                // 上报 SDK 初始化成功事件
                this.eventModule.reportEvent(SDKEventName.SDK_INIT_SUCCESS, {
                    custom_data: {
                        init_duration: Date.now(),
                        user_id: this.userModule.getUserId(),
                    },
                });
                if (config.debug) {
                    console.log('[GameSDK] SDK 初始化成功');
                }
            }
            catch (error) {
                this.state = SDKState.ERROR;
                this.initError = error instanceof Error ? error : new Error(String(error));
                // 上报 SDK 初始化失败事件
                if (this.eventModule) {
                    this.eventModule.reportEvent(SDKEventName.SDK_INIT_FAILED, {
                        error_message: this.initError.message,
                        custom_data: {
                            error_time: Date.now(),
                        },
                    });
                }
                const errorMessage = `SDK 初始化失败: ${this.initError.message}`;
                if (config.debug) {
                    console.error('[GameSDK]', errorMessage, error);
                }
                throw new SDKError(errorMessage, undefined, this.initError);
            }
        }
        // ========================================================================
        // 用户相关方法
        // ========================================================================
        /**
         * 获取用户信息（同步）
         * @returns 用户信息，如果未初始化或用户信息不存在则返回 null
         */
        getUserInfo() {
            this.checkInitialized();
            return this.userModule?.getUserInfo() ?? null;
        }
        /**
         * 刷新用户信息
         * @returns Promise<User> 最新的用户信息
         */
        async refreshUserInfo() {
            this.checkInitialized();
            if (!this.userModule) {
                throw new SDKError('用户模块未初始化');
            }
            return await this.userModule.refreshUserInfo();
        }
        // ========================================================================
        // 游戏配置相关方法
        // ========================================================================
        /**
         * 获取游戏配置
         * @returns Promise<GameConfig> 游戏配置信息
         */
        async getGameConfig() {
            this.checkInitialized();
            if (!this.config) {
                throw new SDKError('SDK 配置未初始化');
            }
            try {
                const response = await getGameConfig$1({
                    client: client,
                    query: {
                        code: this.config.appid,
                    },
                    headers: {
                        'X-Channel-Code': this.config.channel,
                    },
                });
                if (response.data?.data) {
                    return response.data.data;
                }
                else {
                    throw new SDKError('API 返回的游戏配置格式不正确');
                }
            }
            catch (error) {
                const errorMessage = `获取游戏配置失败: ${error instanceof Error ? error.message : String(error)}`;
                if (this.config.debug) {
                    console.error('[GameSDK]', errorMessage, error);
                }
                throw new SDKError(errorMessage, undefined, error instanceof Error ? error : undefined);
            }
        }
        // ========================================================================
        // 广告相关方法
        // ========================================================================
        /**
         * 显示广告
         * @param callback 广告关闭时的回调函数
         */
        async showAd(callback) {
            this.checkInitialized();
            if (!this.adModule) {
                throw new SDKError('广告模块未初始化');
            }
            await this.adModule.showAd(callback);
        }
        /**
         * 检查是否可以显示广告
         */
        canShowAd() {
            if (this.state !== SDKState.INITIALIZED || !this.adModule) {
                return false;
            }
            return this.adModule.canShowAd();
        }
        // ========================================================================
        // 玩家数据备份相关方法
        // ========================================================================
        /**
         * 备份玩家数据
         * @param backupKey 备份数据键名，用于区分不同类型的数据
         * @param data 要备份的数据
         * @param options 备份选项
         * @returns 备份结果
         */
        async backupPlayerData(backupKey, data, options) {
            this.checkInitialized();
            if (!this.playerDataModule) {
                throw new SDKError('玩家数据模块未初始化');
            }
            return await this.playerDataModule.backupData(backupKey, data, options);
        }
        /**
         * 检索特定备份数据
         * @param backupKey 备份数据键名
         * @returns 备份数据
         */
        async retrievePlayerData(backupKey) {
            this.checkInitialized();
            if (!this.playerDataModule) {
                throw new SDKError('玩家数据模块未初始化');
            }
            return await this.playerDataModule.retrieveData(backupKey);
        }
        /**
         * 检索所有备份数据
         * @returns 所有备份数据列表
         */
        async retrieveAllPlayerData() {
            this.checkInitialized();
            if (!this.playerDataModule) {
                throw new SDKError('玩家数据模块未初始化');
            }
            return await this.playerDataModule.retrieveAllData();
        }
        /**
         * 获取备份统计信息
         * @returns 备份统计信息
         */
        async getPlayerDataStats() {
            this.checkInitialized();
            if (!this.playerDataModule) {
                throw new SDKError('玩家数据模块未初始化');
            }
            return await this.playerDataModule.getStats();
        }
        /**
         * 强制重置广告状态
         * 用于异常情况下的状态恢复
         */
        resetState() {
            if (this.config?.debug) {
                console.log('[GameSDK] 强制重置广告状态');
            }
            if (this.adModule) {
                this.adModule.resetState();
            }
            else {
                if (this.config?.debug) {
                    console.warn('[GameSDK] 广告模块未初始化，无法重置状态');
                }
            }
        }
        // ========================================================================
        // 状态和调试相关方法
        // ========================================================================
        /**
         * 获取 SDK 状态
         */
        getState() {
            return this.state;
        }
        /**
         * 获取 SDK 版本信息
         */
        getVersion() {
            return '1.0.4';
        }
        /**
         * 获取调试信息
         */
        getDebugInfo() {
            return {
                state: this.state,
                version: this.getVersion(),
                config: this.config,
                userInfo: this.getUserInfo(),
                canShowAd: this.canShowAd(),
                adState: this.adModule?.getAdState(),
                eventQueueStatus: this.eventModule?.getQueueStatus(),
                heartbeatStatus: this.heartbeatModule?.getStatus(),
                playerDataStatus: this.playerDataModule?.getModuleStatus(),
                initError: this.initError?.message,
            };
        }
        // ========================================================================
        // 生命周期管理方法
        // ========================================================================
        /**
         * 销毁 SDK
         * 清理所有资源和事件监听器
         */
        destroy() {
            const debug = this.config?.debug;
            if (debug) {
                console.log('[GameSDK] 开始销毁 SDK');
            }
            // 清理各个模块
            this.heartbeatModule?.cleanup();
            this.playerDataModule?.cleanup();
            this.eventModule?.cleanup();
            this.adModule?.cleanup();
            this.userModule?.cleanup();
            this.adapter?.cleanup();
            // 重置状态
            this.state = SDKState.UNINITIALIZED;
            this.config = undefined;
            this.adapter = undefined;
            this.userModule = undefined;
            this.adModule = undefined;
            this.eventModule = undefined;
            this.heartbeatModule = undefined;
            this.playerDataModule = undefined;
            this.initError = undefined;
            // 清除单例实例
            GameSDK.instance = null;
            if (debug) {
                console.log('[GameSDK] SDK 销毁完成');
            }
        }
        // ========================================================================
        // 私有辅助方法
        // ========================================================================
        /**
         * 检查 SDK 是否已初始化
         */
        checkInitialized() {
            if (this.state === SDKState.UNINITIALIZED) {
                throw new SDKError('SDK 未初始化，请先调用 init() 方法');
            }
            if (this.state === SDKState.INITIALIZING) {
                throw new SDKError('SDK 正在初始化中，请等待初始化完成');
            }
            if (this.state === SDKState.ERROR) {
                throw new SDKError(`SDK 初始化失败: ${this.initError?.message ?? '未知错误'}`);
            }
        }
    }
    // ========================================================================
    // 静态属性和方法（单例模式）
    // ========================================================================
    GameSDK.instance = null;

    /**
     * 跨 iframe SDK 访问模块
     *
     * 支持在顶层窗口初始化 SDK，然后在 iframe 中访问该 SDK 实例
     * 解决 iframe 隔离导致的 window.closeAd 回调无法访问的问题
     */
    // ============================================================================
    // 常量定义
    // ============================================================================
    const GLOBAL_SDK_KEY = '__ANYIGAME_AD_SDK__';
    const MESSAGE_PREFIX = 'anyigame-ad-sdk:';
    // ============================================================================
    // 跨 iframe SDK 管理器
    // ============================================================================
    /**
     * 跨 iframe SDK 管理器
     * 负责在顶层窗口初始化 SDK，并处理来自 iframe 的请求
     */
    class CrossIframeSDKManager {
        // --------------------------------------------------------------------------
        // 公共方法
        // --------------------------------------------------------------------------
        /**
         * 在顶层窗口初始化 SDK
         * @param config SDK 配置
         * @param adapter 可选的自定义适配器
         */
        static async initInTopWindow(config, adapter) {
            // 确保在顶层窗口执行
            if (window !== window.top) {
                throw new Error('initInTopWindow 必须在顶层窗口中调用');
            }
            // 获取或创建 SDK 实例
            const sdk = GameSDK.getInstance();
            // 初始化 SDK
            await sdk.init(config, adapter);
            // 将 SDK 实例存储到全局对象
            window[this.GLOBAL_SDK_KEY] = {
                sdk,
                config,
                initialized: true,
                version: '1.0.4'
            };
            // 设置消息监听器，处理来自 iframe 的请求
            this.setupMessageListener();
            if (config.debug) {
                console.log('[CrossIframeSDKManager] SDK 已在顶层窗口初始化完成');
            }
        }
        /**
         * 从 iframe 中获取顶层窗口的 SDK 实例
         */
        static getSDKFromTopWindow() {
            try {
                // 尝试访问顶层窗口的 SDK 实例
                const topWindow = window.top;
                if (!topWindow) {
                    return null;
                }
                const globalSDK = topWindow[this.GLOBAL_SDK_KEY];
                if (globalSDK && globalSDK.initialized && globalSDK.sdk) {
                    return globalSDK.sdk;
                }
                return null;
            }
            catch (error) {
                // 跨域访问可能会失败
                console.warn('[CrossIframeSDKManager] 无法访问顶层窗口的 SDK 实例:', error);
                return null;
            }
        }
        /**
         * 检查顶层窗口是否已初始化 SDK
         */
        static isSDKInitializedInTopWindow() {
            try {
                const topWindow = window.top;
                if (!topWindow) {
                    return false;
                }
                const globalSDK = topWindow[this.GLOBAL_SDK_KEY];
                return !!(globalSDK && globalSDK.initialized);
            }
            catch (error) {
                return false;
            }
        }
        /**
         * 在 iframe 中显示广告（通过消息传递）
         * @param callback 广告关闭回调
         */
        static async showAdFromIframe(callback) {
            // 首先尝试直接访问顶层窗口的 SDK
            const sdk = this.getSDKFromTopWindow();
            if (sdk) {
                return sdk.showAd(callback);
            }
            // 如果无法直接访问，使用消息传递
            return new Promise((resolve, reject) => {
                if (!window.top) {
                    reject(new Error('无法访问顶层窗口'));
                    return;
                }
                // 生成唯一的请求 ID
                const requestId = `showAd_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
                // 设置回调处理
                const messageHandler = (event) => {
                    if (event.data?.type === `${this.MESSAGE_PREFIX}showAd-response` &&
                        event.data?.requestId === requestId) {
                        window.removeEventListener('message', messageHandler);
                        if (event.data.success) {
                            resolve();
                        }
                        else {
                            reject(new Error(event.data.error || '显示广告失败'));
                        }
                    }
                    else if (event.data?.type === `${this.MESSAGE_PREFIX}adClose` &&
                        event.data?.requestId === requestId) {
                        // 处理广告关闭回调
                        if (callback) {
                            callback(event.data.closeType);
                        }
                    }
                };
                window.addEventListener('message', messageHandler);
                // 向顶层窗口发送显示广告请求
                window.top.postMessage({
                    type: `${this.MESSAGE_PREFIX}showAd`,
                    requestId,
                    hasCallback: !!callback
                }, '*');
                // 设置超时
                setTimeout(() => {
                    window.removeEventListener('message', messageHandler);
                    reject(new Error('显示广告请求超时'));
                }, 10000);
            });
        }
        /**
         * 清理跨 iframe SDK 资源
         */
        static cleanup() {
            if (window === window.top) {
                // 在顶层窗口清理全局 SDK 实例
                const globalSDK = window[this.GLOBAL_SDK_KEY];
                if (globalSDK && globalSDK.sdk) {
                    globalSDK.sdk.destroy();
                }
                delete window[this.GLOBAL_SDK_KEY];
            }
        }
        // --------------------------------------------------------------------------
        // 私有方法 - 消息处理
        // --------------------------------------------------------------------------
        /**
         * 设置消息监听器（在顶层窗口中）
         */
        static setupMessageListener() {
            window.addEventListener('message', (event) => {
                if (!event.data?.type?.startsWith(this.MESSAGE_PREFIX)) {
                    return;
                }
                const { type } = event.data;
                if (type === `${this.MESSAGE_PREFIX}showAd`) {
                    this.handleShowAdRequest(event);
                }
                else if (type === `${this.MESSAGE_PREFIX}backupPlayerData`) {
                    this.handleBackupPlayerDataRequest(event);
                }
                else if (type === `${this.MESSAGE_PREFIX}retrievePlayerData`) {
                    this.handleRetrievePlayerDataRequest(event);
                }
                else if (type === `${this.MESSAGE_PREFIX}retrieveAllPlayerData`) {
                    this.handleRetrieveAllPlayerDataRequest(event);
                }
                else if (type === `${this.MESSAGE_PREFIX}getPlayerDataStats`) {
                    this.handleGetPlayerDataStatsRequest(event);
                }
            });
        }
        /**
         * 处理来自 iframe 的显示广告请求
         */
        static async handleShowAdRequest(event) {
            const { requestId, hasCallback } = event.data;
            const sourceWindow = event.source;
            try {
                const globalSDK = window[this.GLOBAL_SDK_KEY];
                if (!globalSDK || !globalSDK.sdk) {
                    throw new Error('SDK 未初始化');
                }
                const sdk = globalSDK.sdk;
                // 创建回调函数，将结果发送回 iframe
                const callback = hasCallback ? (closeType) => {
                    sourceWindow.postMessage({
                        type: `${this.MESSAGE_PREFIX}adClose`,
                        requestId,
                        closeType
                    }, '*');
                } : undefined;
                // 调用 SDK 显示广告
                await sdk.showAd(callback);
                // 发送成功响应
                sourceWindow.postMessage({
                    type: `${this.MESSAGE_PREFIX}showAd-response`,
                    requestId,
                    success: true
                }, '*');
            }
            catch (error) {
                // 发送错误响应
                sourceWindow.postMessage({
                    type: `${this.MESSAGE_PREFIX}showAd-response`,
                    requestId,
                    success: false,
                    error: error instanceof Error ? error.message : String(error)
                }, '*');
            }
        }
        /**
         * 处理来自 iframe 的备份玩家数据请求
         */
        static async handleBackupPlayerDataRequest(event) {
            const { requestId, backupKey, data, options } = event.data;
            const sourceWindow = event.source;
            try {
                const globalSDK = window[this.GLOBAL_SDK_KEY];
                if (!globalSDK || !globalSDK.sdk) {
                    throw new Error('SDK 未初始化');
                }
                const sdk = globalSDK.sdk;
                const result = await sdk.backupPlayerData(backupKey, data, options);
                // 发送成功响应
                sourceWindow.postMessage({
                    type: `${this.MESSAGE_PREFIX}backupPlayerData-response`,
                    requestId,
                    success: true,
                    result
                }, '*');
            }
            catch (error) {
                // 发送错误响应
                sourceWindow.postMessage({
                    type: `${this.MESSAGE_PREFIX}backupPlayerData-response`,
                    requestId,
                    success: false,
                    error: error instanceof Error ? error.message : String(error)
                }, '*');
            }
        }
        /**
         * 处理来自 iframe 的检索玩家数据请求
         */
        static async handleRetrievePlayerDataRequest(event) {
            const { requestId, backupKey } = event.data;
            const sourceWindow = event.source;
            try {
                const globalSDK = window[this.GLOBAL_SDK_KEY];
                if (!globalSDK || !globalSDK.sdk) {
                    throw new Error('SDK 未初始化');
                }
                const sdk = globalSDK.sdk;
                const result = await sdk.retrievePlayerData(backupKey);
                // 发送成功响应
                sourceWindow.postMessage({
                    type: `${this.MESSAGE_PREFIX}retrievePlayerData-response`,
                    requestId,
                    success: true,
                    result
                }, '*');
            }
            catch (error) {
                // 发送错误响应
                sourceWindow.postMessage({
                    type: `${this.MESSAGE_PREFIX}retrievePlayerData-response`,
                    requestId,
                    success: false,
                    error: error instanceof Error ? error.message : String(error)
                }, '*');
            }
        }
        /**
         * 处理来自 iframe 的检索所有玩家数据请求
         */
        static async handleRetrieveAllPlayerDataRequest(event) {
            const { requestId } = event.data;
            const sourceWindow = event.source;
            try {
                const globalSDK = window[this.GLOBAL_SDK_KEY];
                if (!globalSDK || !globalSDK.sdk) {
                    throw new Error('SDK 未初始化');
                }
                const sdk = globalSDK.sdk;
                const result = await sdk.retrieveAllPlayerData();
                // 发送成功响应
                sourceWindow.postMessage({
                    type: `${this.MESSAGE_PREFIX}retrieveAllPlayerData-response`,
                    requestId,
                    success: true,
                    result
                }, '*');
            }
            catch (error) {
                // 发送错误响应
                sourceWindow.postMessage({
                    type: `${this.MESSAGE_PREFIX}retrieveAllPlayerData-response`,
                    requestId,
                    success: false,
                    error: error instanceof Error ? error.message : String(error)
                }, '*');
            }
        }
        /**
         * 处理来自 iframe 的获取玩家数据统计请求
         */
        static async handleGetPlayerDataStatsRequest(event) {
            const { requestId } = event.data;
            const sourceWindow = event.source;
            try {
                const globalSDK = window[this.GLOBAL_SDK_KEY];
                if (!globalSDK || !globalSDK.sdk) {
                    throw new Error('SDK 未初始化');
                }
                const sdk = globalSDK.sdk;
                const result = await sdk.getPlayerDataStats();
                // 发送成功响应
                sourceWindow.postMessage({
                    type: `${this.MESSAGE_PREFIX}getPlayerDataStats-response`,
                    requestId,
                    success: true,
                    result
                }, '*');
            }
            catch (error) {
                // 发送错误响应
                sourceWindow.postMessage({
                    type: `${this.MESSAGE_PREFIX}getPlayerDataStats-response`,
                    requestId,
                    success: false,
                    error: error instanceof Error ? error.message : String(error)
                }, '*');
            }
        }
    }
    CrossIframeSDKManager.GLOBAL_SDK_KEY = GLOBAL_SDK_KEY;
    CrossIframeSDKManager.MESSAGE_PREFIX = MESSAGE_PREFIX;
    // ============================================================================
    // 跨 iframe SDK 代理类
    // ============================================================================
    /**
     * 跨 iframe SDK 代理类
     * 在 iframe 中使用，提供与普通 SDK 相同的接口
     */
    class CrossIframeSDKProxy {
        // --------------------------------------------------------------------------
        // 构造函数
        // --------------------------------------------------------------------------
        constructor() {
            this.topWindowSDK = null;
            this.topWindowSDK = CrossIframeSDKManager.getSDKFromTopWindow();
            // 强制绑定方法到实例，确保在生产环境中可访问
            this.isAvailable = this.isAvailable.bind(this);
            this.showAd = this.showAd.bind(this);
            this.canShowAd = this.canShowAd.bind(this);
            this.getUserInfo = this.getUserInfo.bind(this);
            this.refreshUserInfo = this.refreshUserInfo.bind(this);
            this.getGameConfig = this.getGameConfig.bind(this);
            this.getState = this.getState.bind(this);
            this.getVersion = this.getVersion.bind(this);
            this.getDebugInfo = this.getDebugInfo.bind(this);
            this.backupPlayerData = this.backupPlayerData.bind(this);
            this.retrievePlayerData = this.retrievePlayerData.bind(this);
            this.retrieveAllPlayerData = this.retrieveAllPlayerData.bind(this);
            this.getPlayerDataStats = this.getPlayerDataStats.bind(this);
        }
        // --------------------------------------------------------------------------
        // 公共方法 - SDK 状态检查
        // --------------------------------------------------------------------------
        /**
         * 检查 SDK 是否可用
         */
        isAvailable() {
            return CrossIframeSDKManager.isSDKInitializedInTopWindow();
        }
        // --------------------------------------------------------------------------
        // 公共方法 - 广告相关
        // --------------------------------------------------------------------------
        /**
         * 显示广告
         */
        async showAd(callback) {
            if (this.topWindowSDK) {
                // 直接调用顶层窗口的 SDK
                return this.topWindowSDK.showAd(callback);
            }
            else {
                // 使用消息传递
                return CrossIframeSDKManager.showAdFromIframe(callback);
            }
        }
        /**
         * 检查是否可以显示广告
         */
        canShowAd() {
            if (this.topWindowSDK) {
                return this.topWindowSDK.canShowAd();
            }
            return false;
        }
        // --------------------------------------------------------------------------
        // 公共方法 - 用户信息
        // --------------------------------------------------------------------------
        /**
         * 获取用户信息
         */
        getUserInfo() {
            if (this.topWindowSDK) {
                return this.topWindowSDK.getUserInfo();
            }
            return null;
        }
        /**
         * 刷新用户信息
         */
        async refreshUserInfo() {
            if (this.topWindowSDK) {
                return this.topWindowSDK.refreshUserInfo();
            }
            throw new Error('SDK 不可用');
        }
        // --------------------------------------------------------------------------
        // 公共方法 - 游戏配置
        // --------------------------------------------------------------------------
        /**
         * 获取游戏配置
         */
        async getGameConfig() {
            if (this.topWindowSDK) {
                return this.topWindowSDK.getGameConfig();
            }
            throw new Error('SDK 不可用');
        }
        // --------------------------------------------------------------------------
        // 公共方法 - SDK 信息
        // --------------------------------------------------------------------------
        /**
         * 获取 SDK 状态
         */
        getState() {
            if (this.topWindowSDK) {
                return this.topWindowSDK.getState();
            }
            return 'unavailable';
        }
        /**
         * 获取 SDK 版本
         */
        getVersion() {
            if (this.topWindowSDK) {
                return this.topWindowSDK.getVersion();
            }
            return '1.0.4';
        }
        /**
         * 获取调试信息
         */
        getDebugInfo() {
            if (this.topWindowSDK) {
                return this.topWindowSDK.getDebugInfo();
            }
            return { status: 'SDK not available in iframe' };
        }
        // --------------------------------------------------------------------------
        // 公共方法 - 玩家数据备份
        // --------------------------------------------------------------------------
        /**
         * 备份玩家数据
         * @param backupKey 备份数据键名，用于区分不同类型的数据
         * @param data 要备份的数据
         * @param options 备份选项
         * @returns 备份结果
         */
        async backupPlayerData(backupKey, data, options) {
            if (this.topWindowSDK) {
                // 直接调用顶层窗口的 SDK
                return this.topWindowSDK.backupPlayerData(backupKey, data, options);
            }
            else {
                // 使用消息传递
                return this.sendPlayerDataMessage('backupPlayerData', { backupKey, data, options });
            }
        }
        /**
         * 检索特定备份数据
         * @param backupKey 备份数据键名
         * @returns 备份数据
         */
        async retrievePlayerData(backupKey) {
            if (this.topWindowSDK) {
                // 直接调用顶层窗口的 SDK
                return this.topWindowSDK.retrievePlayerData(backupKey);
            }
            else {
                // 使用消息传递
                return this.sendPlayerDataMessage('retrievePlayerData', { backupKey });
            }
        }
        /**
         * 检索所有备份数据
         * @returns 所有备份数据列表
         */
        async retrieveAllPlayerData() {
            if (this.topWindowSDK) {
                // 直接调用顶层窗口的 SDK
                return this.topWindowSDK.retrieveAllPlayerData();
            }
            else {
                // 使用消息传递
                return this.sendPlayerDataMessage('retrieveAllPlayerData', {});
            }
        }
        /**
         * 获取备份统计信息
         * @returns 备份统计信息
         */
        async getPlayerDataStats() {
            if (this.topWindowSDK) {
                // 直接调用顶层窗口的 SDK
                return this.topWindowSDK.getPlayerDataStats();
            }
            else {
                // 使用消息传递
                return this.sendPlayerDataMessage('getPlayerDataStats', {});
            }
        }
        // --------------------------------------------------------------------------
        // 私有方法 - 玩家数据消息传递
        // --------------------------------------------------------------------------
        /**
         * 发送玩家数据相关消息到顶层窗口
         */
        async sendPlayerDataMessage(method, params) {
            return new Promise((resolve, reject) => {
                if (!window.top) {
                    reject(new Error('无法访问顶层窗口'));
                    return;
                }
                // 生成唯一的请求 ID
                const requestId = `${method}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
                // 设置回调处理
                const messageHandler = (event) => {
                    if (event.data?.type === `${CrossIframeSDKManager['MESSAGE_PREFIX']}${method}-response` &&
                        event.data?.requestId === requestId) {
                        window.removeEventListener('message', messageHandler);
                        if (event.data.success) {
                            resolve(event.data.result);
                        }
                        else {
                            reject(new Error(event.data.error || `${method} 失败`));
                        }
                    }
                };
                window.addEventListener('message', messageHandler);
                // 向顶层窗口发送请求
                window.top.postMessage({
                    type: `${CrossIframeSDKManager['MESSAGE_PREFIX']}${method}`,
                    requestId,
                    ...params
                }, '*');
                // 设置超时
                setTimeout(() => {
                    window.removeEventListener('message', messageHandler);
                    reject(new Error(`${method} 请求超时`));
                }, 10000);
            });
        }
    }

    /**
     * @anyigame/ad-sdk
     *
     * H5 游戏 SDK，用于桥接 H5 与原生应用环境
     * 核心功能包括广告展示、用户系统
     *
     * <AUTHOR>
     * @version 1.0.4
     */
    // 枚举和常量
    // ============================================================================
    // SDK 实例和核心功能
    // ============================================================================
    // 创建默认 SDK 实例
    const sdk = GameSDK.getInstance();
    /**
     * 初始化 SDK
     *
     * @param config SDK 配置
     * @example
     * ```typescript
     * import { init, AdCloseType } from '@anyigame/ad-sdk';
     *
     * // 初始化 SDK
     * await init({
     *   appid: '1001',
     *   channel: '1',
     *   debug: true
     * });
     *
     * // 显示广告
     * await showAd((type) => {
     *   if (type === AdCloseType.COMPLETED) {
     *     console.log('用户观看完成，发放奖励');
     *   } else {
     *     console.log('用户取消观看');
     *   }
     * });
     * ```
     */
    const init = sdk.init.bind(sdk);
    // ============================================================================
    // 广告相关功能
    // ============================================================================
    /**
     * 显示广告
     *
     * @param callback 广告关闭时的回调函数
     * @example
     * ```typescript
     * await showAd((type) => {
     *   if (type === AdCloseType.COMPLETED) {
     *     // 用户观看完成，发放奖励
     *     grantReward();
     *   } else {
     *     // 用户取消观看
     *     console.log('用户取消观看广告');
     *   }
     * });
     * ```
     */
    const showAd = sdk.showAd.bind(sdk);
    /**
     * 检查是否可以显示广告
     *
     * @returns boolean 是否可以显示广告
     * @example
     * ```typescript
     * if (canShowAd()) {
     *   await showAd();
     * } else {
     *   console.log('当前无法显示广告');
     * }
     * ```
     */
    const canShowAd = sdk.canShowAd.bind(sdk);
    // ============================================================================
    // 用户信息相关功能
    // ============================================================================
    /**
     * 获取用户信息（同步）
     *
     * @returns 用户信息，如果未初始化或用户信息不存在则返回 null
     * @example
     * ```typescript
     * const userInfo = getUserInfo();
     * if (userInfo) {
     *   console.log('用户昵称:', userInfo.nickname);
     * }
     * ```
     */
    const getUserInfo = sdk.getUserInfo.bind(sdk);
    /**
     * 刷新用户信息
     *
     * @returns Promise<User> 最新的用户信息
     * @example
     * ```typescript
     * try {
     *   const userInfo = await refreshUserInfo();
     *   console.log('刷新后的用户信息:', userInfo);
     * } catch (error) {
     *   console.error('刷新用户信息失败:', error);
     * }
     * ```
     */
    const refreshUserInfo = sdk.refreshUserInfo.bind(sdk);
    // ============================================================================
    // 游戏配置相关功能
    // ============================================================================
    /**
     * 获取游戏配置
     *
     * @returns Promise<GameConfig> 游戏配置信息
     * @example
     * ```typescript
     * try {
     *   const gameConfig = await getGameConfig();
     *   console.log('游戏配置:', gameConfig);
     * } catch (error) {
     *   console.error('获取游戏配置失败:', error);
     * }
     * ```
     */
    const getGameConfig = sdk.getGameConfig.bind(sdk);
    // ============================================================================
    // 玩家数据备份相关功能
    // ============================================================================
    /**
     * 备份玩家数据
     *
     * @param backupKey 备份数据键名，用于区分不同类型的数据
     * @param data 要备份的数据
     * @param options 备份选项
     * @returns Promise<PlayerDataBackupResult> 备份结果
     * @example
     * ```typescript
     * import { backupPlayerData, BackupType } from '@anyigame/ad-sdk';
     *
     * try {
     *   const result = await backupPlayerData('inventory', {
     *     items: [{ id: 1, name: 'sword', count: 1 }],
     *     gold: 1000
     *   }, {
     *     backupType: BackupType.MANUAL,
     *     description: 'Player inventory backup'
     *   });
     *   console.log('备份成功:', result);
     * } catch (error) {
     *   console.error('备份失败:', error);
     * }
     * ```
     */
    const backupPlayerData = sdk.backupPlayerData.bind(sdk);
    /**
     * 检索特定备份数据
     *
     * @param backupKey 备份数据键名
     * @returns Promise<PlayerDataRetrieveResult> 备份数据
     * @example
     * ```typescript
     * import { retrievePlayerData } from '@anyigame/ad-sdk';
     *
     * try {
     *   const data = await retrievePlayerData('inventory');
     *   console.log('检索到的数据:', data.backupData);
     * } catch (error) {
     *   console.error('检索失败:', error);
     * }
     * ```
     */
    const retrievePlayerData = sdk.retrievePlayerData.bind(sdk);
    /**
     * 检索所有备份数据
     *
     * @returns Promise<PlayerDataRetrieveResult[]> 所有备份数据列表
     * @example
     * ```typescript
     * import { retrieveAllPlayerData } from '@anyigame/ad-sdk';
     *
     * try {
     *   const allData = await retrieveAllPlayerData();
     *   allData.forEach(backup => {
     *     console.log(`备份键: ${backup.backupKey}, 数据:`, backup.backupData);
     *   });
     * } catch (error) {
     *   console.error('检索失败:', error);
     * }
     * ```
     */
    const retrieveAllPlayerData = sdk.retrieveAllPlayerData.bind(sdk);
    /**
     * 获取备份统计信息
     *
     * @returns Promise<PlayerDataStats> 备份统计信息
     * @example
     * ```typescript
     * import { getPlayerDataStats } from '@anyigame/ad-sdk';
     *
     * try {
     *   const stats = await getPlayerDataStats();
     *   console.log(`总备份数: ${stats.totalBackups}, 总大小: ${stats.totalDataSize} 字节`);
     * } catch (error) {
     *   console.error('获取统计信息失败:', error);
     * }
     * ```
     */
    const getPlayerDataStats = sdk.getPlayerDataStats.bind(sdk);
    // ============================================================================
    // SDK 状态和工具功能
    // ============================================================================
    /**
     * 获取 SDK 状态
     *
     * @returns string SDK 当前状态
     */
    const getState = sdk.getState.bind(sdk);
    /**
     * 获取 SDK 版本信息
     *
     * @returns string SDK 版本号
     */
    const getVersion = sdk.getVersion.bind(sdk);
    /**
     * 获取调试信息
     *
     * @returns object 包含 SDK 各种状态信息的对象
     */
    const getDebugInfo = sdk.getDebugInfo.bind(sdk);
    /**
     * 强制重置广告状态
     * 用于异常情况下的状态恢复
     *
     * @example
     * ```typescript
     * // 当广告状态异常时，强制重置
     * if (!canShowAd()) {
     *   resetState();
     *   console.log('广告状态已重置');
     * }
     * ```
     */
    const resetState = sdk.resetState.bind(sdk);
    /**
     * 销毁 SDK
     * 清理所有资源和事件监听器
     *
     * @example
     * ```typescript
     * // 在页面卸载时销毁 SDK
     * window.addEventListener('beforeunload', () => {
     *   destroy();
     * });
     * ```
     */
    const destroy = sdk.destroy.bind(sdk);
    /**
     * 在顶层窗口初始化 SDK（跨 iframe 模式）
     *
     * @param config SDK 配置
     * @param adapter 可选的自定义适配器
     * @example
     * ```typescript
     * // 在顶层窗口（父页面）中初始化 SDK
     * import { initInTopWindow } from '@anyigame/ad-sdk';
     *
     * await initInTopWindow({
     *   appid: '1001',
     *   channel: '1',
     *   debug: true
     * });
     * ```
     */
    const initInTopWindow = CrossIframeSDKManager.initInTopWindow.bind(CrossIframeSDKManager);
    /**
     * 检查顶层窗口是否已初始化 SDK
     *
     * @returns boolean 是否已初始化
     * @example
     * ```typescript
     * import { isSDKInitializedInTopWindow } from '@anyigame/ad-sdk';
     *
     * if (isSDKInitializedInTopWindow()) {
     *   console.log('SDK 已在顶层窗口初始化');
     * }
     * ```
     */
    const isSDKInitializedInTopWindow = CrossIframeSDKManager.isSDKInitializedInTopWindow.bind(CrossIframeSDKManager);
    /**
     * 清理跨 iframe SDK 资源
     *
     * @example
     * ```typescript
     * import { cleanupCrossIframeSDK } from '@anyigame/ad-sdk';
     *
     * // 在页面卸载时清理
     * window.addEventListener('beforeunload', () => {
     *   cleanupCrossIframeSDK();
     * });
     * ```
     */
    const cleanupCrossIframeSDK = CrossIframeSDKManager.cleanup.bind(CrossIframeSDKManager);
    /**
     * 创建 iframe SDK 代理实例
     *
     * @returns CrossIframeSDKProxy 代理实例
     * @example
     * ```typescript
     * import { createIframeSDKProxy, AdCloseType } from '@anyigame/ad-sdk';
     *
     * // 在 iframe 中创建代理
     * const sdkProxy = createIframeSDKProxy();
     *
     * if (sdkProxy.isAvailable()) {
     *   await sdkProxy.showAd((type) => {
     *     if (type === AdCloseType.COMPLETED) {
     *       console.log('用户观看完成');
     *     }
     *   });
     * }
     * ```
     */
    function createIframeSDKProxy() {
        return new CrossIframeSDKProxy();
    }

    exports.BaseNativeBridgeAdapter = BaseNativeBridgeAdapter;
    exports.CrossIframeSDKManager = CrossIframeSDKManager;
    exports.CrossIframeSDKProxy = CrossIframeSDKProxy;
    exports.DsmAdapter = DsmAdapter;
    exports.GameSDK = GameSDK;
    exports.SDKConfigValidator = SDKConfigValidator;
    exports.SDKError = SDKError;
    exports.backupPlayerData = backupPlayerData;
    exports.canShowAd = canShowAd;
    exports.cleanupCrossIframeSDK = cleanupCrossIframeSDK;
    exports.createIframeSDKProxy = createIframeSDKProxy;
    exports.default = sdk;
    exports.destroy = destroy;
    exports.getDebugInfo = getDebugInfo;
    exports.getGameConfig = getGameConfig;
    exports.getPlayerDataStats = getPlayerDataStats;
    exports.getState = getState;
    exports.getUserInfo = getUserInfo;
    exports.getVersion = getVersion;
    exports.init = init;
    exports.initInTopWindow = initInTopWindow;
    exports.isSDKInitializedInTopWindow = isSDKInitializedInTopWindow;
    exports.refreshUserInfo = refreshUserInfo;
    exports.resetState = resetState;
    exports.retrieveAllPlayerData = retrieveAllPlayerData;
    exports.retrievePlayerData = retrievePlayerData;
    exports.showAd = showAd;

    Object.defineProperty(exports, '__esModule', { value: true });

}));
//# sourceMappingURL=ad-sdk.umd.js.map
