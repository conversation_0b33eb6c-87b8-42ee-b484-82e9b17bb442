# @anyigame/ad-sdk

[![npm version](https://badge.fury.io/js/@anyigame%2Fad-sdk.svg)](https://badge.fury.io/js/@anyigame%2Fad-sdk)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![TypeScript](https://img.shields.io/badge/TypeScript-Ready-blue.svg)](https://www.typescriptlang.org/)

高可扩展、高可维护的 H5 游戏 SDK，专为 **webview 环境** 设计。此 SDK 桥接 H5 游戏与原生移动应用，提供广告展示、用户管理和事件上报等核心功能。

## ⚠️ 重要提示

**此 SDK 专为 webview 环境设计。** 需要原生移动应用容器（Android/iOS）才能正常工作。SDK 在没有原生桥接接口的标准网页浏览器中无法运行。

## ✨ 特性

- 🖼️ **Webview 优先设计**：专为 webview 环境构建，与原生应用深度集成
- 🏗️ **适配器模式**：轻松适配不同的原生应用环境
- 📱 **跨平台支持**：同时支持 Android 和 iOS 原生环境
- 🔄 **异步初始化**：异步获取用户信息并智能缓存
- 📊 **事件上报**：全面追踪用户行为和交互
- 🎯 **TypeScript 支持**：完整的 TypeScript 支持和类型定义
- 📚 **完善文档**：所有 API 都有详细的 JSDoc 注释
- 🧪 **生产就绪**：经过充分测试的生产级代码
- 🌐 **CDN 支持**：支持单文件 CDN 部署，无需 npm 安装
- 🖼️ **跨 iframe 支持**：在顶层窗口初始化 SDK，在 iframe 中使用，解决回调隔离问题

## 🚀 快速开始

### 安装

#### 方式 1：NPM 安装

```bash
npm install @anyigame/ad-sdk
```

#### 方式 2：CDN 集成

```html
<!-- 生产环境：使用压缩版本 -->
<script src="https://unpkg.com/@anyigame/ad-sdk@latest/dist/ad-sdk.umd.min.js"></script>

<!-- jsDelivr CDN -->
<script src="https://cdn.jsdelivr.net/npm/@anyigame/ad-sdk@latest/dist/ad-sdk.umd.min.js"></script>

<!-- 推荐：锁定特定版本 -->
<script src="https://unpkg.com/@anyigame/ad-sdk@1.0.1/dist/ad-sdk.umd.min.js"></script>
```

#### 可用的 CDN 文件

| 文件 | 大小 | 说明 |
|------|------|-------------|
| `ad-sdk.umd.js` | 46.89 KB | 未压缩版本 |
| `ad-sdk.umd.min.js` | 17.87 KB | **生产环境推荐** |
| Gzipped | ~5.94 KB | 压缩后大小 |

### 基础用法

#### NPM 集成

```typescript
import { init, showAd, getUserInfo, AdCloseType } from '@anyigame/ad-sdk';

// 初始化 SDK
await init({
  appid: '1001',
  channel: '1',
  debug: true
});

// 获取用户信息
const userInfo = getUserInfo();
console.log('用户信息:', userInfo);

// 显示广告
await showAd((type) => {
  if (type === AdCloseType.COMPLETED) {
    console.log('用户观看完成，发放奖励');
    // 在这里处理奖励逻辑
  } else {
    console.log('用户取消观看');
  }
});
```

#### CDN 集成

```html
<!DOCTYPE html>
<html>
<head>
    <title>您的游戏</title>
</head>
<body>
    <!-- 引入 SDK -->
    <script src="https://unpkg.com/@anyigame/ad-sdk@1.0.1/dist/ad-sdk.umd.min.js"></script>

    <script>
        // SDK 作为全局变量 AdSDK 可用
        async function initGame() {
            // 初始化 SDK
            await AdSDK.init({
                appid: '1001',
                channel: '1',
                debug: true
            });

            // 获取用户信息
            const userInfo = AdSDK.getUserInfo();
            console.log('用户信息:', userInfo);

            // 显示广告
            await AdSDK.showAd((type) => {
                if (type === AdSDK.AdCloseType.COMPLETED) {
                    console.log('用户观看完成，发放奖励');
                } else {
                    console.log('用户取消观看');
                }
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initGame);
    </script>
</body>
</html>
```

## 📖 API 文档

### 配置

#### `init(config: SDKConfig): Promise<void>`

使用提供的配置初始化 SDK。

```typescript
interface SDKConfig {
  /** 应用/游戏标识符（数字字符串） */
  appid: string;
  /** 渠道代码（数字字符串） */
  channel: string;
  /** 启用调试模式 */
  debug?: boolean;
  /** API 请求超时时间，毫秒（默认：10000） */
  timeout?: number;
  /** 事件上报重试次数（默认：3） */
  maxRetries?: number;
  /** 事件批次大小（默认：10，最大：10） */
  batchSize?: number;
  /** 事件上报间隔时间，毫秒（默认：5000） */
  reportInterval?: number;
}
```

**示例：**
```typescript
await init({
  appid: '1001',
  channel: '1',
  debug: true,
  timeout: 15000
});
```

### 用户管理

#### `getUserInfo(): User | null`

同步获取缓存的用户信息。如果用户信息不存在或已过期，返回 `null`。

```typescript
interface User {
  id: string;
  name: string;
  avatar?: string;
  // 其他用户属性
}
```

#### `refreshUserInfo(): Promise<User>`

异步刷新用户信息，清除缓存并从 API 获取。

### 广告

#### `showAd(callback?: AdCloseCallback): Promise<void>`

显示广告，可选择提供关闭回调。

```typescript
type AdCloseCallback = (type: AdCloseType) => void;

enum AdCloseType {
  /** 用户观看完成 */
  COMPLETED = 1,
  /** 用户取消观看 */
  CANCELLED = 2,
}
```

**示例：**
```typescript
await showAd((type) => {
  if (type === AdCloseType.COMPLETED) {
    // 给用户发放奖励
    console.log('奖励已发放！');
  }
});
```

#### `canShowAd(): boolean`

检查当前是否可以显示广告。

### 工具方法

#### `getState(): string`

获取当前 SDK 状态。

#### `getVersion(): string`

获取 SDK 版本号。

#### `getDebugInfo(): object`

获取详细的调试信息，包括 SDK 状态、配置和模块状态。

#### `destroy(): void`

销毁 SDK 并清理所有资源。

### 跨 iframe 功能

#### `initInTopWindow(config: SDKConfig, adapter?: NativeBridgeAdapter): Promise<void>`

在顶层窗口初始化 SDK，用于跨 iframe 场景。

**示例：**
```typescript
import { initInTopWindow } from '@anyigame/ad-sdk';

// 在父页面中初始化 SDK
await initInTopWindow({
  appid: '1001',
  channel: '1',
  debug: true
});
```

#### `createIframeSDKProxy(): CrossIframeSDKProxy`

创建 iframe SDK 代理实例，用于在 iframe 中访问顶层窗口的 SDK。

**返回值：** `CrossIframeSDKProxy` 对象，包含以下方法：

- `isAvailable(): boolean` - 检查 SDK 是否可用
- `showAd(callback?: AdCloseCallback): Promise<void>` - 显示广告
- `canShowAd(): boolean` - 检查是否可以显示广告
- `getUserInfo(): User | null` - 获取用户信息
- `refreshUserInfo(): Promise<User>` - 刷新用户信息
- `getGameConfig(): Promise<GameConfig>` - 获取游戏配置
- `getState(): string` - 获取 SDK 状态
- `getVersion(): string` - 获取 SDK 版本
- `getDebugInfo(): object` - 获取调试信息

**示例：**
```typescript
import { createIframeSDKProxy, AdCloseType } from '@anyigame/ad-sdk';

// 在 iframe 中创建代理
const sdkProxy = createIframeSDKProxy();

if (sdkProxy.isAvailable()) {
  // 显示广告
  await sdkProxy.showAd((type) => {
    if (type === AdCloseType.COMPLETED) {
      console.log('用户观看完成');
    }
  });

  // 获取用户信息
  const userInfo = sdkProxy.getUserInfo();
}
```

#### `isSDKInitializedInTopWindow(): boolean`

检查顶层窗口是否已初始化 SDK。

#### `cleanupCrossIframeSDK(): void`

清理跨 iframe SDK 资源。

## 🖼️ Webview 环境要求

### 原生应用要求

此 SDK 需要在 webview 环境中提供特定的原生桥接接口：

#### Android 要求

原生 Android 应用必须向 webview 注入 `DsmJSInterface` 对象：

```javascript
// 必需的原生桥接接口
window.DsmJSInterface = {
  showAd: function() {
    // 显示广告的原生实现
  }
};
```

#### iOS 要求

原生 iOS 应用必须注册 `showAd` 消息处理器：

```javascript
// 必需的原生桥接接口
window.webkit.messageHandlers.showAd = {
  postMessage: function(data) {
    // 显示广告的原生实现
  }
};
```

#### 广告关闭回调

原生应用在广告关闭时必须调用 H5 的 `closeAd` 方法：

```javascript
// Android
webView.evaluateJavascript("closeAd(1)"); // 1=观看完成, 2=取消观看

// iOS
webView.evaluateJavaScript("closeAd(2)");
```

## 🖼️ 跨 iframe 支持

当您的游戏在 iframe 内运行时，浏览器安全限制可能会阻止原生回调（如 `window.closeAd`）正常工作。SDK 提供了跨 iframe 解决方案。

### 基本用法

#### 1. 在顶层窗口（父页面）初始化 SDK

```typescript
import { initInTopWindow } from '@anyigame/ad-sdk';

// 在父页面中初始化 SDK
await initInTopWindow({
  appid: '1001',
  channel: '1',
  debug: true
});
```

#### 2. 在 iframe（游戏页面）中使用 SDK

```typescript
import { createIframeSDKProxy, AdCloseType } from '@anyigame/ad-sdk';

// 创建 SDK 代理实例
const sdkProxy = createIframeSDKProxy();

if (sdkProxy.isAvailable()) {
  // 显示广告
  await sdkProxy.showAd((type) => {
    if (type === AdCloseType.COMPLETED) {
      console.log('用户观看完成，发放奖励');
    }
  });

  // 获取用户信息
  const userInfo = sdkProxy.getUserInfo();
}
```

### CDN 跨 iframe 集成

#### 父页面

```html
<!DOCTYPE html>
<html>
<head>
    <title>父页面</title>
</head>
<body>
    <script src="https://unpkg.com/@anyigame/ad-sdk@1.0.1/dist/ad-sdk.umd.min.js"></script>
    <script>
        // 在顶层窗口初始化 SDK
        window.AdSDK.initInTopWindow({
            appid: '1001',
            channel: '1',
            debug: true
        }).then(() => {
            console.log('SDK 初始化成功');
        });
    </script>

    <!-- 游戏 iframe -->
    <iframe src="game.html" width="800" height="600"></iframe>
</body>
</html>
```

#### iframe 游戏页面

```html
<!DOCTYPE html>
<html>
<head>
    <title>游戏页面</title>
</head>
<body>
    <button id="showAdBtn">显示广告</button>

    <script>
        // 访问父窗口的 SDK
        const AdSDK = window.parent.AdSDK;

        // 创建 SDK 代理
        const sdkProxy = AdSDK.createIframeSDKProxy();

        document.getElementById('showAdBtn').addEventListener('click', async () => {
            if (sdkProxy.isAvailable()) {
                await sdkProxy.showAd((type) => {
                    if (type === AdSDK.AdCloseType.COMPLETED) {
                        console.log('用户观看完成');
                    }
                });
            }
        });
    </script>
</body>
</html>
```

### 跨 iframe API 参考

#### 顶层窗口 API

- `initInTopWindow(config, adapter?)` - 在顶层窗口初始化 SDK
- `isSDKInitializedInTopWindow()` - 检查 SDK 是否已初始化
- `cleanupCrossIframeSDK()` - 清理跨 iframe SDK 资源

#### iframe 代理 API

- `createIframeSDKProxy()` - 创建 SDK 代理实例
- `sdkProxy.isAvailable()` - 检查 SDK 是否可用
- `sdkProxy.showAd(callback)` - 显示广告
- `sdkProxy.canShowAd()` - 检查是否可以显示广告
- `sdkProxy.getUserInfo()` - 获取用户信息
- `sdkProxy.refreshUserInfo()` - 刷新用户信息
- `sdkProxy.getGameConfig()` - 获取游戏配置
- `sdkProxy.getState()` - 获取 SDK 状态
- `sdkProxy.getVersion()` - 获取 SDK 版本
- `sdkProxy.getDebugInfo()` - 获取调试信息

### 重要注意事项

1. **初始化顺序**：必须先在顶层窗口初始化 SDK，然后才能在 iframe 中使用
2. **跨域限制**：如果 iframe 和父页面在不同域名下，某些功能可能受限
3. **错误处理**：为所有 SDK 调用添加适当的错误处理
4. **资源清理**：在页面卸载时调用 `cleanupCrossIframeSDK()` 清理资源

### 常见问题解决

#### 问题：在生产环境中访问不到 `sdkProxy.getUserInfo()` 等方法

**现象**：在开发者工具中只能看到 `sdkProxy.topWindowSDK` 属性，无法访问其他方法。

**原因**：JavaScript 引擎优化可能导致方法在调试器中不可见。

**解决方案**：
1. 确保使用最新版本的 SDK（已修复此问题）
2. 验证方法确实存在：

```javascript
// 检查方法是否存在
console.log('getUserInfo 方法存在:', typeof sdkProxy.getUserInfo === 'function');

// 直接调用方法
const userInfo = sdkProxy.getUserInfo();
console.log('用户信息:', userInfo);

// 检查所有可用方法
console.log('可用方法:', Object.getOwnPropertyNames(sdkProxy.__proto__));
```

3. 如果仍有问题，可以通过原型链访问：

```javascript
// 备用访问方式
const userInfo = Object.getPrototypeOf(sdkProxy).getUserInfo.call(sdkProxy);
```

## 🎮 使用场景

### 场景 1：基础游戏集成

```typescript
class GameManager {
    private sdkReady = false;

    async initialize() {
        try {
            await init({
                appid: '1001',
                channel: '1',
                debug: false
            });
            this.sdkReady = true;
            console.log('游戏 SDK 就绪');
        } catch (error) {
            console.error('SDK 初始化失败:', error);
        }
    }

    async showRewardedAd() {
        if (!this.sdkReady || !canShowAd()) {
            console.log('广告不可用');
            return false;
        }

        return new Promise((resolve) => {
            showAd((type) => {
                if (type === AdCloseType.COMPLETED) {
                    this.grantReward();
                    resolve(true);
                } else {
                    resolve(false);
                }
            });
        });
    }

    private grantReward() {
        // 实现您的奖励逻辑
        console.log('奖励已发放给玩家');
    }
}
```

### 场景 2：Cocos Creator 集成

```typescript
// 在您的 Cocos Creator 游戏中
import { Component, _decorator } from 'cc';
import { init, showAd, AdCloseType } from '@anyigame/ad-sdk';

const { ccclass, property } = _decorator;

@ccclass('AdManager')
export class AdManager extends Component {
    private sdkInitialized = false;

    async onLoad() {
        try {
            await init({
                appid: '1001',
                channel: '1',
                debug: true
            });
            this.sdkInitialized = true;
        } catch (error) {
            console.error('SDK 初始化失败:', error);
        }
    }

    async showRewardAd(): Promise<boolean> {
        if (!this.sdkInitialized) {
            return false;
        }

        return new Promise((resolve) => {
            showAd((type) => {
                const completed = type === AdCloseType.COMPLETED;
                if (completed) {
                    // 发放游戏内奖励
                    this.grantCoins(100);
                }
                resolve(completed);
            });
        });
    }

    private grantCoins(amount: number) {
        // 您的游戏金币发放逻辑
        console.log(`发放了 ${amount} 金币`);
    }
}
```

### 场景 3：错误处理最佳实践

```typescript
class RobustAdManager {
    private maxRetries = 3;
    private retryDelay = 1000;

    async initializeWithRetry() {
        for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
            try {
                await init({
                    appid: '1001',
                    channel: '1',
                    timeout: 15000
                });
                console.log('SDK 初始化成功');
                return;
            } catch (error) {
                console.error(`第 ${attempt} 次初始化尝试失败:`, error);

                if (attempt < this.maxRetries) {
                    await this.delay(this.retryDelay * attempt);
                } else {
                    throw new Error('所有重试后 SDK 初始化仍然失败');
                }
            }
        }
    }

    async showAdWithFallback(): Promise<boolean> {
        try {
            if (!canShowAd()) {
                console.log('广告不可用，显示备用内容');
                this.showFallbackContent();
                return false;
            }

            return await this.showAdWithTimeout(10000);
        } catch (error) {
            console.error('广告显示失败:', error);
            this.showFallbackContent();
            return false;
        }
    }

    private showAdWithTimeout(timeout: number): Promise<boolean> {
        return Promise.race([
            new Promise<boolean>((resolve) => {
                showAd((type) => {
                    resolve(type === AdCloseType.COMPLETED);
                });
            }),
            new Promise<boolean>((_, reject) => {
                setTimeout(() => reject(new Error('广告超时')), timeout);
            })
        ]);
    }

    private showFallbackContent() {
        // 广告失败时显示替代内容
        console.log('显示备用内容');
    }

    private delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}
```

## 🔧 开发

### 环境要求

- Node.js >= 16
- Bun >= 1.0（推荐）或 npm >= 8

### 开发命令

```bash
# 安装依赖
bun install

# 开发模式
bun run dev

# 构建所有格式
bun run build

# 类型检查
bun run type-check

# 运行测试
bun test

# 代码格式化
bun run format

# 代码检查
bun run lint
```

### 项目结构

```
src/
├── adapters/           # 原生桥接适配器
│   ├── NativeBridgeAdapter.ts
│   └── DsmAdapter.ts
├── core/              # 核心 SDK 逻辑
│   └── SDK.ts
├── modules/           # 功能模块
│   ├── AdModule.ts
│   ├── UserModule.ts
│   └── EventModule.ts
├── utils/             # 工具函数
│   ├── storage.ts
│   └── CrossIframeSDK.ts
├── types/             # 类型定义
│   └── index.ts
├── api/               # API 客户端（自动生成）
└── index.ts           # 主入口点
```

## 🌐 浏览器兼容性

SDK 兼容：

- **现代浏览器**：Chrome、Firefox、Safari、Edge（ES2015+）
- **移动 Webview**：iOS Safari、Chrome Mobile、Android WebView
- **旧版支持**：Internet Explorer 11+（需要 polyfills）

### IE11 所需的 Polyfills

```html
<!-- IE11 支持 -->
<script src="https://polyfill.io/v3/polyfill.min.js?features=Promise,fetch"></script>
```

## 🏗️ 架构

### 适配器模式

SDK 使用适配器模式通过 `NativeBridgeAdapter` 接口抽象原生交互逻辑：

```typescript
interface NativeBridgeAdapter {
  detectNativeEnvironment(): NativeEnvironment;
  showAd(callback: AdCloseCallback): void;
  setAdCloseCallback(callback: AdCloseCallback): void;
  cleanup(): void;
}
```

### 模块化设计

- **Core**：SDK 核心逻辑和初始化
- **Adapter**：适配器接口和实现
- **AdModule**：广告显示模块
- **UserModule**：用户信息管理模块
- **EventModule**：事件上报模块

### 自定义适配器

要适配新的原生环境，请实现 `NativeBridgeAdapter` 接口：

```typescript
import { BaseNativeBridgeAdapter, AdCloseCallback, NativeEnvironment } from '@anyigame/ad-sdk';

class CustomAdapter extends BaseNativeBridgeAdapter {
  detectNativeEnvironment(): NativeEnvironment {
    // 实现环境检测逻辑
    return {
      isAndroid: false,
      isIOS: true,
      hasNativeSupport: true
    };
  }

  showAd(callback: AdCloseCallback): void {
    // 实现广告显示逻辑
    // 调用您的自定义原生桥接
    window.yourCustomBridge.showAd();
  }
}

// 使用自定义适配器
await init(config, new CustomAdapter());
```

## 🧪 测试

### 运行测试

```bash
# 运行所有测试
bun test

# 监视模式运行测试
bun run test:watch

# 运行测试并生成覆盖率报告
bun run test:coverage
```

### 生产环境测试

由于此 SDK 需要原生 webview 环境，测试应在实际移动应用中进行。SDK 包含调试模式用于开发测试：

```typescript
await init({
  appid: '1001',
  channel: '1',
  debug: true  // 启用详细日志
});

// 检查 SDK 状态
console.log('SDK 调试信息:', getDebugInfo());
```

## 🤝 贡献

我们欢迎贡献！请遵循以下指南：

1. **Fork 仓库** 并创建您的功能分支
2. **为新功能编写测试**
3. **遵循现有代码风格**（使用 `bun run format` 和 `bun run lint`）
4. **更新文档** 以反映任何 API 更改
5. **提交 pull request** 并提供清晰的描述

### 开发设置

```bash
# 克隆仓库
git clone https://github.com/anyigame/ad-sdk.git
cd ad-sdk

# 安装依赖
bun install

# 运行测试
bun test

# 开始开发
bun run dev
```

## 📄 许可证

MIT License

Copyright (c) 2024 anyigame

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

---

## 📞 支持

- **问题反馈**：[GitHub Issues](https://github.com/anyigame/ad-sdk/issues)
- **文档**：此 README 和内联 JSDoc 注释
- **版本**：1.0.1

如有集成或使用问题，请创建 issue 并提供您的环境和使用场景的详细信息。
